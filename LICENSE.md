# Pay Mule License

## Copyright Notice

Copyright (C) 2025 Pay Mule. All rights reserved.

## Application License

Pay Mule is a proprietary mobile payment application designed for the Zambian market.

### Terms of Use

1. **Ownership**: Pay Mule and all associated intellectual property rights are owned by Pay Mule.

2. **Permitted Use**: This application is licensed for use in Zambia for mobile money transactions, utility payments, and financial services.

3. **Restrictions**: 
   - No reverse engineering, decompilation, or disassembly
   - No redistribution without explicit permission
   - Commercial use limited to authorized partners

4. **Compliance**: This application complies with:
   - Bank of Zambia Financial Services Act 2022
   - Zambia Data Protection Act 2021
   - PCI-DSS Level 1 requirements

5. **Data Protection**: User data is protected according to Zambian data protection laws and international security standards.

### Third-Party Licenses

This application includes third-party components with their respective licenses:
- Flutter Framework (BSD 3-Clause License)
- Various Dart packages (see pubspec.yaml for details)

### Contact

For licensing inquiries, contact: <EMAIL>

---

**Pay Mule** - Empowering Financial Inclusion in Zambia
