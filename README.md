# Zambia Pay - Financial Inclusion for Zambia

A comprehensive Flutter application addressing financial exclusion in Zambia through offline-first architecture, mobile money integration, and utility management.

## 🇿🇲 Problem Statement

Zambia faces significant financial challenges:
- **79.3% unbanked population** - Limited access to traditional banking
- **Utility fragmentation** - Scattered payment systems for ZESCO, water, internet
- **Poor rural connectivity** - Unreliable internet access in remote areas
- **Informal market friction** - Difficulty in digital transactions for small businesses

## 🎯 Solution Overview

Zambia Pay provides a unified platform that:

### Core Features
- **Offline-First Architecture** - Works without internet, syncs when connected
- **Multi-Provider Mobile Money** - MTN, Airtel, Zamtel integration with automatic fallback
- **Unified Utility Payments** - ZESCO electricity, water bills, internet payments
- **PCI-DSS Level 1 Compliance** - Bank of Zambia regulatory compliance
- **Biometric Security** - Fingerprint, face, and iris authentication
- **Local Language Support** - English, Chichewa/Nyanja, Bemba, Tonga, Lozi

### Technical Architecture
- **Database**: SQLite with AES-256 encryption
- **Sync**: VeryPay-style background sync (max 3 retries, 15-min intervals)
- **Network**: Auto-detect connectivity via `connectivity_plus`
- **Security**: FIPS-140-2 encryption standard, PBKDF2 key derivation

## 🏗️ Architecture

```
lib/
├── core/
│   ├── config/           # App configuration and settings
│   ├── constants/        # Application constants
│   └── security/         # Encryption, biometric, compliance services
├── data/
│   └── database/         # SQLite database helper with encryption
├── features/
│   ├── mobile_money/     # MTN, Airtel, Zamtel API integration
│   ├── offline_sync/     # Background sync and queue management
│   ├── utilities/        # ZESCO and utility payment services
│   ├── auth/             # Authentication and user management
│   ├── dashboard/        # Main dashboard UI
│   └── transactions/     # Transaction management
├── presentation/         # UI screens and widgets
└── shared/              # Shared utilities and helpers
```

## 🚀 Getting Started

### Prerequisites
- Flutter SDK 3.0.0 or higher
- Dart SDK 3.0.0 or higher
- Android Studio / VS Code with Flutter extensions
- Android device/emulator or iOS device/simulator

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/zambia-pay.git
   cd zambia-pay
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate model files**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the application**
   ```bash
   flutter run
   ```

### Configuration

#### Mobile Money API Setup
Update the credentials in `lib/main.dart`:

```dart
// MTN Mobile Money
await MobileMoneyService().initialize(
  mtnCredentials: {
    'apiKey': 'YOUR_MTN_API_KEY',
    'subscriptionKey': 'YOUR_MTN_SUBSCRIPTION_KEY',
  },
  // ... other providers
);
```

#### ZESCO API Setup
```dart
await UtilityService().initialize(
  zescoCredentials: {
    'apiKey': 'YOUR_ZESCO_API_KEY',
    'merchantId': 'YOUR_ZESCO_MERCHANT_ID',
  },
);
```

## 📱 Features

### Mobile Money Integration
- **MTN Mobile Money** - Send/receive money, bill payments
- **Airtel Money** - Cross-network transactions
- **Zamtel Kwacha** - Complete mobile money services
- **Automatic Provider Detection** - Based on phone number format
- **Fallback Mechanism** - Tries alternative providers if primary fails

### Offline Capabilities
- **Transaction Queuing** - Store transactions when offline
- **Background Sync** - Automatic sync when connection restored
- **Exponential Backoff** - Smart retry mechanism (1, 2, 4, 8, 15 min intervals)
- **Data Integrity** - Encrypted local storage with checksums

### Utility Payments
- **ZESCO Electricity** - Bill inquiry and payment
- **Water Bills** - LWSC and NWASCO integration
- **Internet/TV** - Various provider support
- **Auto-Pay** - Scheduled automatic payments

### Security Features
- **AES-256-GCM Encryption** - All sensitive data encrypted
- **Biometric Authentication** - Fingerprint, face, iris support
- **PCI-DSS Level 1 Compliance** - Bank of Zambia requirements
- **Audit Logging** - Complete transaction audit trail
- **Data Retention** - 7-year compliance with automatic cleanup

## 🔧 Technical Implementation

### Database Schema
```sql
-- Encrypted SQLite with key tables:
- users (encrypted PII)
- transactions (with offline queue)
- utility_bills (ZESCO, water, etc.)
- audit_logs (compliance tracking)
- offline_queue (sync management)
```

### Mobile Money API Integration
```dart
// Zambian phone number formatting
final mtnConfig = {
  "baseUrl": "https://momodeveloper.mtn.com/v1",
  "currency": "ZMW",
  "partyIdType": "MSISDN", // Format: "26096xxxxxxx"
  "txFee": 0.0021, // Zambia's 2025 Mobile Money Levy
  "fallbackProviders": [AirtelMoney, ZamtelKwacha]
};
```

### Offline Sync Implementation
```dart
// VeryPay-style background sync
class OfflineSyncManager {
  static const int maxRetryAttempts = 3;
  static const int syncIntervalMinutes = 15;

  // Exponential backoff: 1, 2, 4, 8, 15 minutes
  int calculateRetryDelay(int retryCount) {
    return (1 << (retryCount - 1)).clamp(1, 15);
  }
}
```

## 🛡️ Security & Compliance

### Encryption Standards
- **Algorithm**: AES-256-GCM
- **Key Derivation**: PBKDF2 with 100,000 iterations
- **Storage**: Flutter Secure Storage with hardware-backed keys
- **Database**: SQLCipher for encrypted SQLite

### Bank of Zambia Compliance
- **Data Retention**: 7 years (2,555 days)
- **Transaction Limits**: K50,000 daily, K500,000 monthly
- **AML/KYC**: Automated monitoring and flagging
- **Audit Trail**: Complete transaction history with timestamps

### Biometric Security
```dart
// Supported biometric types
const supportedBiometrics = [
  'fingerprint',
  'face',
  'iris'
];

// Authentication timeout
const biometricTimeoutSeconds = 30;
```

## 🌍 Localization

Supported languages:
- **English** (en) - Primary
- **Chichewa/Nyanja** (ny) - Central/Eastern regions
- **Bemba** (bem) - Northern regions
- **Tonga** (ton) - Southern regions
- **Lozi** (loz) - Western regions

## 📊 Performance

### Offline Capabilities
- **Queue Size**: Up to 1,000 transactions
- **Sync Batch**: 50 transactions per batch
- **Retry Logic**: 3 attempts with exponential backoff
- **Data Compression**: Encrypted JSON with compression

### Network Optimization
- **Connection Timeout**: 30 seconds
- **Retry Intervals**: 15 minutes background sync
- **Bandwidth Usage**: Optimized for 2G/3G networks
- **Offline Detection**: Real-time connectivity monitoring

## 🧪 Testing

### Run Tests
```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Widget tests
flutter test test/widget_test.dart
```

### Test Coverage
- **Unit Tests**: Core business logic
- **Integration Tests**: API integrations
- **Widget Tests**: UI components
- **Security Tests**: Encryption and authentication

## 📈 Monitoring & Analytics

### Compliance Monitoring
- **Transaction Risk Scoring** - Real-time AML detection
- **Violation Tracking** - Automated compliance reporting
- **Audit Reports** - Bank of Zambia regulatory reports

### Performance Metrics
- **Sync Success Rate** - Offline transaction processing
- **API Response Times** - Mobile money provider performance
- **Error Rates** - Transaction failure analysis

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow Flutter/Dart style guide
- Add tests for new features
- Update documentation
- Ensure security compliance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: [docs.zambiapay.com](https://docs.zambiapay.com)
- **Issues**: [GitHub Issues](https://github.com/your-org/zambia-pay/issues)

## 🙏 Acknowledgments

- **Bank of Zambia** - Regulatory guidance and compliance requirements
- **MTN Zambia** - Mobile money API integration
- **Airtel Zambia** - Cross-network payment solutions
- **Zamtel** - Telecommunications infrastructure
- **ZESCO** - Electricity payment integration

---

**Built with ❤️ for financial inclusion in Zambia**
