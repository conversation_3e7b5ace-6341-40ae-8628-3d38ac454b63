# 🇿🇲 Zambia Pay Safety Override System

## ⚠️ **Safety Overrides Implementation**

The Safety Override System provides automatic failure recovery with comprehensive data preservation and developer notifications. This system ensures that critical failures are handled gracefully while maintaining data integrity and system stability.

## 🚨 **Automatic Failure Response Protocol**

```bash
IF ANY FAILURE OCCURS:
1. Auto-revert to last stable commit
2. Preserve error logs in /crash_reports
3. Send push notification to developer
4. Generate fix suggestion report

RECOVERY COMMAND:
```bash
./safety_override.sh --restore-point=paymule_stable_v2.1
```

## 🚀 Quick Start

### Prerequisites
- Git repository with tagged stable versions
- Write access to crash reports directory
- Network connectivity for notifications (optional)
- Backup storage space for user data

### Linux/macOS
```bash
# Make script executable
chmod +x safety_override.sh

# Basic recovery to latest stable
./safety_override.sh --restore-point=paymule_stable_v2.1

# Recovery with full notifications
./safety_override.sh \
  --restore-point=paymule_stable_v2.1 \
  --developer-webhook=https://hooks.slack.com/services/... \
  --email=<EMAIL>
```

### Windows (PowerShell)
```powershell
# Basic recovery to latest stable
.\safety_override.ps1 -RestorePoint "paymule_stable_v2.1"

# Recovery with notifications
.\safety_override.ps1 -RestorePoint "paymule_stable_v2.1" -SlackWebhook "https://hooks.slack.com/services/..."
```

## 🔍 **Failure Detection System**

### Critical Services Monitored
1. **Database Service**
   - SQLite database connectivity
   - Database file integrity
   - Lock file detection
   - Disk space availability

2. **API Service**
   - Health endpoint responsiveness
   - Port availability
   - Network connectivity
   - Service process status

3. **Payment Processor**
   - Mobile money API connectivity
   - Transaction queue status
   - Provider authentication
   - Rate limiting compliance

4. **Notification Service**
   - SMS gateway connectivity
   - Push notification service
   - Queue processing status
   - Configuration validity

### Application Health Checks
- **Core Files**: main.dart, pubspec.yaml existence
- **Directory Structure**: lib/features, lib/core presence
- **Configuration**: Valid app configuration files
- **Dependencies**: Flutter environment status

### System Health Monitoring
- **Disk Space**: Fails if usage > 95%
- **Memory Usage**: Monitors available RAM
- **File Permissions**: Checks critical file access
- **Network Connectivity**: Tests external service access

## 📋 **Automatic Recovery Actions**

### 1. Error Log Preservation
**Location**: `/crash_reports/crash_report_TIMESTAMP.log`

**Contents**:
- System information (OS, Flutter version, Git status)
- Service status for all critical components
- Recent application logs (last 50 lines)
- Flutter-specific logs and errors
- System logs and resource usage

**Additional Preservation**:
- Complete logs directory backup
- Configuration file snapshots
- Database state capture

### 2. User Data Backup
**Location**: `/backups/user_data_backup_TIMESTAMP/`

**Protected Data**:
- User profiles and authentication data
- Transaction history and receipts
- Offline transaction queue
- Chilimba group information
- App configuration and preferences
- Database backup with integrity verification

**Backup Manifest**:
- Timestamp and failure context
- List of backed up files
- Data integrity checksums
- Recovery instructions

### 3. Developer Notifications

#### Slack Integration
```json
{
  "text": "🚨 Zambia Pay Safety Override Activated",
  "attachments": [
    {
      "color": "danger",
      "fields": [
        {"title": "Time", "value": "2025-01-29 14:30:00", "short": true},
        {"title": "Restore Point", "value": "paymule_stable_v2.1", "short": true},
        {"title": "Failure Count", "value": "3", "short": true},
        {"title": "Services Affected", "value": "database, api", "short": true}
      ]
    }
  ]
}
```

#### Webhook Notifications
```json
{
  "message": "Critical failure detected in Zambia Pay",
  "severity": "critical",
  "service": "zambia_pay",
  "timestamp": "2025-01-29T14:30:00Z",
  "restore_point": "paymule_stable_v2.1",
  "failure_count": 3,
  "affected_services": ["database", "api"]
}
```

#### Email Alerts
- **Subject**: 🚨 Zambia Pay Safety Override Activated
- **Content**: Detailed failure analysis and recovery status
- **Attachments**: Crash report and fix suggestions
- **Recipients**: Configurable development team list

### 4. Automatic Git Revert

#### Restore Point Detection
1. **Explicit**: User-specified commit/tag
2. **Stable Tags**: Latest `paymule_stable_*` tag
3. **Release Tags**: Latest `v*` tag
4. **Branch Fallback**: main/master branch

#### Revert Process
```bash
# Create emergency backup branch
git branch emergency_backup_TIMESTAMP

# Revert to stable commit
git checkout paymule_stable_v2.1

# Rebuild application
flutter clean && flutter pub get
```

#### Post-Revert Actions
- Application rebuild and dependency resolution
- Service restart and health verification
- Configuration validation
- Database migration if needed

## 🔧 **Fix Suggestion Generation**

### Automated Analysis
**File**: `/crash_reports/fix_suggestions_TIMESTAMP.md`

**Service-Specific Suggestions**:

#### Database Failures
- Check database integrity with `PRAGMA integrity_check`
- Verify disk space and file permissions
- Remove lock files and restart services
- Restore from backup if corrupted

#### API Failures
- Verify service process status
- Check port availability and conflicts
- Test network connectivity
- Validate configuration files

#### Payment Processor Issues
- Check mobile money provider status
- Verify API credentials and authentication
- Test network connectivity to providers
- Review rate limiting and queue status

#### Notification Service Problems
- Test SMS gateway connectivity
- Verify push notification certificates
- Check notification queue processing
- Review service configuration

### Recovery Recommendations
1. **Immediate Actions**: Resource checks, recent changes review
2. **Recovery Options**: Quick fix, manual investigation, emergency rollback
3. **Post-Recovery**: Validation testing, monitoring setup
4. **Escalation**: Contact information and procedures

## 📊 **Recovery Verification**

### Health Check Sequence
1. **Service Status**: Re-verify all critical services
2. **Application Health**: Confirm core functionality
3. **Database Integrity**: Validate data consistency
4. **File System**: Check disk space and permissions

### Success Criteria
- All critical services responding
- Application health checks passing
- Database integrity verified
- User data accessible and consistent

### Recovery Report
**File**: `/crash_reports/recovery_report_TIMESTAMP.md`

**Contents**:
- Recovery duration and actions taken
- Service status after recovery
- Generated files and locations
- Next steps and recommendations
- System information post-recovery

## 🔄 **Integration with Existing Systems**

### Validation Suite Integration
```bash
# Run safety override then validate
./safety_override.sh --restore-point=paymule_stable_v2.1
./zambia_validation_suite.sh --critical-modules="momo,offline,notifications"
```

### Live Testing Integration
```bash
# Recover and test on physical device
./safety_override.sh --restore-point=paymule_stable_v2.1
./live_zambia_test.sh --user-phone=+260961234567 --scenarios="market_payment"
```

### CI/CD Pipeline Integration
```yaml
# GitHub Actions example
- name: Safety Override on Failure
  if: failure()
  run: |
    ./safety_override.sh \
      --restore-point=paymule_stable_v2.1 \
      --developer-webhook=${{ secrets.SLACK_WEBHOOK }}
```

## 🛡️ **Security Considerations**

### Data Protection
- User data encrypted during backup
- Sensitive information redacted from logs
- Access controls on crash reports directory
- Secure transmission of notifications

### Access Control
- Script execution permissions
- Git repository access requirements
- Webhook URL security
- Email recipient validation

## 📈 **Monitoring and Alerting**

### Failure Patterns
- Track failure frequency and types
- Identify recurring issues
- Monitor recovery success rates
- Analyze system stability trends

### Performance Metrics
- Recovery time duration
- Data preservation success rate
- Notification delivery reliability
- System uptime after recovery

## 🔧 **Customization Options**

### Configuration Files
```bash
# Custom crash reports location
./safety_override.sh --crash-reports-dir=/var/log/zambia_pay/crashes

# Disable specific features
./safety_override.sh --no-auto-revert --no-notifications

# Custom notification endpoints
./safety_override.sh \
  --slack-webhook=https://hooks.slack.com/... \
  --developer-webhook=https://api.company.com/alerts \
  --email=<EMAIL>,<EMAIL>
```

### Environment Variables
```bash
export ZAMBIA_PAY_CRASH_DIR="/custom/crash/location"
export ZAMBIA_PAY_BACKUP_DIR="/custom/backup/location"
export ZAMBIA_PAY_SLACK_WEBHOOK="https://hooks.slack.com/..."
```

## 📞 **Emergency Procedures**

### Manual Override
```bash
# Force recovery without auto-detection
./safety_override.sh --restore-point=paymule_emergency_backup --no-auto-revert

# Preserve data only, no system changes
./safety_override.sh --preserve-user-data --no-auto-revert --no-notifications
```

### Escalation Contacts
1. **Technical Lead**: Immediate system issues
2. **DevOps Team**: Infrastructure and deployment
3. **Product Owner**: Business impact decisions
4. **Security Team**: Data breach or security concerns

---

**🇿🇲 The Safety Override System ensures Zambia Pay can automatically recover from critical failures while preserving user data and maintaining system integrity for continued service to rural Zambian communities.**
