# 🇿🇲 Zambia Pay Validation Suite

## Overview

The Zambia Pay Validation Suite is a comprehensive testing framework designed to ensure the stability and reliability of critical mobile money systems before deployment. It validates mobile money API stability, offline sync integrity, and notification delivery metrics.

## 🧪 **Validation Protocol (Before Each Commit)**

```bash
EXECUTE SAFELY:
1. Run Zambian regression suite
2. Validate mobile money API stability
3. Check offline sync integrity
4. Confirm notification delivery metrics

VALIDATION COMMAND:
```bash
./zambia_validation_suite.sh \
--critical-modules="momo,offline,notifications" \
--coverage-threshold=90% \
--max-failures=0 \
--report-file=validation_report.html
```

## 🚀 Quick Start

### Linux/macOS
```bash
# Make script executable
chmod +x zambia_validation_suite.sh

# Run basic validation
./zambia_validation_suite.sh

# Run with high coverage requirements
./zambia_validation_suite.sh --coverage-threshold=95% --verbose
```

### Windows (PowerShell)
```powershell
# Run basic validation
.\zambia_validation_suite.ps1

# Run with high coverage requirements
.\zambia_validation_suite.ps1 -CoverageThreshold 95 -Verbose
```

## 📋 Critical Modules

### 🏦 Mobile Money (momo)
- **MTN Mobile Money API**: Tests provider detection, transaction validation, and fee calculation
- **Airtel Money API**: Validates Airtel-specific functionality and integration
- **Zamtel Kwacha API**: Ensures Zamtel provider compatibility
- **Provider Detection**: Tests automatic provider identification from phone numbers
- **Transaction Validation**: Validates amount ranges and Zambian mobile money levy (0.21%)

### 📱 Offline Sync (offline)
- **Transaction Queue**: Tests offline transaction queuing and processing
- **Data Synchronization**: Validates sync of pending transactions when online
- **Conflict Resolution**: Tests handling of data conflicts during sync
- **Storage Encryption**: Ensures sensitive data is properly encrypted offline

### 🔔 Notifications (notifications)
- **SMS Notifications**: Tests SMS formatting and token generation for offline transactions
- **Push Notifications**: Validates transaction notification creation and delivery
- **Utility Alerts**: Tests ZESCO bill alerts and scheduling
- **Localization**: Ensures proper translation to Nyanja and other local languages

## 🎯 Validation Thresholds

| Metric | Default | Production |
|--------|---------|------------|
| Test Coverage | 90% | 95% |
| Max Failures | 0 | 0 |
| Success Rate | 100% | 100% |

## 📊 Example Usage

### Basic Validation
```bash
# Test all critical modules with default settings
./zambia_validation_suite.sh
```

### Production-Ready Validation
```bash
# Strict validation for production deployment
./zambia_validation_suite.sh \
  --critical-modules="momo,offline,notifications" \
  --coverage-threshold=95% \
  --max-failures=0 \
  --report-file=production_validation.html \
  --verbose
```

### Module-Specific Testing
```bash
# Test only mobile money functionality
./zambia_validation_suite.sh --critical-modules="momo"

# Test offline and notification systems
./zambia_validation_suite.sh --critical-modules="offline,notifications"
```

## 📈 Validation Report

The validation suite generates an HTML report containing:

- **Summary Metrics**: Total tests, pass/fail rates, coverage percentage
- **Module Results**: Detailed results for each critical module
- **Test Details**: Individual test results with pass/fail status
- **Coverage Analysis**: Code coverage breakdown
- **Recommendations**: Next steps based on validation results

## 🔧 Integration with CI/CD

### GitHub Actions
```yaml
name: Zambia Pay Validation
on: [push, pull_request]
jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - run: ./zambia_validation_suite.sh --coverage-threshold=90%
```

### Pre-commit Hook
```bash
#!/bin/bash
# .git/hooks/pre-commit
echo "🇿🇲 Running Zambia Pay validation..."
./zambia_validation_suite.sh --max-failures=0
if [ $? -ne 0 ]; then
    echo "❌ Validation failed - commit blocked"
    exit 1
fi
echo "✅ Validation passed - commit allowed"
```

## 🛠️ Troubleshooting

### Common Issues

1. **Flutter not found**
   ```bash
   # Ensure Flutter is in PATH
   export PATH="$PATH:/path/to/flutter/bin"
   ```

2. **Test failures**
   ```bash
   # Run with verbose output to see detailed errors
   ./zambia_validation_suite.sh --verbose
   ```

3. **Coverage below threshold**
   ```bash
   # Check which modules need more test coverage
   flutter test --coverage
   genhtml coverage/lcov.info -o coverage/html
   ```

## 🇿🇲 Zambian Context

This validation suite is specifically designed for Zambian mobile money systems:

- **Phone Number Formats**: Validates 260XX format for all providers
- **Mobile Money Levy**: Tests 0.21% transaction fee calculation
- **Local Languages**: Supports Nyanja, Bemba, and English
- **Rural Connectivity**: Tests offline functionality for poor network areas
- **Utility Integration**: Validates ZESCO, NWSC, and other utility providers

## 📞 Support

For issues or questions about the validation suite:

1. Check the generated HTML report for detailed error information
2. Run with `--verbose` flag for additional debugging output
3. Review individual test files in the `test/` directory
4. Ensure all dependencies are properly installed with `flutter pub get`

## 🔄 Continuous Improvement

The validation suite is continuously updated to reflect:
- New mobile money provider integrations
- Updated Zambian regulatory requirements
- Enhanced offline functionality
- Additional utility provider support
- Improved localization coverage

---

**Remember**: Always run the validation suite before deploying to production. Zero failures and high coverage are essential for maintaining the reliability of Zambia's mobile money infrastructure.
