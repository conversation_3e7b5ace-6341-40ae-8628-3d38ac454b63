# 🇿🇲 PAY MULE ZAMBIA - AGENT SERVICE SYSTEM

## OVERVIEW

The Zambian Agent Service System provides comprehensive management of Pay Mule agents across all 10 provinces of Zambia. The system integrates with the Zambia Central Registry for agent verification, performance tracking, and service coordination.

---

## 🏛️ ZAMBIA CENTRAL REGISTRY

### Core Implementation

<augment_code_snippet path="lib/services/agent_service.dart" mode="EXCERPT">
```dart
/// 🇿🇲 LOAD PRODUCTION AGENTS
/// Loads agents from Zambia Central Registry with province and rating filtering
void loadProductionAgents() {
  _logger.i('🏪 Loading production agents from Zambia Central Registry');

  final agents = ZambiaCentralRegistry.getAgents(
    provinces: [Province.Eastern, Province.Lusaka],
    minRating: 4.0
  );
  _cacheAgentData(agents);

  _logger.i('✅ Production agents loaded successfully');
}
```
</augment_code_snippet>

### Registry Features

- **Province-Based Filtering**: Filter agents by specific Zambian provinces
- **Rating Management**: Track and filter agents by performance ratings
- **Service Verification**: Validate agent capabilities and licensing
- **Real-Time Updates**: Live agent status and availability monitoring
- **Performance Tracking**: Transaction volume and success rate analytics

---

## 🗺️ ZAMBIAN PROVINCES

The system supports all 10 provinces of Zambia:

| Province | Capital | Agent Coverage | Key Features |
|----------|---------|----------------|--------------|
| **Central** | Kabwe | Regional coverage | Mining communities |
| **Copperbelt** | Ndola | High density | Industrial areas |
| **Eastern** | Chipata | Border coverage | Cross-border trade |
| **Luapula** | Mansa | Rural focus | Fishing communities |
| **Lusaka** | Lusaka | Urban concentration | Capital city services |
| **Muchinga** | Chinsali | Emerging coverage | New province |
| **Northern** | Kasama | Rural network | Agricultural areas |
| **Northwestern** | Solwezi | Mining focus | Copper mining |
| **Southern** | Livingstone | Tourism hub | Victoria Falls area |
| **Western** | Mongu | Flood plains | Seasonal accessibility |

---

## 🏪 AGENT MANAGEMENT FEATURES

### Agent Data Structure

```dart
class PayMuleAgent {
  final String agentId;           // Unique identifier
  final String name;              // Agent business name
  final String phoneNumber;       // Contact number
  final String email;             // Email address
  final Province province;        // Zambian province
  final String district;          // District within province
  final String location;          // Specific location
  final double latitude;          // GPS coordinates
  final double longitude;         // GPS coordinates
  final double rating;            // Performance rating (1-5)
  final int totalTransactions;    // Transaction count
  final double totalVolume;       // Total value processed
  final AgentStatus status;       // Current status
  final List<ServiceType> services; // Available services
  final Map<String, dynamic> businessHours; // Operating hours
  final double commissionRate;    // Commission percentage
  final DateTime registeredAt;    // Registration date
  final DateTime lastActiveAt;    // Last activity
  final Map<String, dynamic> verificationData; // BOZ verification
}
```

### Service Types

```dart
enum ServiceType {
  cashIn,              // Cash deposit services
  cashOut,             // Cash withdrawal services
  billPayment,         // Utility bill payments
  utilityPayment,      // ZESCO, NWSC, LWSC payments
  mobileMoneyTransfer, // Mobile money transactions
  accountOpening,      // New account registration
  customerSupport      // Customer assistance
}
```

### Agent Status Types

```dart
enum AgentStatus {
  active,      // Currently operational
  inactive,    // Temporarily unavailable
  suspended,   // Suspended by BOZ/Pay Mule
  underReview, // Under compliance review
  offline,     // Not connected
  busy         // Currently serving customers
}
```

---

## 🔍 AGENT FILTERING & DISCOVERY

### Province-Based Filtering

```dart
// Get agents in Eastern and Lusaka provinces with high ratings
final agents = ZambiaCentralRegistry.getAgents(
  provinces: [Province.eastern, Province.lusaka],
  minRating: 4.0,
  maxResults: 10,
);
```

### Service-Based Filtering

```dart
// Find agents offering utility payment services
final utilityAgents = ZambiaCentralRegistry.getAgents(
  provinces: [Province.lusaka],
  minRating: 4.0,
  requiredServices: [ServiceType.utilityPayment],
);
```

### Geographic Proximity Search

```dart
// Find nearest agents within 10km
final nearestAgents = await agentService.getNearestAgents(
  userLocation: currentPosition,
  maxDistanceKm: 10.0,
  maxResults: 5,
  minRating: 4.0,
);
```

### Multi-Criteria Search

```dart
// Complex filtering example
final filteredAgents = await agentService.getAgentsByProvince(
  provinces: [Province.copperbelt, Province.lusaka],
  minRating: 4.5,
  requiredServices: [ServiceType.cashIn, ServiceType.cashOut],
  status: AgentStatus.active,
  maxDistance: 15.0,
  userLocation: userPosition,
);
```

---

## ⭐ RATING & PERFORMANCE SYSTEM

### Rating Management

```dart
// Update agent rating after transaction
await agentService.updateAgentRating(
  agentId: 'AGENT_LUSAKA_001',
  rating: 4.8,
  userId: 'user_123',
  feedback: 'Excellent service, very professional',
);
```

### Performance Metrics

- **Transaction Volume**: Total value processed
- **Transaction Count**: Number of completed transactions
- **Success Rate**: Percentage of successful transactions
- **Customer Ratings**: Average customer satisfaction score
- **Response Time**: Average service delivery time
- **Availability**: Percentage of operational hours

### Rating Calculation

```dart
// Rating factors (weighted average)
final rating = (
  customerRatings * 0.4 +      // 40% customer feedback
  successRate * 0.3 +          // 30% transaction success
  responseTime * 0.2 +         // 20% service speed
  availability * 0.1           // 10% operational time
);
```

---

## 🏦 BANK OF ZAMBIA COMPLIANCE

### Agent Verification

All agents must meet BOZ requirements:

- **BOZ License**: Valid agent banking license
- **KYC Compliance**: Know Your Customer verification
- **Background Check**: Criminal and financial background verification
- **Training Certification**: Completed BOZ-approved training
- **Insurance Coverage**: Professional indemnity insurance
- **Audit Compliance**: Regular compliance audits

### Verification Data Structure

```dart
final verificationData = {
  'boz_license': 'BOZ_AGENT_LUSAKA_001_2025',
  'verification_status': 'verified',
  'kyc_completed': true,
  'background_check': 'passed',
  'training_certification': 'BOZ_CERT_2025_001',
  'insurance_policy': 'INS_POL_2025_001',
  'last_audit': '2025-07-15T10:00:00Z',
  'audit_status': 'compliant',
};
```

---

## 📊 AGENT STATISTICS & ANALYTICS

### Registry Statistics

```dart
final stats = ZambiaCentralRegistry.getRegistryStatistics();
// Returns:
// {
//   'total_agents': 54,
//   'active_agents': 48,
//   'average_rating': '4.3',
//   'province_distribution': {
//     'lusaka': 25,
//     'eastern': 15,
//     'copperbelt': 8,
//     'southern': 6
//   },
//   'high_rated_agents': 42,
//   'last_updated': '2025-08-01T10:30:00Z'
// }
```

### Performance Analytics

```dart
// Top performing agents by volume
final topPerformers = agents
  .where((a) => a.rating >= 4.0)
  .toList()
  ..sort((a, b) => b.totalVolume.compareTo(a.totalVolume));

// Service coverage analysis
final serviceCoverage = <ServiceType, int>{};
for (final agent in agents) {
  for (final service in agent.services) {
    serviceCoverage[service] = (serviceCoverage[service] ?? 0) + 1;
  }
}
```

---

## 🚀 USAGE EXAMPLES

### Basic Agent Loading

```dart
// Initialize and load production agents
final agentService = AgentService();
await agentService.initialize();
agentService.loadProductionAgents();
```

### Find Agents for Specific Service

```dart
// Find agents for utility payments in Lusaka
final utilityAgents = await agentService.getAgentsByService(
  serviceType: ServiceType.utilityPayment,
  province: Province.lusaka,
  minRating: 4.0,
  maxResults: 5,
);
```

### Agent Details Lookup

```dart
// Get detailed agent information
final agent = await agentService.getAgentById('AGENT_LUSAKA_001');
if (agent != null) {
  print('Agent: ${agent.name}');
  print('Rating: ${agent.rating}/5.0');
  print('Services: ${agent.services.length}');
  print('Location: ${agent.location}');
}
```

### Real-Time Agent Updates

```dart
// Listen to agent updates
agentService.agentStream?.listen((agents) {
  print('Agent list updated: ${agents.length} agents available');
  // Update UI with new agent data
});
```

---

## 🧪 TESTING & DEMONSTRATION

### Running the Demo

Execute the comprehensive agent service demonstration:

```bash
dart lib/services/demo_agent_service.dart
```

### Demo Scenarios

1. **Production Agent Loading**
   - Load agents from Central Registry
   - Apply province and rating filters
   - Display registry statistics

2. **Agent Filtering**
   - Province-based filtering
   - Service-type filtering
   - Rating threshold filtering

3. **Agent Discovery**
   - Agent lookup by ID
   - Multi-service agent search
   - Performance comparison

4. **Statistics Display**
   - Registry overview
   - Province distribution
   - Performance metrics

### Expected Output

```
🇿🇲 PAY MULE ZAMBIA - AGENT SERVICE DEMONSTRATION
======================================================================

🏪 LOADING PRODUCTION AGENTS
──────────────────────────────────────────────────────────
• Loading agents from Zambia Central Registry...
  Filtering: Eastern & Lusaka provinces, min rating 4.0
• Registry Statistics:
  - Total agents: 54
  - Active agents: 48
  - Average rating: 4.3
  - High-rated agents (≥4.0): 42
✅ Production agents loaded successfully

🔍 DEMONSTRATING AGENT FILTERING
──────────────────────────────────────────────────────────
📍 PROVINCE-BASED FILTERING
   Scenario: Find agents in Eastern Province with rating ≥ 4.5

   🏪 Found 8 high-rated agents in Eastern Province:
      • Chipata Agents (Chipata Central)
        Rating: ⭐ 4.7/5.0
        Services: 4 available
        Transactions: 2,847
```

---

## 🔧 CONFIGURATION & CUSTOMIZATION

### Province Configuration

```dart
// Custom province filtering
final customProvinces = [
  Province.lusaka,    // Capital city
  Province.copperbelt, // Mining region
  Province.eastern,   // Border trade
];

final agents = ZambiaCentralRegistry.getAgents(
  provinces: customProvinces,
  minRating: 4.2,
);
```

### Service Requirements

```dart
// Define required services for specific use cases
final cashServices = [ServiceType.cashIn, ServiceType.cashOut];
final billServices = [ServiceType.billPayment, ServiceType.utilityPayment];
final fullServices = [...cashServices, ...billServices, ServiceType.customerSupport];
```

### Performance Thresholds

```dart
// Set performance criteria
const minRating = 4.0;           // Minimum customer rating
const minTransactions = 100;     // Minimum transaction count
const minVolume = 50000.0;       // Minimum volume processed (K50,000)
const maxResponseTime = 300;     // Maximum response time (5 minutes)
```

---

## 📞 SUPPORT & MAINTENANCE

### Monitoring Commands

```bash
# Check agent service status
dart -e "print(AgentService().getAgentStatistics())"

# Monitor registry statistics
dart -e "print(ZambiaCentralRegistry.getRegistryStatistics())"

# Validate agent data
dart lib/services/demo_agent_service.dart
```

### Troubleshooting

1. **No Agents Found**
   - Check province filter settings
   - Verify minimum rating threshold
   - Confirm registry initialization

2. **Outdated Agent Data**
   - Refresh agent cache
   - Check network connectivity
   - Verify registry sync status

3. **Performance Issues**
   - Monitor agent response times
   - Check service availability
   - Review rating calculations

---

**🇿🇲 The Zambian Agent Service System provides comprehensive agent management capabilities, ensuring reliable service delivery across all provinces of Zambia with full Bank of Zambia compliance.**
