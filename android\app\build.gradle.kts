plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.zm.paymule"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // Zambian mobile money application
        applicationId = "com.zm.paymule"
        // Optimized for Zambian devices (Android 5.0+)
        minSdk = 21  // Android 5.0 Lollipop - covers 95%+ of Zambian devices
        targetSdk = 33  // Android 13 for optimal compatibility
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        // Zambia-specific optimizations
        ndk {
            // Target ARM architectures common in Zambian devices
            abiFilters += listOf("armeabi-v7a", "arm64-v8a")
        }

        // Zambian language and locale support
        resourceConfigurations += listOf(
            "en",    // English (primary)
            "bem",   // Bemba (major Zambian language)
            "nya"    // Nyanja/Chewa (major Zambian language)
        )

        // Performance optimizations for entry-level devices
        vectorDrawables {
            useSupportLibrary = true
        }

        // Zambian device compatibility
        multiDexEnabled = true
    }

    buildTypes {
        release {
            // Production signing for Zambian deployment
            signingConfig = signingConfigs.getByName("debug")

            // Optimizations for Zambian devices
            isMinifyEnabled = true
            isShrinkResources = true

            // ProGuard optimizations for smaller APK size
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )

            // Zambian deployment optimizations
            manifestPlaceholders["appName"] = "PayMule Zambia"
            buildConfigField("String", "COUNTRY_CODE", "\"ZM\"")
            buildConfigField("String", "CURRENCY", "\"ZMW\"")
        }

        debug {
            // Debug build for Zambian testing
            applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"
            isDebuggable = true

            // Faster builds for development
            isMinifyEnabled = false
            isShrinkResources = false
        }
    }

    lintOptions {
        isCheckReleaseBuilds = false
        isAbortOnError = false
    }

    // Zambian device-specific configurations
    packagingOptions {
        // Exclude unnecessary files to reduce APK size
        resources {
            excludes += listOf(
                "META-INF/DEPENDENCIES",
                "META-INF/LICENSE",
                "META-INF/LICENSE.txt",
                "META-INF/NOTICE",
                "META-INF/NOTICE.txt",
                "META-INF/*.kotlin_module"
            )
        }

        // Optimize native libraries for Zambian devices
        jniLibs {
            // Keep only ARM architectures common in Zambia
            pickFirsts += listOf(
                "**/libc++_shared.so",
                "**/libflutter.so"
            )
        }
    }

    // Bundle configuration for smaller downloads
    bundle {
        language {
            // Enable language-based APK splits for smaller downloads
            enableSplit = true
        }
        density {
            // Enable density-based APK splits
            enableSplit = true
        }
        abi {
            // Enable ABI-based APK splits
            enableSplit = true
        }
    }

    // Zambian device compatibility settings
    splits {
        abi {
            isEnable = true
            reset()
            include("armeabi-v7a", "arm64-v8a")
            isUniversalApk = true  // Also generate universal APK
        }

        density {
            isEnable = true
            reset()
            include("ldpi", "mdpi", "hdpi", "xhdpi")  // Common densities in Zambia
        }
    }
}

flutter {
    source = "../.."
}
