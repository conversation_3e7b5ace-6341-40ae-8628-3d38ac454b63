# Pay Mule Proguard Rules for Zambian Mobile Money App

# Keep Flutter classes
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-dontwarn io.flutter.**

# Keep mobile money related classes (updated for PayMule Zambia)
-keep class com.zm.paymule.** { *; }
-keep class com.zambiapay.** { *; }

# Keep encryption classes
-keep class javax.crypto.** { *; }
-keep class java.security.** { *; }

# Keep SQLite classes
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }

# Keep network classes for mobile money APIs
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }
-dontwarn okhttp3.**
-dontwarn retrofit2.**

# Keep JSON serialization
-keep class com.google.gson.** { *; }
-keepattributes Signature
-keepattributes *Annotation*

# Keep biometric authentication
-keep class androidx.biometric.** { *; }

# Keep workmanager for background sync
-keep class androidx.work.** { *; }

# General Android rules
-keepattributes SourceFile,LineNumberTable
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# Remove logging in release builds (optimized for Zambian devices)
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# ===== ZAMBIAN DEVICE OPTIMIZATIONS =====

# Zambian locale and currency handling
-keep class java.util.Currency { *; }
-keep class java.text.NumberFormat { *; }
-keep class java.text.DecimalFormat { *; }
-keep class java.util.Locale { *; }

# Mobile money transaction serialization
-keep class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Parcelable for Zambian data transfer
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator CREATOR;
}

# Keep enums for mobile money transaction types
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Native methods for Zambian device compatibility
-keepclasseswithmembernames class * {
    native <methods>;
}

# Custom views for Zambian UI
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Optimization settings for entry-level Zambian devices
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 3
-allowaccessmodification
-dontpreverify

# Keep reflection for mobile money APIs
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# Zambian mobile network provider classes
-keep class * extends android.telephony.** { *; }
-keep class android.telephony.TelephonyManager { *; }

# Keep classes for Zambian payment gateways
-keep class * implements android.os.Parcelable { *; }
-keep class * extends java.lang.Exception { *; }

# Performance optimization for low-end devices
-dontoptimize
-dontobfuscate
