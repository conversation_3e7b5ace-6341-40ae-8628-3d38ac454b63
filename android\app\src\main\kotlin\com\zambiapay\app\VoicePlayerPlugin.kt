package com.zambiapay.app

import android.content.Context
import android.content.res.AssetFileDescriptor
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.util.Log
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.io.IOException

/**
 * Voice Player Plugin for Zambian language audio guidance
 * Supports offline audio playback for rural accessibility
 */
class VoicePlayerPlugin : FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    private var mediaPlayer: MediaPlayer? = null
    
    companion object {
        private const val CHANNEL = "zambia_pay/voice_player"
        private const val TAG = "VoicePlayerPlugin"
    }

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, CHANNEL)
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "playAudio" -> {
                val audioPath = call.argument<String>("audioPath")
                val volume = call.argument<Double>("volume") ?: 0.8
                
                if (audioPath != null) {
                    playAudio(audioPath, volume.toFloat(), result)
                } else {
                    result.error("INVALID_ARGUMENT", "Audio path is required", null)
                }
            }
            
            "stopAudio" -> {
                stopAudio(result)
            }
            
            "audioExists" -> {
                val audioPath = call.argument<String>("audioPath")
                if (audioPath != null) {
                    checkAudioExists(audioPath, result)
                } else {
                    result.error("INVALID_ARGUMENT", "Audio path is required", null)
                }
            }
            
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun playAudio(audioPath: String, volume: Float, result: Result) {
        // Remove 'assets/' prefix if present
        val assetPath = audioPath.removePrefix("assets/")

        try {
            // Stop any currently playing audio
            stopCurrentAudio()
            
            Log.d(TAG, "Attempting to play audio: $assetPath")
            
            // Check if asset exists
            val assetManager = context.assets
            try {
                val afd: AssetFileDescriptor = assetManager.openFd(assetPath)
                
                mediaPlayer = MediaPlayer().apply {
                    // Configure audio attributes for accessibility
                    setAudioAttributes(
                        AudioAttributes.Builder()
                            .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                            .setUsage(AudioAttributes.USAGE_ASSISTANCE_ACCESSIBILITY)
                            .build()
                    )
                    
                    // Set data source from asset
                    setDataSource(afd.fileDescriptor, afd.startOffset, afd.length)
                    afd.close()
                    
                    // Set volume
                    setVolume(volume, volume)
                    
                    // Prepare and play
                    prepareAsync()
                    setOnPreparedListener { mp ->
                        mp.start()
                        Log.d(TAG, "Audio started playing: $assetPath")
                        result.success(true)
                    }
                    
                    setOnCompletionListener { mp ->
                        Log.d(TAG, "Audio playback completed: $assetPath")
                        mp.release()
                        mediaPlayer = null
                    }
                    
                    setOnErrorListener { mp, what, extra ->
                        Log.e(TAG, "MediaPlayer error: what=$what, extra=$extra")
                        mp.release()
                        mediaPlayer = null
                        result.error("PLAYBACK_ERROR", "Failed to play audio: $assetPath", null)
                        true
                    }
                }
                
            } catch (e: IOException) {
                Log.e(TAG, "Asset not found: $assetPath", e)
                result.error("ASSET_NOT_FOUND", "Audio file not found: $assetPath", null)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error playing audio: $assetPath", e)
            result.error("PLAYBACK_ERROR", "Failed to play audio: ${e.message}", null)
        }
    }

    private fun stopAudio(result: Result) {
        try {
            stopCurrentAudio()
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping audio", e)
            result.error("STOP_ERROR", "Failed to stop audio: ${e.message}", null)
        }
    }

    private fun stopCurrentAudio() {
        mediaPlayer?.let { mp ->
            try {
                if (mp.isPlaying) {
                    mp.stop()
                }
                mp.release()
                Log.d(TAG, "Audio stopped and released")
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping current audio", e)
            } finally {
                mediaPlayer = null
            }
        }
    }

    private fun checkAudioExists(audioPath: String, result: Result) {
        try {
            val assetPath = audioPath.removePrefix("assets/")
            val assetManager = context.assets
            
            try {
                val inputStream = assetManager.open(assetPath)
                inputStream.close()
                result.success(true)
            } catch (e: IOException) {
                result.success(false)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error checking audio existence: $audioPath", e)
            result.success(false)
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        stopCurrentAudio()
    }
}

/**
 * Audio file naming conventions for Zambian languages:
 * 
 * English: *_en.wav
 * Bemba: *_bemba.wav  
 * Nyanja: *_nyanja.wav
 * Tonga: *_tonga.wav
 * Lozi: *_lozi.wav
 * 
 * Example files:
 * - assets/audio/bemba/tapili_water_bemba.wav
 * - assets/audio/nyanja/madzi_bill_nyanja.wav
 * - assets/audio/en/water_bill_en.wav
 * 
 * Voice prompts should be:
 * - Clear and slow for rural users
 * - Native speaker recordings
 * - Compressed for offline storage
 * - Maximum 5 seconds duration
 */
