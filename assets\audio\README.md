# Zambia Pay Voice Audio Assets

This directory contains voice guidance audio files for rural accessibility in Zambian languages.

## Directory Structure

```
assets/audio/
├── en/                 # English audio files
├── bemba/              # Bemba audio files  
├── nyanja/             # Nyanja/Chichewa audio files
├── tonga/              # Tonga audio files
└── lozi/               # Lozi audio files
```

## Audio File Naming Convention

Format: `{action}_{language}.wav`

### Core Actions
- `welcome_{lang}.wav` - Welcome message
- `send_money_{lang}.wav` - Send money prompt
- `pay_bills_{lang}.wav` - Pay bills prompt
- `buy_airtime_{lang}.wav` - Buy airtime prompt
- `water_bill_{lang}.wav` - Water bill payment
- `electricity_bill_{lang}.wav` - Electricity bill payment
- `enter_amount_{lang}.wav` - Enter amount prompt
- `enter_phone_{lang}.wav` - Enter phone number prompt
- `confirm_payment_{lang}.wav` - Confirm payment
- `payment_success_{lang}.wav` - Payment successful
- `payment_failed_{lang}.wav` - Payment failed
- `offline_mode_{lang}.wav` - Offline mode notification

### Language-Specific Examples

#### Bemba Files
- `tapili_water_bemba.wav` - "Tapili water bill" (Pay water bill)
- `tuma_indalama_bemba.wav` - "Tuma indalama" (Send money)
- `umeme_bill_bemba.wav` - "Umeme bill" (Electricity bill)

#### Nyanja Files  
- `madzi_bill_nyanja.wav` - "Madzi bill" (Water bill)
- `tumiza_ndalama_nyanja.wav` - "Tumiza ndalama" (Send money)
- `magetsi_bill_nyanja.wav` - "Magetsi bill" (Electricity bill)

#### Tonga Files
- `meenda_bill_tonga.wav` - "Meenda bill" (Water bill)
- `tuma_mali_tonga.wav` - "Tuma mali" (Send money)
- `getsi_bill_tonga.wav` - "Getsi bill" (Electricity bill)

#### Lozi Files
- `mezi_bill_lozi.wav` - "Mezi bill" (Water bill)
- `roma_mali_lozi.wav` - "Roma mali" (Send money)
- `motlakase_bill_lozi.wav` - "Motlakase bill" (Electricity bill)

## Audio Specifications

### Technical Requirements
- **Format**: WAV (uncompressed for quality)
- **Sample Rate**: 22050 Hz (optimized for speech)
- **Bit Depth**: 16-bit
- **Channels**: Mono
- **Duration**: 2-5 seconds maximum
- **File Size**: <100KB per file

### Recording Guidelines
- **Native Speakers**: Use native speakers for each language
- **Clear Pronunciation**: Slow, clear speech for rural users
- **Consistent Volume**: Normalize all audio to same level
- **Background Noise**: Record in quiet environment
- **Tone**: Friendly, helpful tone

## Usage Examples

### Basic Usage
```dart
// Play water bill prompt in Bemba
VoicePlayer.play("bemba", "tapili_water_bemba.wav");

// Play using prompt key
VoicePlayer.playPrompt('water_bill', language: 'bemba');
```

### In Payment Buttons
```dart
PaymentButton(
  icon: Icons.water_drop, 
  label: "NWSC",
  voicePrompt: 'water_bill', // Plays in current language
  onPressed: () {
    // Voice-guided UI (Bemba/Nyanja)
    VoicePlayer.play("bemba", "tapili_water.wav");
  },
);
```

### Language Switching
```dart
// Set language preference
await VoicePlayer.setLanguage('bemba');

// Play welcome in Bemba
await VoicePlayer.playWelcome();
```

## Localization Mapping

### Water Bill Translations
- **English**: "Water bill payment"
- **Bemba**: "Tapili water bill" 
- **Nyanja**: "Madzi bill payment"
- **Tonga**: "Meenda bill payment"
- **Lozi**: "Mezi bill payment"

### Send Money Translations
- **English**: "Send money"
- **Bemba**: "Tuma indalama"
- **Nyanja**: "Tumiza ndalama" 
- **Tonga**: "Tuma mali"
- **Lozi**: "Roma mali"

### Electricity Bill Translations
- **English**: "Electricity bill"
- **Bemba**: "Umeme bill"
- **Nyanja**: "Magetsi bill"
- **Tonga**: "Getsi bill" 
- **Lozi**: "Motlakase bill"

## Implementation Notes

### Offline Support
- All audio files are bundled with the app
- No internet required for voice guidance
- Fallback to English if language file missing

### Accessibility Features
- Large, clear icons with voice prompts
- Haptic feedback on button press
- Voice confirmation for actions
- Support for screen readers

### Rural Optimization
- Simple, familiar vocabulary
- Slow speech pace
- High contrast visual design
- Large touch targets (minimum 48dp)

## File Compression

For production, consider:
- **MP3 format** for smaller file sizes
- **Opus codec** for best compression
- **Quality vs Size** balance for rural networks

## Future Enhancements

### Dynamic Audio
- Download language packs on demand
- Update audio files remotely
- User-recorded custom prompts

### Advanced Features
- Text-to-speech fallback
- Voice recognition for commands
- Multi-language mixing in single session

## Contributing

When adding new audio files:
1. Follow naming convention
2. Use native speakers
3. Test on actual devices
4. Validate file sizes
5. Update this documentation

## Testing

Test audio playback on:
- Low-end Android devices
- Different volume levels  
- Noisy environments
- With/without headphones
- Various network conditions
