<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Pay Mule Icon - Wallet/Card Design -->
  <defs>
    <!-- Zambia-inspired gradient -->
    <linearGradient id="zambiaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#228B22;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC143C;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle for better app icon appearance -->
  <circle cx="256" cy="256" r="240" fill="url(#zambiaGradient)" opacity="0.1"/>
  
  <!-- Main wallet shape -->
  <path d="M80 120 C80 80, 120 40, 160 40 L440 40 C480 40, 520 80, 520 120 L520 160 C520 200, 480 240, 440 240 L160 240 C120 240, 80 200, 80 160 Z" fill="#000000"/>
  
  <!-- Card slot -->
  <path d="M120 80 C120 70, 130 60, 140 60 L400 60 C410 60, 420 70, 420 80 L420 100 C420 110, 410 120, 400 120 L140 120 C130 120, 120 110, 120 100 Z" fill="#FFFFFF"/>
  
  <!-- Main wallet body -->
  <path d="M80 200 L80 400 C80 440, 120 480, 160 480 L400 480 C440 480, 480 440, 480 400 L480 280 C480 240, 440 200, 400 200 L160 200 C120 200, 80 240, 80 280 Z" fill="#000000"/>
  
  <!-- Wallet interior -->
  <path d="M120 240 L120 400 C120 420, 140 440, 160 440 L400 440 C420 440, 440 420, 440 400 L440 280 C440 260, 420 240, 400 240 L160 240 C140 240, 120 260, 120 280 Z" fill="#F5F5F5"/>
  
  <!-- Card handle/tab -->
  <path d="M360 280 C360 260, 380 240, 400 240 L440 240 C460 240, 480 260, 480 280 L480 360 C480 380, 460 400, 440 400 L400 400 C380 400, 360 380, 360 360 Z" fill="#000000"/>
  
  <!-- Card handle interior -->
  <path d="M380 300 C380 290, 390 280, 400 280 L420 280 C430 280, 440 290, 440 300 L440 340 C440 350, 430 360, 420 360 L400 360 C390 360, 380 350, 380 340 Z" fill="#FFFFFF"/>
</svg>
