#!/usr/bin/env pwsh

# Pay Mule Production APK Build Script (PowerShell)
# Builds optimized production APK for Zambia deployment
# Includes asset optimization for Zambia bandwidth constraints

param(
    [string]$Environment = "zm_prod",
    [switch]$CleanBuild = $false
)

# Configuration
$BuildDir = "build"
$OutputDir = "$BuildDir\production_release"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Pre-build validation
function Test-Environment {
    Write-Info "Validating build environment..."
    
    # Check Flutter installation
    try {
        $flutterVersion = flutter --version 2>$null | Select-Object -First 1
        Write-Info "Flutter version: $flutterVersion"
    }
    catch {
        Write-Error "Flutter is not installed or not in PATH"
        exit 1
    }
    
    # Check if we're in a Flutter project
    if (-not (Test-Path "pubspec.yaml")) {
        Write-Error "Not in a Flutter project directory"
        exit 1
    }
    
    # Validate production configuration
    if ($Environment -eq "zm_prod") {
        Write-Info "Building for Zambia Production environment"
        
        # Check if production credentials are configured
        $productionConfig = Get-Content "lib\core\config\production_config.dart" -Raw
        if ($productionConfig -match "REPLACE_WITH_ACTUAL") {
            Write-Warning "Production credentials contain placeholder values"
            Write-Warning "Please update lib\core\config\production_config.dart with actual credentials"
        }
    }
    
    Write-Success "Environment validation passed"
}

# Clean build artifacts
function Invoke-CleanBuild {
    if ($CleanBuild) {
        Write-Info "Performing clean build..."
        
        flutter clean
        if (Test-Path $BuildDir) {
            Remove-Item -Recurse -Force $BuildDir
        }
        
        Write-Success "Build artifacts cleaned"
    }
}

# Optimize assets for Zambia bandwidth
function Optimize-Assets {
    Write-Info "Optimizing assets for Zambia bandwidth..."
    
    # Create optimized assets directory
    $optimizedDir = "assets\optimized"
    if (-not (Test-Path $optimizedDir)) {
        New-Item -ItemType Directory -Path $optimizedDir -Force | Out-Null
    }
    
    # Copy assets (basic optimization - would need ImageMagick for advanced optimization)
    if (Test-Path "assets\images") {
        Copy-Item "assets\images\*" $optimizedDir -Recurse -Force
        Write-Info "Images copied to optimized directory"
    }
    
    if (Test-Path "assets\fonts") {
        Copy-Item "assets\fonts\*" $optimizedDir -Recurse -Force
        Write-Info "Fonts copied to optimized directory"
    }
    
    Write-Success "Asset optimization completed"
}

# Build Android APK
function Build-AndroidAPK {
    Write-Info "Building Android APK for production..."
    
    # Set environment variables for production
    $env:ENV = "production"
    $env:REGION = "zambia"
    $env:TEST_MODE = "false"
    
    # Build release APK with split per ABI
    Write-Info "Building split APKs..."
    flutter build apk --release `
        --dart-define=ENV=production `
        --dart-define=REGION=zambia `
        --dart-define=TEST_MODE=false `
        --target-platform android-arm,android-arm64,android-x64 `
        --split-per-abi
    
    # Create output directory
    $androidOutputDir = "$OutputDir\android"
    if (-not (Test-Path $androidOutputDir)) {
        New-Item -ItemType Directory -Path $androidOutputDir -Force | Out-Null
    }
    
    # Copy APK files
    $apkSourceDir = "build\app\outputs\flutter-apk"
    if (Test-Path "$apkSourceDir\app-arm64-v8a-release.apk") {
        Copy-Item "$apkSourceDir\app-arm64-v8a-release.apk" "$androidOutputDir\pay-mule-arm64-v8a-$Timestamp.apk"
    }
    if (Test-Path "$apkSourceDir\app-armeabi-v7a-release.apk") {
        Copy-Item "$apkSourceDir\app-armeabi-v7a-release.apk" "$androidOutputDir\pay-mule-armeabi-v7a-$Timestamp.apk"
    }
    if (Test-Path "$apkSourceDir\app-x86_64-release.apk") {
        Copy-Item "$apkSourceDir\app-x86_64-release.apk" "$androidOutputDir\pay-mule-x86_64-$Timestamp.apk"
    }
    
    # Create universal APK
    Write-Info "Building universal APK..."
    flutter build apk --release `
        --dart-define=ENV=production `
        --dart-define=REGION=zambia `
        --dart-define=TEST_MODE=false
    
    if (Test-Path "$apkSourceDir\app-release.apk") {
        Copy-Item "$apkSourceDir\app-release.apk" "$androidOutputDir\pay-mule-universal-$Timestamp.apk"
    }
    
    Write-Success "Android APK build completed"
}

# Generate checksums
function New-Checksums {
    Write-Info "Generating SHA-256 checksums..."
    
    $checksumFile = "$OutputDir\checksums.txt"
    $checksums = @()
    
    # Generate checksums for all APK files
    Get-ChildItem -Path $OutputDir -Filter "*.apk" -Recurse | ForEach-Object {
        $hash = Get-FileHash -Path $_.FullName -Algorithm SHA256
        $relativePath = $_.FullName.Replace((Get-Location).Path + "\", "")
        $checksums += "$($hash.Hash.ToLower())  $relativePath"
    }
    
    $checksums | Out-File -FilePath $checksumFile -Encoding UTF8
    
    Write-Success "Checksums generated: $checksumFile"
}

# Validate build outputs
function Test-BuildOutputs {
    Write-Info "Validating build outputs..."
    
    $errorCount = 0
    
    # Check Android APKs
    $apkFiles = @(
        "$OutputDir\android\pay-mule-universal-$Timestamp.apk",
        "$OutputDir\android\pay-mule-arm64-v8a-$Timestamp.apk",
        "$OutputDir\android\pay-mule-armeabi-v7a-$Timestamp.apk"
    )
    
    foreach ($apk in $apkFiles) {
        if (Test-Path $apk) {
            $size = (Get-Item $apk).Length
            if ($size -gt 1MB) {
                $sizeMB = [math]::Round($size / 1MB, 2)
                Write-Success "APK validated: $(Split-Path $apk -Leaf) ($sizeMB MB)"
            }
            else {
                Write-Error "APK too small (possible build failure): $(Split-Path $apk -Leaf)"
                $errorCount++
            }
        }
        else {
            Write-Error "APK not found: $(Split-Path $apk -Leaf)"
            $errorCount++
        }
    }
    
    # Check checksums file
    if (Test-Path "$OutputDir\checksums.txt") {
        Write-Success "Checksums file created"
    }
    else {
        Write-Error "Checksums file missing"
        $errorCount++
    }
    
    if ($errorCount -eq 0) {
        Write-Success "Build validation passed"
        return $true
    }
    else {
        Write-Error "Build validation failed with $errorCount errors"
        return $false
    }
}

# Generate build report
function New-BuildReport {
    Write-Info "Generating build report..."
    
    $reportFile = "$OutputDir\build_report_$Timestamp.txt"
    $flutterVersion = flutter --version | Select-Object -First 1
    
    $buildOutputs = Get-ChildItem -Path $OutputDir -Filter "*.apk" -Recurse | ForEach-Object {
        $sizeMB = [math]::Round($_.Length / 1MB, 2)
        "  $($_.Name): $sizeMB MB"
    }
    
    $checksums = if (Test-Path "$OutputDir\checksums.txt") {
        Get-Content "$OutputDir\checksums.txt"
    } else {
        "No checksums available"
    }
    
    $report = @"
Pay Mule Production Build Report
================================
Build Date: $(Get-Date)
Environment: $Environment
Clean Build: $CleanBuild
Flutter Version: $flutterVersion

Build Outputs:
$($buildOutputs -join "`n")

SHA-256 Checksums:
$($checksums -join "`n")

Next Steps:
1. Test APK installation on target devices
2. Verify all features work in production mode
3. Submit to Google Play Store for review
4. Deploy to production environment

Build completed successfully!
"@
    
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    
    Write-Success "Build report generated: $reportFile"
}

# Main execution
function Main {
    Write-Info "Pay Mule Production Build Script"
    Write-Info "================================"
    Write-Info "Environment: $Environment"
    Write-Info "Clean Build: $CleanBuild"
    Write-Info "Timestamp: $Timestamp"
    Write-Host ""
    
    Test-Environment
    Invoke-CleanBuild
    
    # Get dependencies
    Write-Info "Getting Flutter dependencies..."
    flutter pub get
    
    Optimize-Assets
    Build-AndroidAPK
    New-Checksums
    
    if (Test-BuildOutputs) {
        New-BuildReport
        
        Write-Success "Production build completed successfully!"
        Write-Info "Output directory: $OutputDir"
        Write-Info "Build report: $OutputDir\build_report_$Timestamp.txt"
        
        Write-Host ""
        Write-Info "Next steps:"
        Write-Host "  1. Test APK on physical devices"
        Write-Host "  2. Verify production API connections"
        Write-Host "  3. Run integration tests"
        Write-Host "  4. Submit to app stores"
    }
    else {
        Write-Error "Production build failed validation"
        exit 1
    }
}

# Run main function
Main
