#!/bin/bash

# Pay Mule Production APK Build Script
# Builds optimized production APK and iOS IPA for Zambia deployment
# Includes asset optimization for Zambia bandwidth constraints

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-zm_prod}
CLEAN_BUILD=${2:-false}
BUILD_DIR="build"
OUTPUT_DIR="$BUILD_DIR/production_release"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Pre-build validation
validate_environment() {
    print_info "Validating build environment..."
    
    # Check Flutter installation
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Check Flutter version
    local flutter_version=$(flutter --version | head -n 1)
    print_info "Flutter version: $flutter_version"
    
    # Check if we're in a Flutter project
    if [ ! -f "pubspec.yaml" ]; then
        print_error "Not in a Flutter project directory"
        exit 1
    fi
    
    # Validate production configuration
    if [ "$ENVIRONMENT" = "zm_prod" ]; then
        print_info "Building for Zambia Production environment"
        
        # Check if production credentials are configured
        if grep -q "REPLACE_WITH_ACTUAL" lib/core/config/production_config.dart; then
            print_warning "Production credentials contain placeholder values"
            print_warning "Please update lib/core/config/production_config.dart with actual credentials"
        fi
    fi
    
    print_success "Environment validation passed"
}

# Clean build artifacts
clean_build() {
    if [ "$CLEAN_BUILD" = "true" ] || [ "$CLEAN_BUILD" = "--clean-build" ]; then
        print_info "Performing clean build..."
        
        flutter clean
        rm -rf $BUILD_DIR
        
        print_success "Build artifacts cleaned"
    fi
}

# Optimize assets for Zambia bandwidth
optimize_assets() {
    print_info "Optimizing assets for Zambia bandwidth..."
    
    # Create optimized assets directory
    mkdir -p assets/optimized
    
    # Optimize images (reduce file sizes for 2G/3G networks)
    if command -v convert &> /dev/null; then
        print_info "Optimizing images with ImageMagick..."
        
        find assets/images -name "*.png" -type f | while read -r img; do
            local optimized_img="assets/optimized/$(basename "$img")"
            convert "$img" -strip -quality 85 -resize 80% "$optimized_img" 2>/dev/null || cp "$img" "$optimized_img"
        done
        
        find assets/images -name "*.jpg" -type f | while read -r img; do
            local optimized_img="assets/optimized/$(basename "$img")"
            convert "$img" -strip -quality 75 "$optimized_img" 2>/dev/null || cp "$img" "$optimized_img"
        done
        
        print_success "Images optimized for bandwidth"
    else
        print_warning "ImageMagick not found, skipping image optimization"
        cp -r assets/images/* assets/optimized/ 2>/dev/null || true
    fi
    
    # Optimize fonts (subset for Zambian languages)
    print_info "Optimizing fonts for Zambian languages..."
    # This would typically involve font subsetting for English, Bemba, Nyanja, etc.
    # For now, we'll just copy the fonts
    cp -r assets/fonts/* assets/optimized/ 2>/dev/null || true
    
    print_success "Asset optimization completed"
}

# Build Android APK
build_android_apk() {
    print_info "Building Android APK for production..."
    
    # Set environment variables for production
    export ENV=production
    export REGION=zambia
    export TEST_MODE=false
    
    # Build release APK
    flutter build apk \
        --release \
        --dart-define=ENV=production \
        --dart-define=REGION=zambia \
        --dart-define=TEST_MODE=false \
        --target-platform android-arm,android-arm64,android-x64 \
        --split-per-abi
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR/android"
    
    # Copy APK files
    cp build/app/outputs/flutter-apk/app-arm64-v8a-release.apk "$OUTPUT_DIR/android/pay-mule-arm64-v8a-${TIMESTAMP}.apk"
    cp build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk "$OUTPUT_DIR/android/pay-mule-armeabi-v7a-${TIMESTAMP}.apk"
    cp build/app/outputs/flutter-apk/app-x86_64-release.apk "$OUTPUT_DIR/android/pay-mule-x86_64-${TIMESTAMP}.apk"
    
    # Create universal APK
    flutter build apk --release \
        --dart-define=ENV=production \
        --dart-define=REGION=zambia \
        --dart-define=TEST_MODE=false
    
    cp build/app/outputs/flutter-apk/app-release.apk "$OUTPUT_DIR/android/pay-mule-universal-${TIMESTAMP}.apk"
    
    print_success "Android APK build completed"
}

# Build iOS IPA (if on macOS)
build_ios_ipa() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        print_info "Building iOS IPA for production..."
        
        # Build iOS release
        flutter build ios \
            --release \
            --dart-define=ENV=production \
            --dart-define=REGION=zambia \
            --dart-define=TEST_MODE=false
        
        # Create output directory
        mkdir -p "$OUTPUT_DIR/ios"
        
        # Note: Actual IPA creation requires Xcode and proper signing
        print_info "iOS build completed. Use Xcode to create signed IPA."
        print_info "Archive location: build/ios/archive/Runner.xcarchive"
        
        print_success "iOS build completed"
    else
        print_warning "iOS build skipped (not on macOS)"
    fi
}

# Generate checksums
generate_checksums() {
    print_info "Generating SHA-256 checksums..."
    
    cd "$OUTPUT_DIR"
    
    # Generate checksums for all APK files
    find . -name "*.apk" -type f -exec sha256sum {} \; > checksums.txt
    find . -name "*.ipa" -type f -exec sha256sum {} \; >> checksums.txt 2>/dev/null || true
    
    print_success "Checksums generated: $OUTPUT_DIR/checksums.txt"
    
    cd - > /dev/null
}

# Validate build outputs
validate_build() {
    print_info "Validating build outputs..."
    
    local error_count=0
    
    # Check Android APKs
    local apk_files=(
        "$OUTPUT_DIR/android/pay-mule-universal-${TIMESTAMP}.apk"
        "$OUTPUT_DIR/android/pay-mule-arm64-v8a-${TIMESTAMP}.apk"
        "$OUTPUT_DIR/android/pay-mule-armeabi-v7a-${TIMESTAMP}.apk"
    )
    
    for apk in "${apk_files[@]}"; do
        if [ -f "$apk" ]; then
            local size=$(stat -f%z "$apk" 2>/dev/null || stat -c%s "$apk" 2>/dev/null)
            if [ "$size" -gt 1048576 ]; then  # > 1MB
                print_success "APK validated: $(basename "$apk") ($(($size / 1048576)) MB)"
            else
                print_error "APK too small (possible build failure): $(basename "$apk")"
                ((error_count++))
            fi
        else
            print_error "APK not found: $(basename "$apk")"
            ((error_count++))
        fi
    done
    
    # Check checksums file
    if [ -f "$OUTPUT_DIR/checksums.txt" ]; then
        print_success "Checksums file created"
    else
        print_error "Checksums file missing"
        ((error_count++))
    fi
    
    if [ $error_count -eq 0 ]; then
        print_success "Build validation passed"
        return 0
    else
        print_error "Build validation failed with $error_count errors"
        return 1
    fi
}

# Generate build report
generate_build_report() {
    print_info "Generating build report..."
    
    local report_file="$OUTPUT_DIR/build_report_${TIMESTAMP}.txt"
    
    cat > "$report_file" << EOF
Pay Mule Production Build Report
================================
Build Date: $(date)
Environment: $ENVIRONMENT
Clean Build: $CLEAN_BUILD
Flutter Version: $(flutter --version | head -n 1)

Build Outputs:
$(find "$OUTPUT_DIR" -name "*.apk" -o -name "*.ipa" | while read -r file; do
    local size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
    echo "  $(basename "$file"): $(($size / 1048576)) MB"
done)

SHA-256 Checksums:
$(cat "$OUTPUT_DIR/checksums.txt" 2>/dev/null || echo "No checksums available")

Next Steps:
1. Test APK installation on target devices
2. Verify all features work in production mode
3. Submit to Google Play Store for review
4. Deploy to production environment

Build completed successfully!
EOF
    
    print_success "Build report generated: $report_file"
}

# Main execution
main() {
    print_info "Pay Mule Production Build Script"
    print_info "================================"
    print_info "Environment: $ENVIRONMENT"
    print_info "Clean Build: $CLEAN_BUILD"
    print_info "Timestamp: $TIMESTAMP"
    echo
    
    validate_environment
    clean_build
    
    # Get dependencies
    print_info "Getting Flutter dependencies..."
    flutter pub get
    
    optimize_assets
    build_android_apk
    build_ios_ipa
    generate_checksums
    
    if validate_build; then
        generate_build_report
        
        print_success "Production build completed successfully!"
        print_info "Output directory: $OUTPUT_DIR"
        print_info "Build report: $OUTPUT_DIR/build_report_${TIMESTAMP}.txt"
        
        echo
        print_info "Next steps:"
        echo "  1. Test APK on physical devices"
        echo "  2. Verify production API connections"
        echo "  3. Run integration tests"
        echo "  4. Submit to app stores"
    else
        print_error "Production build failed validation"
        exit 1
    fi
}

# Run main function
main
