#!/bin/bash

# build_zambia_compatible_apk.sh - Generate compatible APK for Zambian devices
# Comprehensive build script with V1 signing and specific ABI targeting

set -e

# Default values
MIN_SDK="21"
V1_SIGNING="false"
ABI_FILTERS="armeabi-v7a,arm64-v8a"
OUTPUT_APK=""
BUILD_TYPE="release"
KEYSTORE_FILE="zm_prod_key.jks"
KEYSTORE_ALIAS="paymule_zambia"
KEYSTORE_PASSWORD="PayMuleZM2024!"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_build() {
    echo -e "${MAGENTA}[BUILD]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Generate compatible APK for Zambian devices with specific optimizations"
    echo ""
    echo "Options:"
    echo "  --min-sdk=VERSION           Minimum SDK version (default: 21)"
    echo "  --v1-signing                Enable V1 signature scheme for older devices"
    echo "  --abi-filters=LIST          Comma-separated ABI filters (default: armeabi-v7a,arm64-v8a)"
    echo "  --output=FILE               Output APK filename (required)"
    echo "  --build-type=TYPE           Build type: release or debug (default: release)"
    echo "  --keystore=FILE             Keystore file for signing (default: zm_prod_key.jks)"
    echo "  --help                      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --min-sdk=21 --v1-signing --abi-filters=\"armeabi-v7a\" --output=paymule_fixed.apk"
    echo "  $0 --min-sdk=19 --v1-signing --abi-filters=\"armeabi-v7a,arm64-v8a\" --output=paymule_universal.apk"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --min-sdk=*)
            MIN_SDK="${1#*=}"
            shift
            ;;
        --v1-signing)
            V1_SIGNING="true"
            shift
            ;;
        --abi-filters=*)
            ABI_FILTERS="${1#*=}"
            shift
            ;;
        --output=*)
            OUTPUT_APK="${1#*=}"
            shift
            ;;
        --build-type=*)
            BUILD_TYPE="${1#*=}"
            shift
            ;;
        --keystore=*)
            KEYSTORE_FILE="${1#*=}"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$OUTPUT_APK" ]]; then
    print_error "Output APK filename is required. Use --output=FILE"
    show_usage
    exit 1
fi

print_header "🇿🇲 BUILDING ZAMBIAN COMPATIBLE APK"
print_status "Configuration:"
print_status "  • Min SDK: $MIN_SDK"
print_status "  • V1 Signing: $V1_SIGNING"
print_status "  • ABI Filters: $ABI_FILTERS"
print_status "  • Output APK: $OUTPUT_APK"
print_status "  • Build Type: $BUILD_TYPE"
echo ""

# Step 1: Environment validation
print_header "🔍 ENVIRONMENT VALIDATION"

# Check Flutter
if ! command -v flutter &> /dev/null; then
    print_error "Flutter not found. Please install Flutter SDK."
    exit 1
fi
print_success "Flutter SDK found: $(flutter --version | head -1)"

# Check Android SDK
if [[ -z "$ANDROID_HOME" ]]; then
    print_warning "ANDROID_HOME not set. Trying to detect..."
    if [[ -d "$HOME/Android/Sdk" ]]; then
        export ANDROID_HOME="$HOME/Android/Sdk"
        print_status "Found Android SDK: $ANDROID_HOME"
    else
        print_error "Android SDK not found. Please set ANDROID_HOME."
        exit 1
    fi
fi

# Check Java
if ! command -v java &> /dev/null; then
    print_error "Java not found. Please install JDK 11 or higher."
    exit 1
fi
print_success "Java found: $(java -version 2>&1 | head -1)"

echo ""

# Step 2: Update build configuration
print_header "⚙️  UPDATING BUILD CONFIGURATION"

# Backup original build.gradle.kts
if [[ -f "android/app/build.gradle.kts" ]]; then
    cp "android/app/build.gradle.kts" "android/app/build.gradle.kts.backup.$(date +%Y%m%d_%H%M%S)"
    print_status "Build configuration backed up"
fi

# Update minSdk in build.gradle.kts
print_status "Updating minimum SDK to $MIN_SDK..."
if [[ -f "android/app/build.gradle.kts" ]]; then
    sed -i.bak "s/minSdk = [0-9]*/minSdk = $MIN_SDK/g" "android/app/build.gradle.kts"
    print_success "Minimum SDK updated to $MIN_SDK"
else
    print_error "build.gradle.kts not found"
    exit 1
fi

# Update ABI filters
print_status "Updating ABI filters to: $ABI_FILTERS..."
IFS=',' read -ra ABI_ARRAY <<< "$ABI_FILTERS"
ABI_LIST=""
for abi in "${ABI_ARRAY[@]}"; do
    ABI_LIST="$ABI_LIST\"$abi\", "
done
ABI_LIST=${ABI_LIST%, }  # Remove trailing comma

# Update ndk abiFilters in build.gradle.kts
if grep -q "abiFilters" "android/app/build.gradle.kts"; then
    sed -i.bak "s/abiFilters += listOf(.*)/abiFilters += listOf($ABI_LIST)/g" "android/app/build.gradle.kts"
    print_success "ABI filters updated: $ABI_FILTERS"
else
    print_warning "ABI filters not found in build configuration"
fi

echo ""

# Step 3: Clean and prepare
print_header "🧹 CLEANING BUILD ENVIRONMENT"
print_status "Cleaning Flutter build cache..."
flutter clean

print_status "Cleaning Android build cache..."
cd android
./gradlew clean
cd ..

print_status "Getting dependencies..."
flutter pub get

print_success "Build environment prepared"
echo ""

# Step 4: Create keystore if needed and V1 signing is enabled
if [[ "$V1_SIGNING" == "true" ]]; then
    print_header "🔐 PREPARING V1 SIGNING"
    
    if [[ ! -f "$KEYSTORE_FILE" ]]; then
        print_status "Creating keystore for V1 signing: $KEYSTORE_FILE"
        
        keytool -genkey \
            -v \
            -keystore "$KEYSTORE_FILE" \
            -alias "$KEYSTORE_ALIAS" \
            -keyalg RSA \
            -keysize 2048 \
            -validity 10000 \
            -storepass "$KEYSTORE_PASSWORD" \
            -keypass "$KEYSTORE_PASSWORD" \
            -dname "CN=PayMule Zambia, OU=Mobile Money, O=PayMule Ltd, L=Lusaka, ST=Lusaka, C=ZM"
        
        print_success "Keystore created: $KEYSTORE_FILE"
    else
        print_success "Using existing keystore: $KEYSTORE_FILE"
    fi
    echo ""
fi

# Step 5: Build APK
print_header "🏗️  BUILDING COMPATIBLE APK"

# Determine build command based on ABI filters
BUILD_ARGS=""
if [[ "$ABI_FILTERS" == "armeabi-v7a" ]]; then
    BUILD_ARGS="--target-platform android-arm"
elif [[ "$ABI_FILTERS" == "arm64-v8a" ]]; then
    BUILD_ARGS="--target-platform android-arm64"
elif [[ "$ABI_FILTERS" == "armeabi-v7a,arm64-v8a" ]]; then
    BUILD_ARGS="--target-platform android-arm,android-arm64"
else
    BUILD_ARGS="--target-platform android-arm,android-arm64"
fi

print_build "Building APK with configuration:"
print_build "  • Target platforms: $BUILD_ARGS"
print_build "  • Build type: $BUILD_TYPE"
print_build "  • Min SDK: $MIN_SDK"

# Build the APK
if [[ "$BUILD_TYPE" == "release" ]]; then
    flutter build apk \
        --release \
        $BUILD_ARGS \
        --dart-define=COUNTRY_CODE=ZM \
        --dart-define=CURRENCY=ZMW \
        --dart-define=MIN_SDK=$MIN_SDK
else
    flutter build apk \
        --debug \
        $BUILD_ARGS \
        --dart-define=COUNTRY_CODE=ZM \
        --dart-define=CURRENCY=ZMW \
        --dart-define=MIN_SDK=$MIN_SDK
fi

if [[ $? -eq 0 ]]; then
    print_success "APK build completed successfully"
else
    print_error "APK build failed"
    exit 1
fi

echo ""

# Step 6: Handle V1 signing if enabled
if [[ "$V1_SIGNING" == "true" ]]; then
    print_header "✍️  APPLYING V1 SIGNATURE"
    
    # Find the built APK
    BUILT_APK=""
    if [[ -f "build/app/outputs/flutter-apk/app-release.apk" ]]; then
        BUILT_APK="build/app/outputs/flutter-apk/app-release.apk"
    elif [[ -f "build/app/outputs/flutter-apk/app-debug.apk" ]]; then
        BUILT_APK="build/app/outputs/flutter-apk/app-debug.apk"
    else
        print_error "Built APK not found"
        exit 1
    fi
    
    print_status "Applying V1 signature to: $BUILT_APK"
    
    # Create temporary signed APK
    TEMP_SIGNED_APK="${OUTPUT_APK%.apk}_temp_signed.apk"
    
    if command -v apksigner &> /dev/null; then
        # Use apksigner for V1 + V2 signing
        apksigner sign \
            --ks "$KEYSTORE_FILE" \
            --ks-key-alias "$KEYSTORE_ALIAS" \
            --ks-pass pass:"$KEYSTORE_PASSWORD" \
            --key-pass pass:"$KEYSTORE_PASSWORD" \
            --v1-signing-enabled \
            --v2-signing-enabled \
            --out "$TEMP_SIGNED_APK" \
            "$BUILT_APK"
        
        print_success "V1 + V2 signature applied"
        
    elif command -v jarsigner &> /dev/null; then
        # Use jarsigner for V1 signing only
        cp "$BUILT_APK" "$TEMP_SIGNED_APK"
        
        jarsigner \
            -verbose \
            -sigalg SHA256withRSA \
            -digestalg SHA-256 \
            -keystore "$KEYSTORE_FILE" \
            -storepass "$KEYSTORE_PASSWORD" \
            -keypass "$KEYSTORE_PASSWORD" \
            "$TEMP_SIGNED_APK" \
            "$KEYSTORE_ALIAS"
        
        print_success "V1 signature applied"
    else
        print_error "Neither apksigner nor jarsigner found"
        exit 1
    fi
    
    # Align the APK
    if command -v zipalign &> /dev/null; then
        print_status "Aligning APK..."
        zipalign -v 4 "$TEMP_SIGNED_APK" "$OUTPUT_APK"
        rm "$TEMP_SIGNED_APK"
        print_success "APK aligned and saved as: $OUTPUT_APK"
    else
        mv "$TEMP_SIGNED_APK" "$OUTPUT_APK"
        print_warning "zipalign not found. APK may not be optimally aligned."
    fi
    
else
    # Copy the built APK to output location
    if [[ -f "build/app/outputs/flutter-apk/app-release.apk" ]]; then
        cp "build/app/outputs/flutter-apk/app-release.apk" "$OUTPUT_APK"
    elif [[ -f "build/app/outputs/flutter-apk/app-debug.apk" ]]; then
        cp "build/app/outputs/flutter-apk/app-debug.apk" "$OUTPUT_APK"
    else
        print_error "Built APK not found"
        exit 1
    fi
    print_success "APK saved as: $OUTPUT_APK"
fi

echo ""

# Step 7: Verification
print_header "✅ APK VERIFICATION"

if [[ -f "$OUTPUT_APK" ]]; then
    # Check file size
    APK_SIZE=$(du -h "$OUTPUT_APK" | cut -f1)
    print_status "APK Size: $APK_SIZE"
    
    # Verify with aapt if available
    if command -v aapt &> /dev/null; then
        print_status "Verifying APK structure..."
        
        # Check minimum SDK
        ACTUAL_MIN_SDK=$(aapt dump badging "$OUTPUT_APK" 2>/dev/null | grep "sdkVersion" | sed "s/.*sdkVersion:'\([0-9]*\)'.*/\1/" || echo "unknown")
        if [[ "$ACTUAL_MIN_SDK" == "$MIN_SDK" ]]; then
            print_success "✅ Minimum SDK verified: $ACTUAL_MIN_SDK"
        else
            print_warning "⚠️  Minimum SDK mismatch: expected $MIN_SDK, got $ACTUAL_MIN_SDK"
        fi
        
        # Check package name
        PACKAGE_NAME=$(aapt dump badging "$OUTPUT_APK" 2>/dev/null | grep "package:" | sed "s/.*name='\([^']*\)'.*/\1/" || echo "unknown")
        print_status "Package name: $PACKAGE_NAME"
        
        # Check native libraries
        print_status "Native libraries:"
        aapt list "$OUTPUT_APK" | grep "lib/" | head -5 || echo "No native libraries found"
        
    fi
    
    # Verify signature if V1 signing was applied
    if [[ "$V1_SIGNING" == "true" ]]; then
        print_status "Verifying signature..."
        if command -v apksigner &> /dev/null; then
            if apksigner verify --verbose "$OUTPUT_APK" > /dev/null 2>&1; then
                print_success "✅ APK signature verified"
            else
                print_warning "⚠️  APK signature verification failed"
            fi
        fi
    fi
    
else
    print_error "Output APK not found: $OUTPUT_APK"
    exit 1
fi

echo ""

# Step 8: Final summary
print_header "🎉 BUILD COMPLETE"
print_success "Zambian compatible APK generated successfully!"
echo ""
print_status "📱 APK Details:"
print_status "  • File: $OUTPUT_APK"
print_status "  • Size: $APK_SIZE"
print_status "  • Min SDK: $MIN_SDK (Android $(( MIN_SDK <= 21 ? 5.0 : MIN_SDK <= 23 ? 6.0 : MIN_SDK <= 25 ? 7.1 : MIN_SDK <= 28 ? 9.0 : 10.0 ))+)"
print_status "  • ABI Support: $ABI_FILTERS"
print_status "  • V1 Signing: $V1_SIGNING"
echo ""
print_status "🇿🇲 Zambian Device Compatibility:"
print_status "  • Entry-level smartphones: ✅"
print_status "  • Budget Android devices: ✅"
print_status "  • Older Android versions: ✅"
print_status "  • ARM processors: ✅"
echo ""
print_status "📋 Next Steps:"
print_status "1. Test installation: adb install -r -g -t $OUTPUT_APK"
print_status "2. Run diagnostics: ./generate_install_report.sh --format=html"
print_status "3. Deploy to Zambian devices"

# Restore original build configuration
if [[ -f "android/app/build.gradle.kts.backup.$(date +%Y%m%d)_"* ]]; then
    print_status "Build configuration can be restored from backup if needed"
fi
