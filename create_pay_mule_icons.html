<!DOCTYPE html>
<html>
<head>
    <title>Pay Mule Icon Creator</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .preview {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
        }
        canvas { 
            border: 2px solid #ddd; 
            border-radius: 10px;
            margin: 10px;
        }
        .download-section {
            margin: 20px 0;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
        }
        .download-btn {
            background: #228B22;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
            display: inline-block;
        }
        .download-btn:hover {
            background: #1e7b1e;
        }
        .size-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .size-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇿🇲 Pay Mule Icon Creator</h1>
        <p>Generate all required Pay Mule wallet icons for your Flutter app</p>
        
        <div class="preview">
            <h3>Preview</h3>
            <canvas id="previewCanvas" width="200" height="200"></canvas>
            <p>Pay Mule Wallet Icon with Zambian Colors</p>
        </div>
        
        <div class="download-section">
            <h3>📱 Generate All Icon Sizes</h3>
            <p>Click the button below to generate and download all required icon sizes for Android and iOS:</p>
            <button class="download-btn" onclick="generateAllIcons()">
                🎨 Generate All Pay Mule Icons
            </button>
        </div>
        
        <div class="instructions">
            <h4>📋 Installation Instructions</h4>
            <ol>
                <li><strong>Generate Icons:</strong> Click the button above to create all icon sizes</li>
                <li><strong>Android Icons:</strong> Place in <code>android/app/src/main/res/mipmap-*/ic_launcher.png</code></li>
                <li><strong>iOS Icons:</strong> Place in <code>ios/Runner/Assets.xcassets/AppIcon.appiconset/</code></li>
                <li><strong>Test:</strong> Run <code>flutter clean && flutter run</code></li>
            </ol>
        </div>
        
        <div id="generatedIcons" class="size-grid" style="display: none;">
            <!-- Generated icons will appear here -->
        </div>
    </div>

    <script>
        // Pay Mule icon drawing function
        function drawPayMuleIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 512; // Base size 512
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Colors based on your design
            const black = '#000000';
            const white = '#FFFFFF';
            const lightGray = '#F5F5F5';
            const zambiaGreen = '#228B22';  // Zambian flag green
            const zambiaOrange = '#FF8C00'; // Zambian flag orange
            const zambiaRed = '#DC143C';    // Zambian flag red
            
            // Scale helper function
            function s(value) {
                return value * scale;
            }
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#f8f8f8');
            gradient.addColorStop(1, '#e8e8e8');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Top card/stripe (black rounded rectangle)
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(s(80), s(60), s(352), s(80), s(40));
            ctx.fill();
            
            // White stripe in top card
            ctx.fillStyle = white;
            ctx.beginPath();
            ctx.roundRect(s(120), s(80), s(272), s(40), s(20));
            ctx.fill();
            
            // Main wallet body (black rounded rectangle)
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(s(80), s(160), s(320), s(240), s(40));
            ctx.fill();
            
            // Wallet interior (light area)
            ctx.fillStyle = lightGray;
            ctx.beginPath();
            ctx.roundRect(s(110), s(190), s(260), s(180), s(20));
            ctx.fill();
            
            // Card handle/tab on the right
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(s(380), s(220), s(60), s(100), s(20));
            ctx.fill();
            
            // Card handle interior (white)
            ctx.fillStyle = white;
            ctx.beginPath();
            ctx.roundRect(s(395), s(240), s(30), s(60), s(10));
            ctx.fill();
            
            // Zambian flag colors (horizontal stripes)
            const colorWidth = s(40);
            const colorHeight = s(12);
            const colorY = s(210);
            const startX = s(130);
            
            // Green stripe
            ctx.fillStyle = zambiaGreen;
            ctx.beginPath();
            ctx.roundRect(startX, colorY, colorWidth, colorHeight, s(6));
            ctx.fill();
            
            // Orange stripe
            ctx.fillStyle = zambiaOrange;
            ctx.beginPath();
            ctx.roundRect(startX + s(50), colorY, colorWidth, colorHeight, s(6));
            ctx.fill();
            
            // Red stripe
            ctx.fillStyle = zambiaRed;
            ctx.beginPath();
            ctx.roundRect(startX + s(100), colorY, colorWidth, colorHeight, s(6));
            ctx.fill();
            
            // "PM" text (Pay Mule)
            ctx.fillStyle = black;
            ctx.font = `bold ${s(80)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('PM', s(240), s(300));
        }
        
        // Icon size configurations
        const androidSizes = [
            { name: 'mdpi', size: 48, folder: 'mipmap-mdpi' },
            { name: 'hdpi', size: 72, folder: 'mipmap-hdpi' },
            { name: 'xhdpi', size: 96, folder: 'mipmap-xhdpi' },
            { name: 'xxhdpi', size: 144, folder: 'mipmap-xxhdpi' },
            { name: 'xxxhdpi', size: 192, folder: 'mipmap-xxxhdpi' }
        ];
        
        const iosSizes = [
            { name: 'Icon-App-20x20@1x', size: 20 },
            { name: 'Icon-App-20x20@2x', size: 40 },
            { name: 'Icon-App-20x20@3x', size: 60 },
            { name: 'Icon-App-29x29@1x', size: 29 },
            { name: 'Icon-App-29x29@2x', size: 58 },
            { name: 'Icon-App-29x29@3x', size: 87 },
            { name: 'Icon-App-40x40@1x', size: 40 },
            { name: 'Icon-App-40x40@2x', size: 80 },
            { name: 'Icon-App-40x40@3x', size: 120 },
            { name: 'Icon-App-60x60@2x', size: 120 },
            { name: 'Icon-App-60x60@3x', size: 180 },
            { name: 'Icon-App-76x76@1x', size: 76 },
            { name: 'Icon-App-76x76@2x', size: 152 },
            { name: 'Icon-App-83.5x83.5@2x', size: 167 },
            { name: 'Icon-App-1024x1024@1x', size: 1024 }
        ];
        
        // Generate and download a single icon
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Generate all icons
        function generateAllIcons() {
            const container = document.getElementById('generatedIcons');
            container.innerHTML = '';
            container.style.display = 'grid';
            
            // Generate Android icons
            androidSizes.forEach(config => {
                const canvas = document.createElement('canvas');
                canvas.width = config.size;
                canvas.height = config.size;
                drawPayMuleIcon(canvas, config.size);
                
                const item = createIconItem(canvas, `Android ${config.name}`, config.size, `ic_launcher_${config.name}.png`);
                container.appendChild(item);
            });
            
            // Generate iOS icons
            iosSizes.forEach(config => {
                const canvas = document.createElement('canvas');
                canvas.width = config.size;
                canvas.height = config.size;
                drawPayMuleIcon(canvas, config.size);
                
                const item = createIconItem(canvas, `iOS ${config.name}`, config.size, `${config.name}.png`);
                container.appendChild(item);
            });
        }
        
        // Create icon display item
        function createIconItem(canvas, name, size, filename) {
            const item = document.createElement('div');
            item.className = 'size-item';
            
            // Create smaller display canvas
            const displayCanvas = document.createElement('canvas');
            const displaySize = Math.min(size, 80);
            displayCanvas.width = displaySize;
            displayCanvas.height = displaySize;
            
            const displayCtx = displayCanvas.getContext('2d');
            displayCtx.drawImage(canvas, 0, 0, displaySize, displaySize);
            
            item.innerHTML = `
                <h5>${name}</h5>
                <p>${size}x${size}px</p>
                <button class="download-btn" onclick="downloadIcon(arguments[0], '${filename}')" style="font-size: 12px; padding: 8px 12px;">
                    Download
                </button>
            `;
            
            // Insert canvas before the button
            item.insertBefore(displayCanvas, item.children[2]);
            
            // Fix the download button click handler
            const btn = item.querySelector('.download-btn');
            btn.onclick = () => downloadIcon(canvas, filename);
            
            return item;
        }
        
        // Initialize preview
        window.onload = function() {
            const previewCanvas = document.getElementById('previewCanvas');
            drawPayMuleIcon(previewCanvas, 200);
        };
    </script>
</body>
</html>
