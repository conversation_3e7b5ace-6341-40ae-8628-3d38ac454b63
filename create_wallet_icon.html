<!DOCTYPE html>
<html>
<head>
    <title>Pay Mule Icon Generator</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas { 
            border: 2px solid #ddd; 
            border-radius: 10px;
            margin: 20px 0;
        }
        .download-btn {
            background: #228B22;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        .download-btn:hover {
            background: #1e7b1e;
        }
        .instructions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .zambia-colors {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .color-box {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            border: 2px solid #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇿🇲 Pay Mule Icon Generator</h1>
        <p>Generate high-quality app icons for Pay Mule with Zambian color scheme</p>
        
        <div class="zambia-colors">
            <div class="color-box" style="background: #228B22;" title="Zambia Green"></div>
            <div class="color-box" style="background: #FF8C00;" title="Zambia Orange"></div>
            <div class="color-box" style="background: #DC143C;" title="Zambia Red"></div>
            <div class="color-box" style="background: #000000;" title="Black"></div>
        </div>
        
        <canvas id="iconCanvas" width="1024" height="1024"></canvas>
        
        <div>
            <button class="download-btn" onclick="downloadIcon(1024)">Download 1024x1024 (iOS Store)</button>
            <button class="download-btn" onclick="downloadIcon(512)">Download 512x512 (Base)</button>
            <button class="download-btn" onclick="downloadIcon(192)">Download 192x192 (Android XXXHDPI)</button>
        </div>
        
        <div class="instructions">
            <h3>📋 Instructions for Pay Mule Production:</h3>
            <ol>
                <li><strong>Download the 1024x1024 icon</strong> using the button above</li>
                <li><strong>Use an online icon generator:</strong>
                    <ul>
                        <li>Go to <a href="https://appicon.co/" target="_blank">appicon.co</a> or <a href="https://makeappicon.com/" target="_blank">makeappicon.com</a></li>
                        <li>Upload your downloaded 1024x1024 PNG</li>
                        <li>Download the complete icon sets for iOS and Android</li>
                    </ul>
                </li>
                <li><strong>Replace the placeholder files</strong> in your Pay Mule project:
                    <ul>
                        <li>Android: <code>android/app/src/main/res/mipmap-*/ic_launcher.png</code></li>
                        <li>iOS: <code>ios/Runner/Assets.xcassets/AppIcon.appiconset/*.png</code></li>
                    </ul>
                </li>
                <li><strong>Test the icons:</strong>
                    <ul>
                        <li>Run: <code>flutter clean && flutter pub get</code></li>
                        <li>Run: <code>flutter test integration_test/icon_validation_test.dart</code></li>
                        <li>Build and test on device: <code>flutter run</code></li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('iconCanvas');
        const ctx = canvas.getContext('2d');
        
        // Zambia colors
        const zambiaGreen = '#228B22';
        const zambiaOrange = '#FF8C00';
        const zambiaRed = '#DC143C';
        const black = '#000000';
        const white = '#FFFFFF';
        const lightGray = '#F5F5F5';
        
        function drawWalletIcon() {
            // Clear canvas
            ctx.clearRect(0, 0, 1024, 1024);
            
            // Background with subtle Zambia gradient
            const gradient = ctx.createLinearGradient(0, 0, 1024, 1024);
            gradient.addColorStop(0, zambiaGreen + '20');
            gradient.addColorStop(0.5, zambiaOrange + '20');
            gradient.addColorStop(1, zambiaRed + '20');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 1024, 1024);
            
            // Main wallet shape (top part)
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(80, 120, 440, 120, 40);
            ctx.fill();
            
            // Card slot (white stripe)
            ctx.fillStyle = white;
            ctx.beginPath();
            ctx.roundRect(120, 160, 280, 40, 20);
            ctx.fill();
            
            // Main wallet body
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(80, 280, 400, 280, 40);
            ctx.fill();
            
            // Wallet interior
            ctx.fillStyle = lightGray;
            ctx.beginPath();
            ctx.roundRect(120, 320, 320, 200, 20);
            ctx.fill();
            
            // Card handle/tab
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(440, 360, 80, 120, 20);
            ctx.fill();
            
            // Card handle interior
            ctx.fillStyle = white;
            ctx.beginPath();
            ctx.roundRect(460, 380, 40, 80, 10);
            ctx.fill();
            
            // Add Zambia accent (small colored elements)
            ctx.fillStyle = zambiaGreen;
            ctx.beginPath();
            ctx.roundRect(140, 340, 60, 20, 10);
            ctx.fill();
            
            ctx.fillStyle = zambiaOrange;
            ctx.beginPath();
            ctx.roundRect(220, 340, 60, 20, 10);
            ctx.fill();
            
            ctx.fillStyle = zambiaRed;
            ctx.beginPath();
            ctx.roundRect(300, 340, 60, 20, 10);
            ctx.fill();
            
            // Add "PM" text for Pay Mule
            ctx.fillStyle = black;
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('PM', 280, 450);
        }
        
        function downloadIcon(size) {
            // Create a new canvas for the specific size
            const downloadCanvas = document.createElement('canvas');
            downloadCanvas.width = size;
            downloadCanvas.height = size;
            const downloadCtx = downloadCanvas.getContext('2d');
            
            // Scale and draw the icon
            downloadCtx.scale(size / 1024, size / 1024);
            downloadCtx.drawImage(canvas, 0, 0);
            
            // Download
            const link = document.createElement('a');
            link.download = `pay_mule_icon_${size}x${size}.png`;
            link.href = downloadCanvas.toDataURL();
            link.click();
        }
        
        // Draw the icon when page loads
        drawWalletIcon();
    </script>
</body>
</html>
