#!/bin/bash

# 🇿🇲 PAY MULE ZAMBIA - PRODUCTION CHECKLIST DEMONSTRATION
# 
# Demonstrates the production checklist runner with various scenarios
# Shows different test configurations and report formats

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}🇿🇲 PAY MULE ZAMBIA - PRODUCTION CHECKLIST DEMONSTRATION${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
}

print_scenario() {
    echo -e "${BLUE}📋 SCENARIO: $1${NC}"
    echo -e "${BLUE}────────────────────────────────────────────────────────────${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

run_checklist_scenario() {
    local scenario_name="$1"
    local command="$2"
    
    print_scenario "$scenario_name"
    print_info "Command: $command"
    echo ""
    
    if eval "$command"; then
        echo ""
        print_success "Scenario '$scenario_name' completed successfully"
    else
        echo ""
        echo -e "${RED}❌ Scenario '$scenario_name' failed${NC}"
    fi
    
    echo ""
    echo "Press Enter to continue to next scenario..."
    read
    echo ""
}

main() {
    print_header
    
    echo "This demonstration shows various production checklist scenarios."
    echo "Each scenario tests different aspects of the Pay Mule Zambia system."
    echo ""
    echo "Press Enter to start the demonstration..."
    read
    echo ""
    
    # Scenario 1: Basic Production Validation
    run_checklist_scenario \
        "Basic Production Validation" \
        "dart lib/scripts/run_zambia_prod_checklist.dart --required-tests=\"mtn_live_tx,airtel_balance_check,zesco_payment\" --report-format=console"
    
    # Scenario 2: Full System Validation with PDF Report
    run_checklist_scenario \
        "Full System Validation with PDF Report" \
        "dart lib/scripts/run_zambia_prod_checklist.dart --required-tests=\"mtn_live_tx,airtel_balance_check,zesco_payment,nwsc_payment,agent_discovery,offline_sync,security_alerts\" --ussd-flow=\"*211*1*26097XXXXXX*5.0#\" --agent-verification=\"Chipata Market\" --failure-mode=\"normal\" --report-format=pdf"
    
    # Scenario 3: Rainy Season Resilience Testing
    run_checklist_scenario \
        "Rainy Season Resilience Testing" \
        "dart lib/scripts/run_zambia_prod_checklist.dart --required-tests=\"offline_sync,security_alerts\" --failure-mode=\"rainy_season_sim\" --report-format=html"
    
    # Scenario 4: Utility Payment Focus
    run_checklist_scenario \
        "Utility Payment Focus Testing" \
        "dart lib/scripts/run_zambia_prod_checklist.dart --required-tests=\"zesco_payment,nwsc_payment\" --agent-verification=\"Lusaka Central\" --report-format=json"
    
    # Scenario 5: Mobile Money Integration
    run_checklist_scenario \
        "Mobile Money Integration Testing" \
        "dart lib/scripts/run_zambia_prod_checklist.dart --required-tests=\"mtn_live_tx,airtel_balance_check\" --ussd-flow=\"*432*1*26097XXXXXX*10.0#\" --report-format=console"
    
    # Scenario 6: Agent System Validation
    run_checklist_scenario \
        "Agent System Validation" \
        "dart lib/scripts/run_zambia_prod_checklist.dart --required-tests=\"agent_discovery\" --agent-verification=\"Chipata Market\" --report-format=console"
    
    # Scenario 7: Network Outage Simulation
    run_checklist_scenario \
        "Network Outage Simulation" \
        "dart lib/scripts/run_zambia_prod_checklist.dart --required-tests=\"offline_sync\" --failure-mode=\"network_outage\" --report-format=console"
    
    echo -e "${GREEN}🎉 PRODUCTION CHECKLIST DEMONSTRATION COMPLETED${NC}"
    echo ""
    echo "All scenarios have been demonstrated. The production checklist runner"
    echo "provides comprehensive validation for:"
    echo ""
    echo "✅ Live API integrations (MTN, Airtel, ZESCO, NWSC)"
    echo "✅ USSD flow validation"
    echo "✅ Agent system verification"
    echo "✅ Failure mode simulation"
    echo "✅ Multiple report formats (console, PDF, JSON, HTML)"
    echo ""
    echo "Generated reports can be found in the current directory."
    echo ""
    
    # Show generated reports
    echo "Generated reports:"
    find . -name "zambia_production_checklist_*" -type f 2>/dev/null | while read -r file; do
        if [ -f "$file" ]; then
            echo "  📄 $file"
        fi
    done
    
    echo ""
    echo -e "${CYAN}🇿🇲 Pay Mule Zambia is ready for production deployment!${NC}"
}

# Check if Dart is available
if ! command -v dart &> /dev/null; then
    echo -e "${RED}❌ Dart SDK not found. Please install Dart SDK to run the demonstration.${NC}"
    echo "Visit: https://dart.dev/get-dart"
    exit 1
fi

# Check if script exists
if [ ! -f "lib/scripts/run_zambia_prod_checklist.dart" ]; then
    echo -e "${RED}❌ Production checklist script not found.${NC}"
    echo "Please ensure you're running this from the project root directory."
    exit 1
fi

# Run the demonstration
main
