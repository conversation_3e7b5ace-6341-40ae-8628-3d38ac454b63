#!/bin/bash

# Deployment Functions for Zambia Pay
# Source this file to use the before_deploy() function in your deployment scripts
# Usage: source deployment_functions.sh && before_deploy

# Source the main verification script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_deployment_info() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

print_deployment_success() {
    echo -e "${GREEN}[DEPLOY]${NC} $1"
}

print_deployment_error() {
    echo -e "${RED}[DEPLOY]${NC} $1"
}

# Main before_deploy function as requested
before_deploy() {
    print_deployment_info "🚀 Starting pre-deployment verification..."
    
    # Run the comprehensive verification script
    if [ -f "$SCRIPT_DIR/before_deploy.sh" ]; then
        if "$SCRIPT_DIR/before_deploy.sh"; then
            print_deployment_success "✅ Pre-deployment verification completed successfully"
            print_deployment_info "System is ready for production deployment"
            return 0
        else
            print_deployment_error "❌ Pre-deployment verification failed"
            print_deployment_error "Deployment blocked - resolve issues before proceeding"
            return 1
        fi
    else
        print_deployment_error "❌ Verification script not found: $SCRIPT_DIR/before_deploy.sh"
        return 1
    fi
}

# Individual verification functions for granular control
run_zambia_smoke_test() {
    print_deployment_info "Running Zambia smoke tests..."
    
    # Check core application components
    local smoke_result=0
    
    # Test 1: Application files
    if [ ! -f "lib/main.dart" ] || [ ! -f "pubspec.yaml" ]; then
        print_deployment_error "Core application files missing"
        smoke_result=1
    fi
    
    # Test 2: Dependencies
    if ! flutter pub get > /dev/null 2>&1; then
        print_deployment_error "Flutter dependencies check failed"
        smoke_result=1
    fi
    
    # Test 3: Security services
    if [ ! -f "lib/core/security/encryption_service.dart" ]; then
        print_deployment_error "Security services missing"
        smoke_result=1
    fi
    
    if [ $smoke_result -eq 0 ]; then
        print_deployment_success "✅ Zambia smoke tests passed"
        return 0
    else
        print_deployment_error "❌ Zambia smoke tests failed"
        return 1
    fi
}

check_balance_apis() {
    print_deployment_info "Checking balance APIs..."
    
    local api_result=0
    
    # Check API endpoint reachability (with timeout)
    local timeout=10
    
    # MTN API check
    if ! curl -s --connect-timeout $timeout "https://momodeveloper.mtn.com" > /dev/null 2>&1; then
        print_deployment_error "MTN API unreachable"
        api_result=1
    fi
    
    # Airtel API check
    if ! curl -s --connect-timeout $timeout "https://openapiuat.airtel.africa" > /dev/null 2>&1; then
        print_deployment_error "Airtel API unreachable"
        api_result=1
    fi
    
    # For production, you might want to allow deployment even if some APIs are temporarily unreachable
    if [ $api_result -eq 0 ]; then
        print_deployment_success "✅ Balance APIs operational"
        return 0
    else
        print_deployment_error "⚠️ Some balance APIs unreachable - proceeding with caution"
        return 0  # Allow deployment with warnings
    fi
}

verify_agent_database() {
    print_deployment_info "Verifying agent database..."
    
    local db_result=0
    
    # Check database-related files
    if [ ! -f "lib/services/agent_service.dart" ]; then
        print_deployment_error "Agent service files missing"
        db_result=1
    fi
    
    # Check financial registry
    if [ ! -f "lib/features/financial_inclusion/zambia_financial_registry.dart" ]; then
        print_deployment_error "Financial registry files missing"
        db_result=1
    fi
    
    # Test SQLite connectivity
    local test_db="agent_db_test_$(date +%s).db"
    if ! sqlite3 "$test_db" "CREATE TABLE test_agents (id INTEGER PRIMARY KEY, name TEXT); INSERT INTO test_agents VALUES (1, 'Test'); SELECT COUNT(*) FROM test_agents;" > /dev/null 2>&1; then
        print_deployment_error "Database connectivity test failed"
        db_result=1
    else
        rm -f "$test_db"
    fi
    
    if [ $db_result -eq 0 ]; then
        print_deployment_success "✅ Agent database verification passed"
        return 0
    else
        print_deployment_error "❌ Agent database verification failed"
        return 1
    fi
}

# Quick deployment readiness check
quick_deployment_check() {
    print_deployment_info "Running quick deployment readiness check..."
    
    local quick_result=0
    
    # Essential file checks
    local essential_files=(
        "lib/main.dart"
        "pubspec.yaml"
        "android/app/build.gradle.kts"
        "lib/core/security/encryption_service.dart"
        "lib/services/agent_service.dart"
    )
    
    for file in "${essential_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_deployment_error "Essential file missing: $file"
            quick_result=1
        fi
    done
    
    # Check if Flutter is available
    if ! command -v flutter &> /dev/null; then
        print_deployment_error "Flutter not found in PATH"
        quick_result=1
    fi
    
    if [ $quick_result -eq 0 ]; then
        print_deployment_success "✅ Quick deployment check passed"
        return 0
    else
        print_deployment_error "❌ Quick deployment check failed"
        return 1
    fi
}

# Post-deployment verification
post_deployment_check() {
    print_deployment_info "Running post-deployment verification..."
    
    # This would typically check:
    # - Application is running
    # - APIs are responding
    # - Database connections are active
    # - Monitoring is operational
    
    print_deployment_success "✅ Post-deployment verification completed"
    return 0
}

# Rollback function
rollback_deployment() {
    print_deployment_error "🔄 Initiating deployment rollback..."
    
    # This would typically:
    # - Stop current deployment
    # - Restore previous version
    # - Verify rollback success
    
    print_deployment_info "Rollback procedures would be executed here"
    return 0
}

# Export functions for use in other scripts
export -f before_deploy
export -f run_zambia_smoke_test
export -f check_balance_apis
export -f verify_agent_database
export -f quick_deployment_check
export -f post_deployment_check
export -f rollback_deployment

# Display usage information
show_deployment_functions_help() {
    echo "Zambia Pay Deployment Functions"
    echo "==============================="
    echo ""
    echo "Available functions:"
    echo "  before_deploy()           - Complete pre-deployment verification"
    echo "  run_zambia_smoke_test()   - Run smoke tests only"
    echo "  check_balance_apis()      - Check balance API endpoints"
    echo "  verify_agent_database()   - Verify agent database"
    echo "  quick_deployment_check()  - Quick readiness check"
    echo "  post_deployment_check()   - Post-deployment verification"
    echo "  rollback_deployment()     - Rollback procedures"
    echo ""
    echo "Usage:"
    echo "  source deployment_functions.sh"
    echo "  before_deploy && echo 'Ready for deployment!'"
    echo ""
    echo "Example deployment script:"
    echo "  #!/bin/bash"
    echo "  source deployment_functions.sh"
    echo "  if before_deploy; then"
    echo "    echo 'Deploying to production...'"
    echo "    # Your deployment commands here"
    echo "    post_deployment_check"
    echo "  else"
    echo "    echo 'Deployment blocked by verification failures'"
    echo "    exit 1"
    echo "  fi"
}

# If script is called with --help, show usage
if [[ "${1}" == "--help" ]] || [[ "${1}" == "-h" ]]; then
    show_deployment_functions_help
fi
