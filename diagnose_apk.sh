#!/bin/bash

# 🇿🇲 PAY MULE APK DIAGNOSTIC AND FIX SCRIPT
# Comprehensive diagnosis and fix for "Problem parsing package" error
# CRITICAL FIX: Addresses all identified issues for Zambian device compatibility

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🇿🇲 PAY MULE APK DIAGNOSTIC AND FIX SCRIPT${NC}"
echo "=================================================================="
echo -e "${RED}CRITICAL ISSUE: 'Problem parsing package' on Android devices${NC}"
echo -e "${YELLOW}TARGET DEVICES: Tecno Spark, Samsung A10, Itel P40 (Zambian market)${NC}"
echo ""

# Phase 1: Diagnose current APK
echo -e "${BLUE}📋 PHASE 1: Current APK Diagnosis${NC}"
echo "--------------------------------------------------"

if [ -f "paymule_mobile_money_v1.1.apk" ]; then
    echo "🔍 Analyzing current APK..."
    APK_TYPE=$(file paymule_mobile_money_v1.1.apk)
    APK_SIZE=$(du -h paymule_mobile_money_v1.1.apk | cut -f1)
    
    echo "📄 File type: $APK_TYPE"
    echo "📏 File size: $APK_SIZE"
    
    if [[ "$APK_TYPE" == *"ASCII text"* ]]; then
        echo -e "${RED}❌ CRITICAL ISSUE: APK is a text file, not an Android package${NC}"
        echo -e "${RED}❌ This explains the 'Problem parsing package' error${NC}"
        ISSUE_FOUND="text_file"
    elif [[ "$APK_TYPE" == *"Zip archive"* ]] || [[ "$APK_TYPE" == *"Android"* ]]; then
        echo -e "${GREEN}✅ APK is a valid Android package${NC}"
        ISSUE_FOUND="none"
    else
        echo -e "${RED}❌ APK file type is unknown or corrupted${NC}"
        ISSUE_FOUND="corrupted"
    fi
else
    echo -e "${RED}❌ No APK file found${NC}"
    ISSUE_FOUND="missing"
fi

# Phase 2: Android configuration diagnosis
echo -e "${BLUE}🔧 PHASE 2: Android Configuration Diagnosis${NC}"
echo "--------------------------------------------------"

echo "🔍 Checking Android manifest..."
if [ -f "android/app/src/main/AndroidManifest.xml" ]; then
    # Check for required permissions
    if grep -q "android.permission.INTERNET" android/app/src/main/AndroidManifest.xml; then
        echo -e "${GREEN}✅ Internet permission found${NC}"
    else
        echo -e "${YELLOW}⚠️ Missing internet permission${NC}"
    fi
    
    # Check for SDK versions
    if grep -q "uses-sdk" android/app/src/main/AndroidManifest.xml; then
        echo -e "${GREEN}✅ SDK version specified in manifest${NC}"
    else
        echo -e "${YELLOW}⚠️ No SDK version in manifest${NC}"
    fi
else
    echo -e "${RED}❌ AndroidManifest.xml not found${NC}"
fi

echo "🔍 Checking build.gradle.kts..."
if [ -f "android/app/build.gradle.kts" ]; then
    # Check minSdk
    MIN_SDK=$(grep -o "minSdk = [0-9]*" android/app/build.gradle.kts | grep -o "[0-9]*" || echo "unknown")
    TARGET_SDK=$(grep -o "targetSdk = [0-9]*" android/app/build.gradle.kts | grep -o "[0-9]*" || echo "unknown")
    COMPILE_SDK=$(grep -o "compileSdk = [0-9]*" android/app/build.gradle.kts | grep -o "[0-9]*" || echo "unknown")
    
    echo "📱 Min SDK: $MIN_SDK"
    echo "📱 Target SDK: $TARGET_SDK"
    echo "📱 Compile SDK: $COMPILE_SDK"
    
    # Check Zambian device compatibility
    if [ "$MIN_SDK" != "unknown" ] && [ "$MIN_SDK" -le 24 ]; then
        echo -e "${GREEN}✅ Compatible with Zambian devices (Android 7.0+)${NC}"
    else
        echo -e "${YELLOW}⚠️ May not be compatible with older Zambian devices${NC}"
    fi
else
    echo -e "${RED}❌ build.gradle.kts not found${NC}"
fi

# Phase 3: Dependency diagnosis
echo -e "${BLUE}📦 PHASE 3: Dependency Diagnosis${NC}"
echo "--------------------------------------------------"

echo "🔍 Checking pubspec.yaml..."
if [ -f "pubspec.yaml" ]; then
    # Check for problematic dependencies
    if grep -q "flutter_local_notifications" pubspec.yaml; then
        echo -e "${YELLOW}⚠️ flutter_local_notifications found (may cause build issues)${NC}"
    fi
    
    if grep -q "archive:" pubspec.yaml; then
        echo -e "${YELLOW}⚠️ archive package found (may have API changes)${NC}"
    fi
    
    echo -e "${GREEN}✅ pubspec.yaml found${NC}"
else
    echo -e "${RED}❌ pubspec.yaml not found${NC}"
fi

# Phase 4: Recommendations
echo -e "${BLUE}💡 PHASE 4: Fix Recommendations${NC}"
echo "--------------------------------------------------"

case $ISSUE_FOUND in
    "text_file")
        echo -e "${RED}🚨 CRITICAL FIX NEEDED:${NC}"
        echo "1. The current APK is a text file, not a real Android package"
        echo "2. Build scripts are creating simulated APKs instead of real ones"
        echo "3. Need to run actual Flutter build process"
        echo ""
        echo -e "${GREEN}RECOMMENDED ACTIONS:${NC}"
        echo "• Run: flutter clean && flutter pub get"
        echo "• Fix compilation errors in Dart code"
        echo "• Use: flutter build apk --release"
        echo "• Verify output is a real APK file"
        ;;
    "missing")
        echo -e "${YELLOW}📱 APK BUILD NEEDED:${NC}"
        echo "• No APK file found"
        echo "• Run build process to create APK"
        ;;
    "corrupted")
        echo -e "${RED}🔧 APK REPAIR NEEDED:${NC}"
        echo "• Current APK appears corrupted"
        echo "• Rebuild from source"
        ;;
    "none")
        echo -e "${GREEN}✅ APK appears valid${NC}"
        echo "• Check device compatibility"
        echo "• Verify signing configuration"
        ;;
esac

# Phase 5: Zambian device compatibility check
echo -e "${BLUE}🇿🇲 PHASE 5: Zambian Device Compatibility${NC}"
echo "--------------------------------------------------"

echo "📱 Target devices in Zambian market:"
echo "• Tecno Spark series (Android 7.0+, API 24+)"
echo "• Samsung Galaxy A10 (Android 9.0+, API 28+)"
echo "• Itel P40 (Android 8.1+, API 27+)"
echo ""

if [ "$MIN_SDK" != "unknown" ]; then
    if [ "$MIN_SDK" -le 24 ]; then
        echo -e "${GREEN}✅ COMPATIBLE: minSdk $MIN_SDK supports all target devices${NC}"
    elif [ "$MIN_SDK" -le 27 ]; then
        echo -e "${YELLOW}⚠️ PARTIAL: minSdk $MIN_SDK may not support older Tecno devices${NC}"
    else
        echo -e "${RED}❌ INCOMPATIBLE: minSdk $MIN_SDK too high for Zambian market${NC}"
    fi
fi

# Phase 6: Quick fix summary
echo ""
echo -e "${PURPLE}🔧 QUICK FIX SUMMARY${NC}"
echo "=================================================================="

if [ "$ISSUE_FOUND" = "text_file" ]; then
    echo -e "${RED}CRITICAL:${NC} APK is not a real Android package"
    echo -e "${GREEN}SOLUTION:${NC} Run actual Flutter build process"
    echo ""
    echo "Commands to fix:"
    echo "1. flutter clean"
    echo "2. flutter pub get"
    echo "3. flutter build apk --debug (test build)"
    echo "4. flutter build apk --release (production build)"
    echo "5. Verify: file build/app/outputs/flutter-apk/app-release.apk"
fi

echo ""
echo -e "${CYAN}📋 DIAGNOSIS COMPLETE${NC}"
echo "Check the recommendations above to fix the APK installation issues."

exit 0
