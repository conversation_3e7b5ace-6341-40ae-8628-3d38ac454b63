#!/bin/bash

# Example Deployment Script for Zambia Pay
# Demonstrates how to use the before_deploy() function

set -e

# Source the deployment functions
source ./deployment_functions.sh

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Main deployment function
deploy_to_production() {
    echo "🚀 Zambia Pay Production Deployment"
    echo "===================================="
    echo
    
    print_step "1. Running pre-deployment verification..."
    
    # Use the before_deploy function
    if before_deploy; then
        print_success "Pre-deployment verification passed!"
        echo
        
        print_step "2. Building production APK..."
        # This would run your APK build
        echo "   📱 Building production APK (simulated)..."
        sleep 2
        print_success "APK build completed"
        echo
        
        print_step "3. Deploying to production environment..."
        # This would deploy your application
        echo "   🌐 Deploying to production servers (simulated)..."
        sleep 2
        print_success "Deployment completed"
        echo
        
        print_step "4. Running post-deployment checks..."
        if post_deployment_check; then
            print_success "Post-deployment verification passed!"
        else
            print_error "Post-deployment verification failed!"
            return 1
        fi
        echo
        
        print_success "🎉 PRODUCTION DEPLOYMENT SUCCESSFUL!"
        echo
        echo "Next steps:"
        echo "  - Monitor application performance"
        echo "  - Check error logs"
        echo "  - Verify user transactions"
        echo "  - Activate production alerts"
        
    else
        print_error "Pre-deployment verification failed!"
        print_error "Deployment aborted - please fix issues and try again"
        echo
        echo "To debug:"
        echo "  - Check the verification logs"
        echo "  - Review failed components"
        echo "  - Fix issues and re-run verification"
        return 1
    fi
}

# Individual test functions for debugging
test_individual_components() {
    echo "🔍 Testing Individual Components"
    echo "==============================="
    echo
    
    print_step "Testing smoke tests..."
    if run_zambia_smoke_test; then
        print_success "Smoke tests passed"
    else
        print_error "Smoke tests failed"
    fi
    echo
    
    print_step "Testing balance APIs..."
    if check_balance_apis; then
        print_success "Balance APIs operational"
    else
        print_error "Balance APIs have issues"
    fi
    echo
    
    print_step "Testing agent database..."
    if verify_agent_database; then
        print_success "Agent database verified"
    else
        print_error "Agent database verification failed"
    fi
    echo
}

# Quick check function
quick_check() {
    echo "⚡ Quick Deployment Check"
    echo "========================"
    echo
    
    if quick_deployment_check; then
        print_success "Quick check passed - system appears ready"
    else
        print_error "Quick check failed - system not ready"
        return 1
    fi
}

# Show help
show_help() {
    echo "Zambia Pay Deployment Script"
    echo "============================"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  deploy     - Full production deployment with verification"
    echo "  test       - Test individual components"
    echo "  quick      - Quick deployment readiness check"
    echo "  help       - Show this help message"
    echo
    echo "Examples:"
    echo "  $0 deploy    # Deploy to production"
    echo "  $0 test      # Test components individually"
    echo "  $0 quick     # Quick readiness check"
}

# Main script logic
case "${1:-deploy}" in
    "deploy")
        deploy_to_production
        ;;
    "test")
        test_individual_components
        ;;
    "quick")
        quick_check
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
