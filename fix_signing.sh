#!/bin/bash

# fix_signing.sh - Add V1 signing for compatibility with older Android devices
# Critical for devices running Android 6.0 and below

set -e

# Default values
APK_FILE=""
V1_SIGNING="enabled"
KEYSTORE_FILE="zm_prod_key.jks"
KEYSTORE_ALIAS="paymule_zambia"
KEYSTORE_PASSWORD="PayMuleZM2024!"
KEY_PASSWORD="PayMuleZM2024!"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --apk=FILE              APK file to sign (required)"
    echo "  --v1-signing=enabled    Enable V1 signing (default: enabled)"
    echo "  --keystore=FILE         Keystore file (default: zm_prod_key.jks)"
    echo "  --alias=ALIAS           Keystore alias (default: paymule_zambia)"
    echo "  --store-pass=PASS       Keystore password"
    echo "  --key-pass=PASS         Key password"
    echo "  --help                  Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --apk=paymule_mobile_money_v1.1.apk --v1-signing=enabled"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --apk=*)
            APK_FILE="${1#*=}"
            shift
            ;;
        --v1-signing=*)
            V1_SIGNING="${1#*=}"
            shift
            ;;
        --keystore=*)
            KEYSTORE_FILE="${1#*=}"
            shift
            ;;
        --alias=*)
            KEYSTORE_ALIAS="${1#*=}"
            shift
            ;;
        --store-pass=*)
            KEYSTORE_PASSWORD="${1#*=}"
            shift
            ;;
        --key-pass=*)
            KEY_PASSWORD="${1#*=}"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$APK_FILE" ]]; then
    print_error "APK file is required. Use --apk=FILE"
    show_usage
    exit 1
fi

if [[ ! -f "$APK_FILE" ]]; then
    print_error "APK file not found: $APK_FILE"
    exit 1
fi

print_status "🔐 Starting V1 signing process for Zambian device compatibility..."
print_status "APK File: $APK_FILE"
print_status "V1 Signing: $V1_SIGNING"
print_status "Keystore: $KEYSTORE_FILE"

# Create keystore if it doesn't exist
if [[ ! -f "$KEYSTORE_FILE" ]]; then
    print_warning "Keystore not found. Creating new keystore: $KEYSTORE_FILE"
    
    keytool -genkey \
        -v \
        -keystore "$KEYSTORE_FILE" \
        -alias "$KEYSTORE_ALIAS" \
        -keyalg RSA \
        -keysize 2048 \
        -validity 10000 \
        -storepass "$KEYSTORE_PASSWORD" \
        -keypass "$KEY_PASSWORD" \
        -dname "CN=PayMule Zambia, OU=Mobile Money, O=PayMule Ltd, L=Lusaka, ST=Lusaka, C=ZM"
    
    print_success "Keystore created successfully"
fi

# Create backup of original APK
BACKUP_APK="${APK_FILE%.apk}_backup.apk"
cp "$APK_FILE" "$BACKUP_APK"
print_status "Backup created: $BACKUP_APK"

# Create signed APK filename
SIGNED_APK="${APK_FILE%.apk}_v1_signed.apk"

# Check if we have apksigner (preferred) or jarsigner
if command -v apksigner &> /dev/null; then
    print_status "Using apksigner for V1 + V2 signing..."
    
    # Sign with both V1 and V2 schemes for maximum compatibility
    apksigner sign \
        --ks "$KEYSTORE_FILE" \
        --ks-key-alias "$KEYSTORE_ALIAS" \
        --ks-pass pass:"$KEYSTORE_PASSWORD" \
        --key-pass pass:"$KEY_PASSWORD" \
        --v1-signing-enabled \
        --v2-signing-enabled \
        --out "$SIGNED_APK" \
        "$APK_FILE"
    
    print_success "APK signed with V1 + V2 schemes: $SIGNED_APK"
    
elif command -v jarsigner &> /dev/null; then
    print_status "Using jarsigner for V1 signing..."
    
    # Copy APK for signing
    cp "$APK_FILE" "$SIGNED_APK"
    
    # Sign with jarsigner (V1 scheme)
    jarsigner \
        -verbose \
        -sigalg SHA256withRSA \
        -digestalg SHA-256 \
        -keystore "$KEYSTORE_FILE" \
        -storepass "$KEYSTORE_PASSWORD" \
        -keypass "$KEY_PASSWORD" \
        "$SIGNED_APK" \
        "$KEYSTORE_ALIAS"
    
    print_success "APK signed with V1 scheme: $SIGNED_APK"
    
else
    print_error "Neither apksigner nor jarsigner found. Please install Android SDK build-tools."
    exit 1
fi

# Verify the signature
print_status "Verifying APK signature..."

if command -v apksigner &> /dev/null; then
    apksigner verify --verbose "$SIGNED_APK"
    print_success "APK signature verification passed"
elif command -v jarsigner &> /dev/null; then
    jarsigner -verify -verbose -certs "$SIGNED_APK"
    print_success "APK signature verification passed"
fi

# Check APK alignment
print_status "Checking APK alignment..."
if command -v zipalign &> /dev/null; then
    if zipalign -c -v 4 "$SIGNED_APK"; then
        print_success "APK is properly aligned"
    else
        print_warning "APK is not aligned. Aligning now..."
        ALIGNED_APK="${SIGNED_APK%.apk}_aligned.apk"
        zipalign -v 4 "$SIGNED_APK" "$ALIGNED_APK"
        mv "$ALIGNED_APK" "$SIGNED_APK"
        print_success "APK aligned successfully"
    fi
else
    print_warning "zipalign not found. APK may not be optimally aligned."
fi

# Final verification for Zambian device compatibility
print_status "🇿🇲 Verifying Zambian device compatibility..."

# Check minimum SDK version
MIN_SDK=$(aapt dump badging "$SIGNED_APK" 2>/dev/null | grep "sdkVersion" | sed "s/.*sdkVersion:'\([0-9]*\)'.*/\1/")
if [[ -n "$MIN_SDK" && "$MIN_SDK" -le 21 ]]; then
    print_success "✅ Minimum SDK: $MIN_SDK (Compatible with Android 5.0+)"
else
    print_warning "⚠️  Minimum SDK: $MIN_SDK (May not work on older devices)"
fi

# Check package name
PACKAGE=$(aapt dump badging "$SIGNED_APK" 2>/dev/null | grep "package:" | sed "s/.*name='\([^']*\)'.*/\1/")
if [[ "$PACKAGE" == "com.zm.paymule" ]]; then
    print_success "✅ Package name: $PACKAGE (Zambian localized)"
else
    print_warning "⚠️  Package name: $PACKAGE (Not Zambian localized)"
fi

print_success "🎉 V1 signing completed successfully!"
print_status "Original APK: $APK_FILE"
print_status "Backup APK: $BACKUP_APK"
print_status "Signed APK: $SIGNED_APK"
print_status ""
print_status "📱 The signed APK is now compatible with:"
print_status "   • Android 5.0+ devices (API 21+)"
print_status "   • Older Zambian smartphones"
print_status "   • Devices requiring V1 signature scheme"
print_status ""
print_status "🚀 Ready for deployment in Zambia!"
