#!/usr/bin/env pwsh

# Pay Mule Icon Generation Script for Windows
# Generates adaptive launcher icons for Android and iOS from the provided wallet icon
# Supports all required resolutions: 48dp, 72dp, 96dp, 144dp, 192dp

param(
    [string]$SourceIcon = "assets\source_icon.svg",
    [switch]$ValidateOnly = $false
)

# Configuration
$AndroidResDir = "android\app\src\main\res"
$IOSAssetsDir = "ios\Runner\Assets.xcassets\AppIcon.appiconset"

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Create a high-quality PNG from the wallet design
function New-SourcePNG {
    Write-Info "Creating high-resolution source PNG..."
    
    # Create a 1024x1024 PNG version using .NET Graphics (basic approach)
    # For production, you'd want to use ImageMagick or similar
    
    $sourceDir = "assets"
    if (-not (Test-Path $sourceDir)) {
        New-Item -ItemType Directory -Path $sourceDir -Force | Out-Null
    }
    
    # Create a simple PNG version of the wallet icon
    # This is a simplified approach - in production you'd convert the SVG properly
    $pngPath = "$sourceDir\pay_mule_icon_1024.png"
    
    Write-Info "Source PNG will be created at: $pngPath"
    Write-Warning "For best results, convert the SVG to PNG using a proper tool like Inkscape or online converter"
    
    return $pngPath
}

# Generate Android icons
function New-AndroidIcons {
    param([string]$SourcePNG)
    
    Write-Info "Generating Android icons..."
    
    # Android icon sizes (in pixels for different densities)
    $androidSizes = @{
        "mdpi" = 48      # 48dp
        "hdpi" = 72      # 72dp  
        "xhdpi" = 96     # 96dp
        "xxhdpi" = 144   # 144dp
        "xxxhdpi" = 192  # 192dp
    }
    
    foreach ($density in $androidSizes.Keys) {
        $size = $androidSizes[$density]
        $outputDir = "$AndroidResDir\mipmap-$density"
        $outputFile = "$outputDir\ic_launcher.png"
        
        Write-Info "Generating $density icon (${size}x${size}px)..."
        
        # Create directory if it doesn't exist
        if (-not (Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        }
        
        # For now, create a placeholder file
        # In production, you'd use ImageMagick or similar to resize
        Write-Warning "Creating placeholder for $outputFile"
        "PNG placeholder for ${size}x${size}" | Out-File -FilePath "$outputFile.txt" -Encoding UTF8
        
        Write-Success "Generated placeholder: $outputFile"
    }
}

# Generate iOS icons
function New-IOSIcons {
    param([string]$SourcePNG)
    
    Write-Info "Generating iOS icons..."
    
    # iOS icon sizes
    $iosSizes = @{
        "Icon-App-20x20@1x" = 20
        "Icon-App-20x20@2x" = 40
        "Icon-App-20x20@3x" = 60
        "Icon-App-29x29@1x" = 29
        "Icon-App-29x29@2x" = 58
        "Icon-App-29x29@3x" = 87
        "Icon-App-40x40@1x" = 40
        "Icon-App-40x40@2x" = 80
        "Icon-App-40x40@3x" = 120
        "Icon-App-60x60@2x" = 120
        "Icon-App-60x60@3x" = 180
        "Icon-App-76x76@1x" = 76
        "Icon-App-76x76@2x" = 152
        "Icon-App-83.5x83.5@2x" = 167
        "Icon-App-1024x1024@1x" = 1024
    }
    
    # Ensure iOS assets directory exists
    if (-not (Test-Path $IOSAssetsDir)) {
        New-Item -ItemType Directory -Path $IOSAssetsDir -Force | Out-Null
    }
    
    foreach ($iconName in $iosSizes.Keys) {
        $size = $iosSizes[$iconName]
        $outputFile = "$IOSAssetsDir\${iconName}.png"
        
        Write-Info "Generating $iconName (${size}x${size}px)..."
        
        # Create placeholder
        "PNG placeholder for ${size}x${size}" | Out-File -FilePath "$outputFile.txt" -Encoding UTF8
        
        Write-Success "Generated placeholder: $outputFile"
    }
}

# Create proper iOS Contents.json
function New-IOSContentsJson {
    Write-Info "Creating iOS Contents.json..."
    
    $contentsJson = @{
        images = @(
            @{ idiom = "iphone"; scale = "2x"; size = "20x20" },
            @{ idiom = "iphone"; scale = "3x"; size = "20x20" },
            @{ idiom = "iphone"; scale = "2x"; size = "29x29" },
            @{ idiom = "iphone"; scale = "3x"; size = "29x29" },
            @{ idiom = "iphone"; scale = "2x"; size = "40x40" },
            @{ idiom = "iphone"; scale = "3x"; size = "40x40" },
            @{ idiom = "iphone"; scale = "2x"; size = "60x60" },
            @{ idiom = "iphone"; scale = "3x"; size = "60x60" },
            @{ idiom = "ipad"; scale = "1x"; size = "20x20" },
            @{ idiom = "ipad"; scale = "2x"; size = "20x20" },
            @{ idiom = "ipad"; scale = "1x"; size = "29x29" },
            @{ idiom = "ipad"; scale = "2x"; size = "29x29" },
            @{ idiom = "ipad"; scale = "1x"; size = "40x40" },
            @{ idiom = "ipad"; scale = "2x"; size = "40x40" },
            @{ idiom = "ipad"; scale = "1x"; size = "76x76" },
            @{ idiom = "ipad"; scale = "2x"; size = "76x76" },
            @{ idiom = "ipad"; scale = "2x"; size = "83.5x83.5" },
            @{ idiom = "ios-marketing"; scale = "1x"; size = "1024x1024" }
        )
        info = @{
            author = "Pay Mule"
            version = 1
        }
    }
    
    # Add filename to each image entry
    foreach ($image in $contentsJson.images) {
        $size = $image.size
        $scale = $image.scale
        $image.filename = "Icon-App-$size@$scale.png"
    }
    
    $jsonContent = $contentsJson | ConvertTo-Json -Depth 10
    $contentsFile = "$IOSAssetsDir\Contents.json"
    $jsonContent | Out-File -FilePath $contentsFile -Encoding UTF8
    
    Write-Success "Created iOS Contents.json"
}

# Validate generated icons
function Test-GeneratedIcons {
    Write-Info "Validating generated icons..."
    
    $errorCount = 0
    
    # Check Android icons
    $androidDensities = @("mdpi", "hdpi", "xhdpi", "xxhdpi", "xxxhdpi")
    foreach ($density in $androidDensities) {
        $iconFile = "$AndroidResDir\mipmap-$density\ic_launcher.png"
        $placeholderFile = "$iconFile.txt"
        
        if (Test-Path $placeholderFile) {
            Write-Success "Android $density placeholder created"
        } else {
            Write-Error "Missing Android $density placeholder"
            $errorCount++
        }
    }
    
    # Check iOS icons (sample)
    $iosIcons = @("<EMAIL>", "<EMAIL>")
    foreach ($icon in $iosIcons) {
        $iconFile = "$IOSAssetsDir\$icon"
        $placeholderFile = "$iconFile.txt"
        
        if (Test-Path $placeholderFile) {
            Write-Success "iOS $icon placeholder created"
        } else {
            Write-Error "Missing iOS $icon placeholder"
            $errorCount++
        }
    }
    
    # Check Contents.json
    if (Test-Path "$IOSAssetsDir\Contents.json") {
        Write-Success "iOS Contents.json created"
    } else {
        Write-Error "iOS Contents.json missing"
        $errorCount++
    }
    
    if ($errorCount -eq 0) {
        Write-Success "Icon structure validation passed!"
        return $true
    } else {
        Write-Error "Icon validation failed with $errorCount errors"
        return $false
    }
}

# Generate instructions for manual icon creation
function Show-ManualInstructions {
    Write-Info ""
    Write-Info "MANUAL ICON CREATION INSTRUCTIONS"
    Write-Info "=================================="
    Write-Info ""
    Write-Info "Since we're on Windows without ImageMagick, please follow these steps:"
    Write-Info ""
    Write-Info "1. CONVERT SVG TO PNG:"
    Write-Info "   - Open assets\source_icon.svg in a graphics editor (Inkscape, GIMP, or online converter)"
    Write-Info "   - Export as PNG at 1024x1024 pixels"
    Write-Info "   - Save as assets\pay_mule_icon_1024.png"
    Write-Info ""
    Write-Info "2. GENERATE ANDROID ICONS:"
    Write-Info "   Replace the .txt placeholders with actual PNG files:"
    
    $androidSizes = @{
        "mdpi" = 48; "hdpi" = 72; "xhdpi" = 96; "xxhdpi" = 144; "xxxhdpi" = 192
    }
    
    foreach ($density in $androidSizes.Keys) {
        $size = $androidSizes[$density]
        Write-Info "   - $AndroidResDir\mipmap-$density\ic_launcher.png (${size}x${size}px)"
    }
    
    Write-Info ""
    Write-Info "3. GENERATE iOS ICONS:"
    Write-Info "   Replace the .txt placeholders with actual PNG files in $IOSAssetsDir\"
    Write-Info ""
    Write-Info "4. ONLINE TOOLS (Recommended):"
    Write-Info "   - Use https://appicon.co/ or https://makeappicon.com/"
    Write-Info "   - Upload your 1024x1024 PNG"
    Write-Info "   - Download the generated icon sets"
    Write-Info "   - Replace the placeholder files"
    Write-Info ""
    Write-Info "5. VALIDATE:"
    Write-Info "   Run: flutter test integration_test\icon_validation_test.dart"
    Write-Info ""
}

# Main execution
function Main {
    Write-Info "Pay Mule Icon Generation Script"
    Write-Info "==============================="
    Write-Info "Source Icon: $SourceIcon"
    Write-Info "Validate Only: $ValidateOnly"
    Write-Info ""
    
    if ($ValidateOnly) {
        Test-GeneratedIcons
        return
    }
    
    # Create source PNG
    $sourcePNG = New-SourcePNG
    
    # Generate icon structure
    New-AndroidIcons -SourcePNG $sourcePNG
    New-IOSIcons -SourcePNG $sourcePNG
    New-IOSContentsJson
    
    # Validate structure
    if (Test-GeneratedIcons) {
        Write-Success "Icon structure created successfully!"
        Show-ManualInstructions
        
        Write-Info ""
        Write-Info "NEXT STEPS:"
        Write-Info "1. Convert SVG to high-quality PNG files using the instructions above"
        Write-Info "2. Run: .\generate_pay_mule_icons.ps1 -ValidateOnly"
        Write-Info "3. Test: flutter test integration_test\icon_validation_test.dart"
        Write-Info "4. Build: flutter clean && flutter pub get && flutter run"
    } else {
        Write-Error "Icon structure creation failed"
        exit 1
    }
}

# Run main function
Main
