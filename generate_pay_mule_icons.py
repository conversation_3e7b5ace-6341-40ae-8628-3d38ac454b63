#!/usr/bin/env python3
"""
Pay Mule Icon Generator
Generates all required Pay Mule wallet icons for Android and iOS
Based on the provided wallet design with Zambian flag colors
"""

import os
import json
from PIL import Image, ImageDraw, ImageFont
import sys

def create_pay_mule_icon(size):
    """Create a Pay Mule wallet icon at the specified size"""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Scale factor based on 512px base size
    scale = size / 512
    
    def s(value):
        """Scale a value based on the target size"""
        return int(value * scale)
    
    # Colors based on the design
    black = (0, 0, 0, 255)
    white = (255, 255, 255, 255)
    light_gray = (245, 245, 245, 255)
    zambia_green = (34, 139, 34, 255)    # Zambian flag green
    zambia_orange = (255, 140, 0, 255)   # Zambian flag orange  
    zambia_red = (220, 20, 60, 255)      # Zambian flag red
    
    # Background gradient effect (simplified as solid light color)
    background = (248, 248, 248, 255)
    draw.rectangle([0, 0, size, size], fill=background)
    
    # Top card/stripe (black rounded rectangle)
    top_card_coords = [s(80), s(60), s(432), s(140)]
    draw.rounded_rectangle(top_card_coords, radius=s(40), fill=black)
    
    # White stripe in top card
    white_stripe_coords = [s(120), s(80), s(392), s(120)]
    draw.rounded_rectangle(white_stripe_coords, radius=s(20), fill=white)
    
    # Main wallet body (black rounded rectangle)
    wallet_body_coords = [s(80), s(160), s(400), s(400)]
    draw.rounded_rectangle(wallet_body_coords, radius=s(40), fill=black)
    
    # Wallet interior (light area)
    interior_coords = [s(110), s(190), s(370), s(370)]
    draw.rounded_rectangle(interior_coords, radius=s(20), fill=light_gray)
    
    # Card handle/tab on the right
    handle_coords = [s(380), s(220), s(440), s(320)]
    draw.rounded_rectangle(handle_coords, radius=s(20), fill=black)
    
    # Card handle interior (white)
    handle_interior_coords = [s(395), s(240), s(425), s(300)]
    draw.rounded_rectangle(handle_interior_coords, radius=s(10), fill=white)
    
    # Zambian flag colors (horizontal stripes)
    color_width = s(40)
    color_height = s(12)
    color_y = s(210)
    start_x = s(130)
    
    # Green stripe
    green_coords = [start_x, color_y, start_x + color_width, color_y + color_height]
    draw.rounded_rectangle(green_coords, radius=s(6), fill=zambia_green)
    
    # Orange stripe
    orange_coords = [start_x + s(50), color_y, start_x + s(50) + color_width, color_y + color_height]
    draw.rounded_rectangle(orange_coords, radius=s(6), fill=zambia_orange)
    
    # Red stripe
    red_coords = [start_x + s(100), color_y, start_x + s(100) + color_width, color_y + color_height]
    draw.rounded_rectangle(red_coords, radius=s(6), fill=zambia_red)
    
    # "PM" text (Pay Mule)
    try:
        # Try to use a bold font
        font_size = s(80)
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("Arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # Get text dimensions for centering
        text = "PM"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # Center the text
        text_x = (size - text_width) // 2
        text_y = s(300) - text_height // 2
        
        draw.text((text_x, text_y), text, fill=black, font=font)
    except Exception as e:
        print(f"Warning: Could not add text to {size}x{size} icon: {e}")
    
    return img

def ensure_directory(path):
    """Ensure directory exists, create if it doesn't"""
    os.makedirs(path, exist_ok=True)

def generate_android_icons():
    """Generate Android launcher icons"""
    print("🤖 Generating Android icons...")
    
    android_sizes = {
        'mdpi': 48,
        'hdpi': 72,
        'xhdpi': 96,
        'xxhdpi': 144,
        'xxxhdpi': 192
    }
    
    base_path = "android/app/src/main/res"
    
    for density, size in android_sizes.items():
        # Create directory
        icon_dir = os.path.join(base_path, f"mipmap-{density}")
        ensure_directory(icon_dir)
        
        # Generate icon
        icon = create_pay_mule_icon(size)
        
        # Save icon
        icon_path = os.path.join(icon_dir, "ic_launcher.png")
        icon.save(icon_path, "PNG")
        
        print(f"  ✅ Created {density}: {icon_path} ({size}x{size}px)")

def generate_ios_icons():
    """Generate iOS app icons"""
    print("🍎 Generating iOS icons...")
    
    ios_sizes = {
        'Icon-App-20x20@1x': 20,
        'Icon-App-20x20@2x': 40,
        'Icon-App-20x20@3x': 60,
        'Icon-App-29x29@1x': 29,
        'Icon-App-29x29@2x': 58,
        'Icon-App-29x29@3x': 87,
        'Icon-App-40x40@1x': 40,
        'Icon-App-40x40@2x': 80,
        'Icon-App-40x40@3x': 120,
        'Icon-App-60x60@2x': 120,
        'Icon-App-60x60@3x': 180,
        'Icon-App-76x76@1x': 76,
        'Icon-App-76x76@2x': 152,
        'Icon-App-83.5x83.5@2x': 167,
        'Icon-App-1024x1024@1x': 1024
    }
    
    base_path = "ios/Runner/Assets.xcassets/AppIcon.appiconset"
    ensure_directory(base_path)
    
    for icon_name, size in ios_sizes.items():
        # Generate icon
        icon = create_pay_mule_icon(size)
        
        # Save icon
        icon_path = os.path.join(base_path, f"{icon_name}.png")
        icon.save(icon_path, "PNG")
        
        print(f"  ✅ Created {icon_name}: {icon_path} ({size}x{size}px)")

def create_ios_contents_json():
    """Create iOS Contents.json file"""
    print("📄 Creating iOS Contents.json...")
    
    contents = {
        "images": [
            {"idiom": "iphone", "scale": "2x", "size": "20x20", "filename": "<EMAIL>"},
            {"idiom": "iphone", "scale": "3x", "size": "20x20", "filename": "<EMAIL>"},
            {"idiom": "iphone", "scale": "2x", "size": "29x29", "filename": "<EMAIL>"},
            {"idiom": "iphone", "scale": "3x", "size": "29x29", "filename": "<EMAIL>"},
            {"idiom": "iphone", "scale": "2x", "size": "40x40", "filename": "<EMAIL>"},
            {"idiom": "iphone", "scale": "3x", "size": "40x40", "filename": "<EMAIL>"},
            {"idiom": "iphone", "scale": "2x", "size": "60x60", "filename": "<EMAIL>"},
            {"idiom": "iphone", "scale": "3x", "size": "60x60", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "1x", "size": "20x20", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "2x", "size": "20x20", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "1x", "size": "29x29", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "2x", "size": "29x29", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "1x", "size": "40x40", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "2x", "size": "40x40", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "1x", "size": "76x76", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "2x", "size": "76x76", "filename": "<EMAIL>"},
            {"idiom": "ipad", "scale": "2x", "size": "83.5x83.5", "filename": "<EMAIL>"},
            {"idiom": "ios-marketing", "scale": "1x", "size": "1024x1024", "filename": "<EMAIL>"}
        ],
        "info": {
            "author": "Pay Mule",
            "version": 1
        }
    }
    
    contents_path = "ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json"
    with open(contents_path, 'w') as f:
        json.dump(contents, f, indent=2)
    
    print(f"  ✅ Created Contents.json: {contents_path}")

def main():
    """Main function to generate all icons"""
    print("🇿🇲 Pay Mule Icon Generator")
    print("=" * 40)
    
    try:
        # Check if PIL is available
        from PIL import Image
        print("✅ PIL (Pillow) is available")
    except ImportError:
        print("❌ PIL (Pillow) not found. Installing...")
        os.system("pip install Pillow")
        try:
            from PIL import Image
            print("✅ PIL (Pillow) installed successfully")
        except ImportError:
            print("❌ Failed to install PIL. Please install manually: pip install Pillow")
            return False
    
    # Generate all icons
    generate_android_icons()
    generate_ios_icons()
    create_ios_contents_json()
    
    print("\n🎉 Pay Mule icons generated successfully!")
    print("\nNext steps:")
    print("1. Run: flutter clean")
    print("2. Run: flutter pub get")
    print("3. Test: flutter run")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
