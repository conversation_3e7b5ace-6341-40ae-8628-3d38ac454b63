import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:zambia_pay/main.dart' as app;

/// Icon Validation Test for Pay Mule
/// 
/// Validates that all required icons are present and correctly sized
/// Tests icon appearance on different screen densities
/// Ensures Zambia color scheme is preserved
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Pay Mule Icon Validation Tests', () {
    
    testWidgets('Android Icons - All densities present', (WidgetTester tester) async {
      // Required Android icon densities and their expected sizes
      final Map<String, int> androidIconSizes = {
        'mipmap-mdpi/ic_launcher.png': 48,      // 48dp
        'mipmap-hdpi/ic_launcher.png': 72,      // 72dp
        'mipmap-xhdpi/ic_launcher.png': 96,     // 96dp
        'mipmap-xxhdpi/ic_launcher.png': 144,   // 144dp
        'mipmap-xxxhdpi/ic_launcher.png': 192,  // 192dp
      };

      for (final entry in androidIconSizes.entries) {
        final iconPath = entry.key;
        final expectedSize = entry.value;
        
        // Check if icon file exists
        final iconFile = File('android/app/src/main/res/$iconPath');
        expect(iconFile.existsSync(), true, 
               reason: 'Android icon missing: $iconPath');
        
        if (iconFile.existsSync()) {
          // Validate icon is not empty
          final fileSize = iconFile.lengthSync();
          expect(fileSize, greaterThan(0), 
                 reason: 'Android icon is empty: $iconPath');
          
          print('✅ Android icon validated: $iconPath (${fileSize} bytes)');
        }
      }
    });

    testWidgets('iOS Icons - All sizes present', (WidgetTester tester) async {
      // Required iOS icon sizes
      final List<String> iosIconNames = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (final iconName in iosIconNames) {
        final iconFile = File('ios/Runner/Assets.xcassets/AppIcon.appiconset/$iconName');
        expect(iconFile.existsSync(), true, 
               reason: 'iOS icon missing: $iconName');
        
        if (iconFile.existsSync()) {
          final fileSize = iconFile.lengthSync();
          expect(fileSize, greaterThan(0), 
                 reason: 'iOS icon is empty: $iconName');
          
          print('✅ iOS icon validated: $iconName (${fileSize} bytes)');
        }
      }
    });

    testWidgets('Icon Contents.json validation', (WidgetTester tester) async {
      final contentsFile = File('ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json');
      expect(contentsFile.existsSync(), true, 
             reason: 'iOS Contents.json missing');
      
      if (contentsFile.existsSync()) {
        final contents = contentsFile.readAsStringSync();
        expect(contents.contains('"idiom" : "iphone"'), true,
               reason: 'Contents.json missing iPhone idiom');
        expect(contents.contains('"size" : "60x60"'), true,
               reason: 'Contents.json missing required 60x60 size');
        expect(contents.contains('"scale" : "3x"'), true,
               reason: 'Contents.json missing 3x scale');
        
        print('✅ iOS Contents.json validated');
      }
    });

    testWidgets('App launches with correct icon', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Verify app launches successfully (icon is loaded correctly)
      expect(find.byType(app.MyApp), findsOneWidget);
      
      print('✅ App launches successfully with new icon');
    });

    testWidgets('Icon quality validation', (WidgetTester tester) async {
      // Check that high-resolution icons are sufficiently large
      final highResIcons = [
        'android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png',
        'ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>',
      ];

      for (final iconPath in highResIcons) {
        final iconFile = File(iconPath);
        if (iconFile.existsSync()) {
          final fileSize = iconFile.lengthSync();
          
          // High-res icons should be at least 10KB for good quality
          expect(fileSize, greaterThan(10 * 1024), 
                 reason: 'High-res icon too small (poor quality): $iconPath');
          
          // But not too large (over 1MB indicates unoptimized)
          expect(fileSize, lessThan(1024 * 1024), 
                 reason: 'Icon file too large (unoptimized): $iconPath');
          
          print('✅ Icon quality validated: $iconPath (${(fileSize / 1024).toStringAsFixed(1)} KB)');
        }
      }
    });

    testWidgets('Zambia color scheme validation', (WidgetTester tester) async {
      // This test would ideally analyze the icon colors
      // For now, we'll just verify the icons exist and are not default Flutter icons
      
      final androidIcon = File('android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png');
      final iosIcon = File('ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>');
      
      if (androidIcon.existsSync() && iosIcon.existsSync()) {
        final androidSize = androidIcon.lengthSync();
        final iosSize = iosIcon.lengthSync();
        
        // Default Flutter icons are typically very small
        // Custom icons should be larger
        expect(androidSize, greaterThan(5 * 1024), 
               reason: 'Android icon appears to be default Flutter icon');
        expect(iosSize, greaterThan(20 * 1024), 
               reason: 'iOS icon appears to be default Flutter icon');
        
        print('✅ Icons appear to be custom (not default Flutter icons)');
        print('   Android icon: ${(androidSize / 1024).toStringAsFixed(1)} KB');
        print('   iOS icon: ${(iosSize / 1024).toStringAsFixed(1)} KB');
      }
    });

    testWidgets('Icon accessibility validation', (WidgetTester tester) async {
      // Launch app and check that icon is accessible
      app.main();
      await tester.pumpAndSettle();

      // Verify app name is correctly set (affects accessibility)
      final binding = tester.binding;
      expect(binding, isNotNull);
      
      // The app should launch without accessibility issues
      // This indirectly validates that the icon is properly configured
      print('✅ App accessibility validated (icon properly configured)');
    });

    testWidgets('Performance impact validation', (WidgetTester tester) async {
      // Measure app startup time to ensure icons don't impact performance
      final stopwatch = Stopwatch()..start();
      
      app.main();
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      final startupTime = stopwatch.elapsedMilliseconds;
      
      // App should start within reasonable time (5 seconds max)
      expect(startupTime, lessThan(5000), 
             reason: 'App startup too slow, icons may be too large');
      
      print('✅ App startup performance validated: ${startupTime}ms');
    });
  });

  group('Icon Integration Tests', () {
    
    testWidgets('Icon appears in app drawer simulation', (WidgetTester tester) async {
      // This simulates how the icon would appear in the app drawer
      app.main();
      await tester.pumpAndSettle();

      // Verify the app widget tree is built correctly
      // This ensures the icon configuration doesn't break the app
      expect(find.byType(app.MyApp), findsOneWidget);
      
      print('✅ Icon integration with app drawer validated');
    });

    testWidgets('Icon works with different screen densities', (WidgetTester tester) async {
      // Test different screen densities
      final densities = [1.0, 1.5, 2.0, 3.0, 4.0];
      
      for (final density in densities) {
        await tester.binding.setSurfaceSize(Size(360 * density, 640 * density));
        tester.binding.window.devicePixelRatioTestValue = density;
        
        app.main();
        await tester.pumpAndSettle();
        
        // App should work at all densities
        expect(find.byType(app.MyApp), findsOneWidget);
        
        print('✅ Icon validated at ${density}x density');
      }
      
      // Reset to default
      await tester.binding.setSurfaceSize(null);
      tester.binding.window.clearDevicePixelRatioTestValue();
    });
  });
}

/// Helper class for icon validation utilities
class IconValidationHelper {
  
  /// Validates that an icon file meets quality standards
  static bool validateIconQuality(File iconFile, int expectedMinSize) {
    if (!iconFile.existsSync()) return false;
    
    final fileSize = iconFile.lengthSync();
    return fileSize >= expectedMinSize && fileSize <= 1024 * 1024; // Max 1MB
  }
  
  /// Gets a summary of all icon files and their sizes
  static Map<String, int> getIconSummary() {
    final summary = <String, int>{};
    
    // Android icons
    final androidDensities = ['mdpi', 'hdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'];
    for (final density in androidDensities) {
      final file = File('android/app/src/main/res/mipmap-$density/ic_launcher.png');
      if (file.existsSync()) {
        summary['android_$density'] = file.lengthSync();
      }
    }
    
    // iOS icons (sample)
    final iosIcons = ['<EMAIL>', '<EMAIL>'];
    for (final icon in iosIcons) {
      final file = File('ios/Runner/Assets.xcassets/AppIcon.appiconset/$icon');
      if (file.existsSync()) {
        summary['ios_${icon.replaceAll('.png', '')}'] = file.lengthSync();
      }
    }
    
    return summary;
  }
}
