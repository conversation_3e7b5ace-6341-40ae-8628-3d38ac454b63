/// Zambia-Specific Authentication Flow for Pay Mule
/// Implements BoZ-compliant authentication with SMS OTP and biometric binding
/// Integrates with production lock system for secure deployment

import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../core/production_lock.dart';
import '../core/security/biometric_service.dart';
import '../core/security/encryption_service.dart';
import '../core/config/app_config.dart';
import '../features/auth/data/services/zambia_sms_service.dart';
import '../features/auth/data/services/pin_service.dart';
import '../features/financial_inclusion/tiered_kyc_service.dart';

/// Production mode flag - controlled by ProductionLock system
bool get kProductionMode => ProductionLock().isProductionMode;

class ZambiaAuthFlow {
  static final ZambiaAuthFlow _instance = ZambiaAuthFlow._internal();
  factory ZambiaAuthFlow() => _instance;
  ZambiaAuthFlow._internal();

  static final Logger _logger = Logger();
  final BiometricService _biometricService = BiometricService();
  final EncryptionService _encryptionService = EncryptionService();
  final TieredKYCService _kycService = TieredKYCService();

  /// Main authentication entry point
  /// Switches between production and development flows based on ProductionLock
  Future<AuthenticationResult> authenticate({
    required String phoneNumber,
    String? userId,
  }) async {
    try {
      _logger.i('🇿🇲 Starting Zambia authentication flow');
      _logger.i('Production mode: $kProductionMode');

      if (kProductionMode) {
        return await _productionAuthFlow(phoneNumber, userId);
      } else {
        return await _developmentAuthFlow(phoneNumber, userId);
      }
    } catch (e) {
      _logger.e('Authentication flow failed: $e');
      return AuthenticationResult(
        success: false,
        error: 'AUTHENTICATION_FAILED',
        message: 'Authentication process failed. Please try again.',
      );
    }
  }

  /// Production SMS OTP Flow - BoZ Compliant
  Future<AuthenticationResult> _productionAuthFlow(
    String phoneNumber, 
    String? userId,
  ) async {
    _logger.i('🔒 Executing PRODUCTION SMS OTP FLOW');

    try {
      // Step 1: Request SMS OTP through Zambian telecom providers
      _logger.i('📱 Requesting SMS OTP for: $phoneNumber');
      final otpResult = await ZambiaSMSService.requestOTP(
        phoneNumber: phoneNumber,
        provider: _detectProvider(phoneNumber),
      );

      if (!otpResult.success) {
        return AuthenticationResult(
          success: false,
          error: 'OTP_REQUEST_FAILED',
          message: 'Failed to send OTP. Please check your phone number.',
        );
      }

      // Step 2: Verify OTP (this would be called separately when user enters OTP)
      // For now, we return pending state
      return AuthenticationResult(
        success: true,
        isPending: true,
        otpToken: otpResult.token,
        message: 'OTP sent successfully. Please enter the code.',
        nextStep: AuthStep.OTP_VERIFICATION,
      );

    } catch (e) {
      _logger.e('Production auth flow failed: $e');
      return AuthenticationResult(
        success: false,
        error: 'PRODUCTION_AUTH_FAILED',
        message: 'Production authentication failed. Please contact support.',
      );
    }
  }

  /// Verify OTP and proceed with PIN setup
  Future<AuthenticationResult> verifyOTP({
    required String otpCode,
    required String otpToken,
    required String phoneNumber,
    String? userId,
  }) async {
    try {
      _logger.i('🔐 Verifying OTP for production authentication');

      // Verify OTP with Zambia SMS Service
      final verificationResult = await ZambiaSMSService.verifyOTP(
        otpCode: otpCode,
        token: otpToken,
      );

      if (!verificationResult.success) {
        return AuthenticationResult(
          success: false,
          error: 'OTP_VERIFICATION_FAILED',
          message: 'Invalid OTP code. Please try again.',
        );
      }

      _logger.i('✅ OTP verified successfully');

      // Update KYC status for phone verification
      if (userId != null) {
        await _kycService.verifyPhoneNumber(
          userId: userId,
          otpCode: otpCode,
        );
      }

      // Proceed to PIN setup with biometric binding
      return AuthenticationResult(
        success: true,
        message: 'OTP verified. Please set up your secure PIN.',
        nextStep: AuthStep.PIN_SETUP,
        verificationToken: verificationResult.verificationToken,
      );

    } catch (e) {
      _logger.e('OTP verification failed: $e');
      return AuthenticationResult(
        success: false,
        error: 'OTP_VERIFICATION_ERROR',
        message: 'OTP verification failed. Please try again.',
      );
    }
  }

  /// PIN Setup with Biometric Binding - BoZ Security Standards
  Future<AuthenticationResult> setupSecurePIN({
    required String pin,
    required String confirmPin,
    required String verificationToken,
    required String userId,
    bool biometricBackup = true,
  }) async {
    try {
      _logger.i('🔐 Setting up secure PIN with biometric binding');

      // Validate PIN strength
      final pinValidation = _validatePINStrength(pin);
      if (!pinValidation.isValid) {
        return AuthenticationResult(
          success: false,
          error: 'WEAK_PIN',
          message: pinValidation.message,
        );
      }

      // Confirm PIN match
      if (pin != confirmPin) {
        return AuthenticationResult(
          success: false,
          error: 'PIN_MISMATCH',
          message: 'PINs do not match. Please try again.',
        );
      }

      // Encrypt and store PIN using BoZ-compliant encryption
      final pinService = PINService();
      final pinSetupResult = await pinService.setupPIN(
        userId: userId,
        pin: pin,
        encryptionStandard: AppConfig.complianceSettings['encryptionStandard'],
      );

      if (!pinSetupResult.success) {
        return AuthenticationResult(
          success: false,
          error: 'PIN_SETUP_FAILED',
          message: 'Failed to set up PIN. Please try again.',
        );
      }

      _logger.i('✅ PIN setup completed successfully');

      // Setup biometric backup if requested and available
      if (biometricBackup) {
        final biometricResult = await _setupBiometricBackup(userId);
        if (biometricResult.success) {
          _logger.i('✅ Biometric backup enabled');
        } else {
          _logger.w('⚠️ Biometric backup setup failed: ${biometricResult.message}');
        }
      }

      // Complete authentication
      return AuthenticationResult(
        success: true,
        message: 'Authentication setup completed successfully.',
        userId: userId,
        authToken: await _generateAuthToken(userId),
        nextStep: AuthStep.COMPLETE,
      );

    } catch (e) {
      _logger.e('PIN setup failed: $e');
      return AuthenticationResult(
        success: false,
        error: 'PIN_SETUP_ERROR',
        message: 'PIN setup failed. Please try again.',
      );
    }
  }

  /// Development authentication flow (for testing)
  Future<AuthenticationResult> _developmentAuthFlow(
    String phoneNumber,
    String? userId,
  ) async {
    _logger.i('🧪 Executing DEVELOPMENT authentication flow');

    // Simulate OTP process for development
    await Future.delayed(Duration(seconds: 1));

    return AuthenticationResult(
      success: true,
      message: 'Development authentication completed',
      userId: userId ?? 'dev_user_${DateTime.now().millisecondsSinceEpoch}',
      authToken: 'dev_token_${DateTime.now().millisecondsSinceEpoch}',
      nextStep: AuthStep.COMPLETE,
    );
  }

  /// Detect mobile network provider from phone number
  String _detectProvider(String phoneNumber) {
    // Remove country code and formatting
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanNumber.startsWith('26096') || cleanNumber.startsWith('96')) {
      return 'MTN';
    } else if (cleanNumber.startsWith('26097') || cleanNumber.startsWith('97')) {
      return 'AIRTEL';
    } else if (cleanNumber.startsWith('26095') || cleanNumber.startsWith('95')) {
      return 'ZAMTEL';
    }
    
    return 'UNKNOWN';
  }

  /// Validate PIN strength according to BoZ requirements
  PINValidationResult _validatePINStrength(String pin) {
    if (pin.length < 4) {
      return PINValidationResult(
        isValid: false,
        message: 'PIN must be at least 4 digits long.',
      );
    }

    if (pin.length > 6) {
      return PINValidationResult(
        isValid: false,
        message: 'PIN must not exceed 6 digits.',
      );
    }

    // Check for sequential numbers
    if (_isSequential(pin)) {
      return PINValidationResult(
        isValid: false,
        message: 'PIN cannot contain sequential numbers (e.g., 1234).',
      );
    }

    // Check for repeated digits
    if (_hasRepeatedDigits(pin)) {
      return PINValidationResult(
        isValid: false,
        message: 'PIN cannot contain repeated digits (e.g., 1111).',
      );
    }

    return PINValidationResult(
      isValid: true,
      message: 'PIN is strong and secure.',
    );
  }

  /// Setup biometric backup authentication
  Future<BiometricSetupResult> _setupBiometricBackup(String userId) async {
    try {
      final isAvailable = await _biometricService.isBiometricAvailable();
      if (!isAvailable) {
        return BiometricSetupResult(
          success: false,
          message: 'Biometric authentication not available on this device.',
        );
      }

      final enableResult = await _biometricService.enableBiometric(userId);
      if (enableResult) {
        return BiometricSetupResult(
          success: true,
          message: 'Biometric backup enabled successfully.',
        );
      } else {
        return BiometricSetupResult(
          success: false,
          message: 'Failed to enable biometric backup.',
        );
      }
    } catch (e) {
      return BiometricSetupResult(
        success: false,
        message: 'Biometric setup error: $e',
      );
    }
  }

  /// Generate secure authentication token
  Future<String> _generateAuthToken(String userId) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final tokenData = '$userId:$timestamp:${kProductionMode ? "PROD" : "DEV"}';

    await _encryptionService.initialize();
    return await _encryptionService.encryptData(tokenData);
  }

  /// Check if PIN contains sequential numbers
  bool _isSequential(String pin) {
    for (int i = 0; i < pin.length - 1; i++) {
      final current = int.tryParse(pin[i]) ?? 0;
      final next = int.tryParse(pin[i + 1]) ?? 0;
      if (next != current + 1) return false;
    }
    return true;
  }

  /// Check if PIN has repeated digits
  bool _hasRepeatedDigits(String pin) {
    final firstDigit = pin[0];
    return pin.split('').every((digit) => digit == firstDigit);
  }
}

/// Authentication result data class
class AuthenticationResult {
  final bool success;
  final bool isPending;
  final String? error;
  final String message;
  final String? userId;
  final String? authToken;
  final String? otpToken;
  final String? verificationToken;
  final AuthStep? nextStep;

  AuthenticationResult({
    required this.success,
    this.isPending = false,
    this.error,
    required this.message,
    this.userId,
    this.authToken,
    this.otpToken,
    this.verificationToken,
    this.nextStep,
  });
}

/// Authentication steps enum
enum AuthStep {
  OTP_VERIFICATION,
  PIN_SETUP,
  BIOMETRIC_SETUP,
  COMPLETE,
}

/// PIN validation result
class PINValidationResult {
  final bool isValid;
  final String message;

  PINValidationResult({
    required this.isValid,
    required this.message,
  });
}

/// Biometric setup result
class BiometricSetupResult {
  final bool success;
  final String message;

  BiometricSetupResult({
    required this.success,
    required this.message,
  });
}
