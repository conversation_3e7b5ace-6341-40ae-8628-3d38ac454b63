import 'dart:io';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Voice-guided UI player for Zambian languages
/// Supports Bemba, Nyanja, Tonga, Lozi, and English
class VoicePlayer {
  static final VoicePlayer _instance = VoicePlayer._internal();
  factory VoicePlayer() => _instance;
  VoicePlayer._internal();

  static const MethodChannel _channel = MethodChannel('zambia_pay/voice_player');
  static final Logger _logger = Logger();
  
  // Supported languages
  static const Map<String, String> supportedLanguages = {
    'en': 'English',
    'bemba': 'Bemba',
    'nyanja': 'Nyanja/Chichewa', 
    'tonga': 'Tonga',
    'lozi': '<PERSON>zi',
  };

  // Voice prompts mapping
  static const Map<String, Map<String, String>> voicePrompts = {
    'en': {
      'welcome': 'welcome_en.wav',
      'send_money': 'send_money_en.wav',
      'pay_bills': 'pay_bills_en.wav',
      'buy_airtime': 'buy_airtime_en.wav',
      'water_bill': 'water_bill_en.wav',
      'electricity_bill': 'electricity_bill_en.wav',
      'enter_amount': 'enter_amount_en.wav',
      'enter_phone': 'enter_phone_en.wav',
      'confirm_payment': 'confirm_payment_en.wav',
      'payment_success': 'payment_success_en.wav',
      'payment_failed': 'payment_failed_en.wav',
      'offline_mode': 'offline_mode_en.wav',
    },
    'bemba': {
      'welcome': 'welcome_bemba.wav',
      'send_money': 'tuma_indalama_bemba.wav',
      'pay_bills': 'lipila_bills_bemba.wav',
      'buy_airtime': 'gula_airtime_bemba.wav',
      'water_bill': 'tapili_water_bemba.wav',
      'electricity_bill': 'umeme_bill_bemba.wav',
      'enter_amount': 'injila_amount_bemba.wav',
      'enter_phone': 'injila_phone_bemba.wav',
      'confirm_payment': 'confirm_payment_bemba.wav',
      'payment_success': 'payment_success_bemba.wav',
      'payment_failed': 'payment_failed_bemba.wav',
      'offline_mode': 'offline_mode_bemba.wav',
    },
    'nyanja': {
      'welcome': 'welcome_nyanja.wav',
      'send_money': 'tumiza_ndalama_nyanja.wav',
      'pay_bills': 'lipira_bills_nyanja.wav',
      'buy_airtime': 'gula_airtime_nyanja.wav',
      'water_bill': 'madzi_bill_nyanja.wav',
      'electricity_bill': 'magetsi_bill_nyanja.wav',
      'enter_amount': 'lowetsa_amount_nyanja.wav',
      'enter_phone': 'lowetsa_phone_nyanja.wav',
      'confirm_payment': 'confirm_payment_nyanja.wav',
      'payment_success': 'payment_success_nyanja.wav',
      'payment_failed': 'payment_failed_nyanja.wav',
      'offline_mode': 'offline_mode_nyanja.wav',
    },
    'tonga': {
      'welcome': 'welcome_tonga.wav',
      'send_money': 'tuma_mali_tonga.wav',
      'pay_bills': 'bhadhala_bills_tonga.wav',
      'buy_airtime': 'tenga_airtime_tonga.wav',
      'water_bill': 'meenda_bill_tonga.wav',
      'electricity_bill': 'getsi_bill_tonga.wav',
      'enter_amount': 'isa_amount_tonga.wav',
      'enter_phone': 'isa_phone_tonga.wav',
      'confirm_payment': 'confirm_payment_tonga.wav',
      'payment_success': 'payment_success_tonga.wav',
      'payment_failed': 'payment_failed_tonga.wav',
      'offline_mode': 'offline_mode_tonga.wav',
    },
    'lozi': {
      'welcome': 'welcome_lozi.wav',
      'send_money': 'roma_mali_lozi.wav',
      'pay_bills': 'lefa_bills_lozi.wav',
      'buy_airtime': 'reka_airtime_lozi.wav',
      'water_bill': 'mezi_bill_lozi.wav',
      'electricity_bill': 'motlakase_bill_lozi.wav',
      'enter_amount': 'kenya_amount_lozi.wav',
      'enter_phone': 'kenya_phone_lozi.wav',
      'confirm_payment': 'confirm_payment_lozi.wav',
      'payment_success': 'payment_success_lozi.wav',
      'payment_failed': 'payment_failed_lozi.wav',
      'offline_mode': 'offline_mode_lozi.wav',
    },
  };

  static String? _currentLanguage;
  static bool _isVoiceEnabled = true;
  static double _voiceVolume = 0.8;

  /// Initialize voice player
  static Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentLanguage = prefs.getString('voice_language') ?? 'en';
      _isVoiceEnabled = prefs.getBool('voice_enabled') ?? true;
      _voiceVolume = prefs.getDouble('voice_volume') ?? 0.8;
      
      _logger.i('Voice player initialized: $_currentLanguage, enabled: $_isVoiceEnabled');
    } catch (e) {
      _logger.e('Failed to initialize voice player: $e');
    }
  }

  /// Play voice prompt
  static Future<void> play(String language, String audioFile) async {
    if (!_isVoiceEnabled) {
      _logger.d('Voice disabled, skipping: $audioFile');
      return;
    }

    try {
      // Validate language
      if (!supportedLanguages.containsKey(language)) {
        _logger.w('Unsupported language: $language, falling back to English');
        language = 'en';
      }

      // Construct full audio path
      final audioPath = 'assets/audio/$language/$audioFile';
      
      _logger.d('Playing voice: $audioPath');
      
      // Play audio using platform channel
      await _channel.invokeMethod('playAudio', {
        'audioPath': audioPath,
        'volume': _voiceVolume,
      });
      
    } catch (e) {
      _logger.e('Failed to play voice: $audioFile - $e');
    }
  }

  /// Play voice prompt by key
  static Future<void> playPrompt(String promptKey, {String? language}) async {
    language ??= _currentLanguage ?? 'en';
    
    final prompts = voicePrompts[language];
    if (prompts == null || !prompts.containsKey(promptKey)) {
      _logger.w('Voice prompt not found: $promptKey in $language');
      return;
    }
    
    await play(language, prompts[promptKey]!);
  }

  /// Play welcome message
  static Future<void> playWelcome({String? language}) async {
    await playPrompt('welcome', language: language);
  }

  /// Play water bill prompt (as in your example)
  static Future<void> playWaterBill({String? language}) async {
    language ??= _currentLanguage ?? 'bemba';
    await playPrompt('water_bill', language: language);
  }

  /// Play electricity bill prompt
  static Future<void> playElectricityBill({String? language}) async {
    await playPrompt('electricity_bill', language: language);
  }

  /// Play send money prompt
  static Future<void> playSendMoney({String? language}) async {
    await playPrompt('send_money', language: language);
  }

  /// Play payment confirmation
  static Future<void> playPaymentSuccess({String? language}) async {
    await playPrompt('payment_success', language: language);
  }

  /// Play payment failure
  static Future<void> playPaymentFailed({String? language}) async {
    await playPrompt('payment_failed', language: language);
  }

  /// Play offline mode notification
  static Future<void> playOfflineMode({String? language}) async {
    await playPrompt('offline_mode', language: language);
  }

  /// Set voice language
  static Future<void> setLanguage(String language) async {
    if (!supportedLanguages.containsKey(language)) {
      _logger.w('Unsupported language: $language');
      return;
    }

    _currentLanguage = language;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('voice_language', language);
      _logger.i('Voice language set to: $language');
    } catch (e) {
      _logger.e('Failed to save voice language: $e');
    }
  }

  /// Enable/disable voice
  static Future<void> setVoiceEnabled(bool enabled) async {
    _isVoiceEnabled = enabled;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('voice_enabled', enabled);
      _logger.i('Voice enabled: $enabled');
    } catch (e) {
      _logger.e('Failed to save voice setting: $e');
    }
  }

  /// Set voice volume
  static Future<void> setVolume(double volume) async {
    _voiceVolume = volume.clamp(0.0, 1.0);
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('voice_volume', _voiceVolume);
      _logger.i('Voice volume set to: $_voiceVolume');
    } catch (e) {
      _logger.e('Failed to save voice volume: $e');
    }
  }

  /// Get current language
  static String get currentLanguage => _currentLanguage ?? 'en';

  /// Check if voice is enabled
  static bool get isVoiceEnabled => _isVoiceEnabled;

  /// Get current volume
  static double get volume => _voiceVolume;

  /// Get supported languages
  static Map<String, String> get languages => supportedLanguages;

  /// Stop current audio
  static Future<void> stop() async {
    try {
      await _channel.invokeMethod('stopAudio');
    } catch (e) {
      _logger.e('Failed to stop audio: $e');
    }
  }

  /// Check if audio file exists
  static Future<bool> audioExists(String language, String audioFile) async {
    try {
      final audioPath = 'assets/audio/$language/$audioFile';
      return await _channel.invokeMethod('audioExists', {'audioPath': audioPath});
    } catch (e) {
      _logger.e('Failed to check audio existence: $e');
      return false;
    }
  }

  /// Download missing audio files (for future implementation)
  static Future<void> downloadAudioPack(String language) async {
    try {
      _logger.i('Downloading audio pack for: $language');
      // Implementation would download audio files from server
      // and store them locally for offline use
    } catch (e) {
      _logger.e('Failed to download audio pack: $e');
    }
  }

  /// Get voice settings summary
  static Map<String, dynamic> getSettings() {
    return {
      'language': _currentLanguage,
      'enabled': _isVoiceEnabled,
      'volume': _voiceVolume,
      'supportedLanguages': supportedLanguages,
    };
  }
}
