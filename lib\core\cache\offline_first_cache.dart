import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';

import '../network/network_quality_detector.dart';
import '../data_usage/data_usage_monitor.dart';

/// Offline-first cache system optimized for Zambian data constraints
/// Prioritizes cached data and only refreshes when absolutely necessary
class OfflineFirstCache extends ChangeNotifier {
  static final OfflineFirstCache _instance = OfflineFirstCache._internal();
  factory OfflineFirstCache() => _instance;
  OfflineFirstCache._internal();

  final Logger _logger = Logger();
  final NetworkQualityDetector _networkDetector = NetworkQualityDetector();
  final DataUsageMonitor _dataMonitor = DataUsageMonitor();

  // Cache storage
  final Map<String, CacheEntry> _memoryCache = {};
  SharedPreferences? _prefs;

  // Cache configuration
  static const Duration _defaultTTL = Duration(hours: 24);
  static const Duration _shortTTL = Duration(minutes: 30);
  static const Duration _longTTL = Duration(days: 7);
  static const int _maxMemoryCacheSize = 100;
  static const int _maxDiskCacheSize = 500;

  // Cache statistics
  int _cacheHits = 0;
  int _cacheMisses = 0;
  int _networkRequests = 0;
  double _dataSaved = 0.0; // in MB

  /// Initialize offline-first cache
  Future<void> initialize() async {
    try {
      _logger.i('Initializing OfflineFirstCache...');
      
      _prefs = await SharedPreferences.getInstance();
      await _loadCacheStatistics();
      await _cleanupExpiredEntries();
      
      _logger.i('OfflineFirstCache initialized - Cache entries: ${_memoryCache.length}');
    } catch (e) {
      _logger.e('Failed to initialize OfflineFirstCache: $e');
    }
  }

  /// Get data with offline-first strategy
  Future<T?> get<T>({
    required String key,
    required Future<T> Function() networkFetch,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, dynamic> Function(T)? toJson,
    Duration? ttl,
    bool forceRefresh = false,
    bool isEssential = false,
  }) async {
    final cacheKey = _generateCacheKey(key);
    
    try {
      // Check memory cache first (fastest)
      if (!forceRefresh) {
        final memoryCached = _getFromMemoryCache<T>(cacheKey);
        if (memoryCached != null) {
          _cacheHits++;
          _logger.d('Memory cache hit for key: $key');
          return memoryCached;
        }
      }

      // Check disk cache (still fast, no network)
      if (!forceRefresh) {
        final diskCached = await _getFromDiskCache<T>(cacheKey, fromJson);
        if (diskCached != null) {
          _cacheHits++;
          _logger.d('Disk cache hit for key: $key');
          
          // Store in memory cache for faster future access
          _storeInMemoryCache(cacheKey, diskCached, ttl ?? _defaultTTL);
          
          return diskCached;
        }
      }

      // Cache miss - decide whether to fetch from network
      _cacheMisses++;
      
      if (!_shouldFetchFromNetwork(isEssential)) {
        _logger.w('Network fetch blocked for key: $key');
        return null;
      }

      // Fetch from network
      _networkRequests++;
      _logger.i('Fetching from network for key: $key');
      
      final networkData = await networkFetch();
      
      // Store in both memory and disk cache
      await _storeInCache(cacheKey, networkData, toJson, ttl ?? _defaultTTL);
      
      // Track data usage
      final estimatedDataUsage = _estimateDataUsage(key);
      await _dataMonitor.trackDataUsage(
        dataUsedMB: estimatedDataUsage,
        category: 'cache',
        operation: 'network_fetch',
      );
      
      return networkData;
      
    } catch (e) {
      _logger.e('Failed to get data for key $key: $e');
      
      // Try to return stale cache data as last resort
      final staleData = await _getStaleFromCache<T>(cacheKey, fromJson);
      if (staleData != null) {
        _logger.w('Returning stale cache data for key: $key');
        return staleData;
      }
      
      return null;
    }
  }

  /// Store data in cache
  Future<void> set<T>({
    required String key,
    required T data,
    Map<String, dynamic> Function(T)? toJson,
    Duration? ttl,
  }) async {
    final cacheKey = _generateCacheKey(key);
    await _storeInCache(cacheKey, data, toJson, ttl ?? _defaultTTL);
  }

  /// Remove data from cache
  Future<void> remove(String key) async {
    final cacheKey = _generateCacheKey(key);
    
    // Remove from memory cache
    _memoryCache.remove(cacheKey);
    
    // Remove from disk cache
    await _prefs?.remove('cache_$cacheKey');
    await _prefs?.remove('cache_meta_$cacheKey');
    
    _logger.d('Removed cache entry for key: $key');
  }

  /// Clear all cache data
  Future<void> clear() async {
    // Clear memory cache
    _memoryCache.clear();
    
    // Clear disk cache
    final keys = _prefs?.getKeys().where((key) => key.startsWith('cache_')).toList() ?? [];
    for (final key in keys) {
      await _prefs?.remove(key);
    }
    
    _logger.i('Cleared all cache data');
    notifyListeners();
  }

  /// Get cache statistics
  Map<String, dynamic> getStatistics() {
    final totalRequests = _cacheHits + _cacheMisses;
    final hitRate = totalRequests > 0 ? (_cacheHits / totalRequests) * 100 : 0.0;
    
    return {
      'cacheHits': _cacheHits,
      'cacheMisses': _cacheMisses,
      'networkRequests': _networkRequests,
      'hitRate': hitRate,
      'dataSaved': _dataSaved,
      'memoryCacheSize': _memoryCache.length,
      'diskCacheSize': _getDiskCacheSize(),
    };
  }

  /// Check if should fetch from network
  bool _shouldFetchFromNetwork(bool isEssential) {
    final networkQuality = _networkDetector.networkQuality;
    
    // Always allow essential requests if we have any connection
    if (isEssential && networkQuality != NetworkQuality.offline) {
      return true;
    }
    
    // Block requests on poor network unless essential
    if (networkQuality == NetworkQuality.poor && !isEssential) {
      return false;
    }
    
    // Block all requests when offline
    if (networkQuality == NetworkQuality.offline) {
      return false;
    }
    
    // Check data usage limits
    final estimatedDataUsage = 0.01; // Conservative estimate
    if (!_dataMonitor.canUseDataForOperation(estimatedDataUsage, isEssential: isEssential)) {
      return false;
    }
    
    return true;
  }

  /// Get data from memory cache
  T? _getFromMemoryCache<T>(String cacheKey) {
    final entry = _memoryCache[cacheKey];
    if (entry == null) return null;
    
    if (entry.isExpired) {
      _memoryCache.remove(cacheKey);
      return null;
    }
    
    return entry.data as T?;
  }

  /// Get data from disk cache
  Future<T?> _getFromDiskCache<T>(String cacheKey, T Function(Map<String, dynamic>)? fromJson) async {
    try {
      final dataString = _prefs?.getString('cache_$cacheKey');
      final metaString = _prefs?.getString('cache_meta_$cacheKey');
      
      if (dataString == null || metaString == null) return null;
      
      final meta = jsonDecode(metaString) as Map<String, dynamic>;
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(meta['expiryTime']);
      
      if (DateTime.now().isAfter(expiryTime)) {
        // Remove expired entry
        await _prefs?.remove('cache_$cacheKey');
        await _prefs?.remove('cache_meta_$cacheKey');
        return null;
      }
      
      if (fromJson != null) {
        final dataMap = jsonDecode(dataString) as Map<String, dynamic>;
        return fromJson(dataMap);
      } else {
        return jsonDecode(dataString) as T;
      }
      
    } catch (e) {
      _logger.e('Failed to get from disk cache: $e');
      return null;
    }
  }

  /// Get stale data from cache (ignoring expiry)
  Future<T?> _getStaleFromCache<T>(String cacheKey, T Function(Map<String, dynamic>)? fromJson) async {
    try {
      // Check memory cache first (even if expired)
      final memoryEntry = _memoryCache[cacheKey];
      if (memoryEntry != null) {
        return memoryEntry.data as T?;
      }
      
      // Check disk cache (even if expired)
      final dataString = _prefs?.getString('cache_$cacheKey');
      if (dataString == null) return null;
      
      if (fromJson != null) {
        final dataMap = jsonDecode(dataString) as Map<String, dynamic>;
        return fromJson(dataMap);
      } else {
        return jsonDecode(dataString) as T;
      }
      
    } catch (e) {
      _logger.e('Failed to get stale cache: $e');
      return null;
    }
  }

  /// Store data in both memory and disk cache
  Future<void> _storeInCache<T>(
    String cacheKey,
    T data,
    Map<String, dynamic> Function(T)? toJson,
    Duration ttl,
  ) async {
    // Store in memory cache
    _storeInMemoryCache(cacheKey, data, ttl);
    
    // Store in disk cache
    await _storeToDiskCache(cacheKey, data, toJson, ttl);
  }

  /// Store data in memory cache
  void _storeInMemoryCache<T>(String cacheKey, T data, Duration ttl) {
    // Remove oldest entries if cache is full
    if (_memoryCache.length >= _maxMemoryCacheSize) {
      final oldestKey = _memoryCache.keys.first;
      _memoryCache.remove(oldestKey);
    }
    
    final expiryTime = DateTime.now().add(ttl);
    _memoryCache[cacheKey] = CacheEntry(data: data, expiryTime: expiryTime);
  }

  /// Store data in disk cache
  Future<void> _storeToDiskCache<T>(
    String cacheKey,
    T data,
    Map<String, dynamic> Function(T)? toJson,
    Duration ttl,
  ) async {
    try {
      final expiryTime = DateTime.now().add(ttl);
      
      String dataString;
      if (toJson != null) {
        dataString = jsonEncode(toJson(data));
      } else {
        dataString = jsonEncode(data);
      }
      
      final meta = {
        'expiryTime': expiryTime.millisecondsSinceEpoch,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        'size': dataString.length,
      };
      
      await _prefs?.setString('cache_$cacheKey', dataString);
      await _prefs?.setString('cache_meta_$cacheKey', jsonEncode(meta));
      
    } catch (e) {
      _logger.e('Failed to store to disk cache: $e');
    }
  }

  /// Generate cache key
  String _generateCacheKey(String key) {
    final bytes = utf8.encode(key);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Estimate data usage for cache operation
  double _estimateDataUsage(String key) {
    // Estimate based on operation type
    if (key.contains('balance')) return 0.005; // 5KB
    if (key.contains('transaction')) return 0.02; // 20KB
    if (key.contains('profile')) return 0.01; // 10KB
    return 0.015; // 15KB default
  }

  /// Get disk cache size
  int _getDiskCacheSize() {
    final keys = _prefs?.getKeys().where((key) => key.startsWith('cache_')).toList() ?? [];
    return keys.length ~/ 2; // Divide by 2 because we store data and meta separately
  }

  /// Cleanup expired entries
  Future<void> _cleanupExpiredEntries() async {
    try {
      // Cleanup memory cache
      final expiredMemoryKeys = _memoryCache.entries
          .where((entry) => entry.value.isExpired)
          .map((entry) => entry.key)
          .toList();
      
      for (final key in expiredMemoryKeys) {
        _memoryCache.remove(key);
      }
      
      // Cleanup disk cache
      final allKeys = _prefs?.getKeys().where((key) => key.startsWith('cache_meta_')).toList() ?? [];
      
      for (final metaKey in allKeys) {
        final metaString = _prefs?.getString(metaKey);
        if (metaString != null) {
          final meta = jsonDecode(metaString) as Map<String, dynamic>;
          final expiryTime = DateTime.fromMillisecondsSinceEpoch(meta['expiryTime']);
          
          if (DateTime.now().isAfter(expiryTime)) {
            final dataKey = metaKey.replaceFirst('cache_meta_', 'cache_');
            await _prefs?.remove(dataKey);
            await _prefs?.remove(metaKey);
          }
        }
      }
      
      _logger.i('Cleaned up expired cache entries');
      
    } catch (e) {
      _logger.e('Failed to cleanup expired entries: $e');
    }
  }

  /// Load cache statistics
  Future<void> _loadCacheStatistics() async {
    try {
      _cacheHits = _prefs?.getInt('cache_hits') ?? 0;
      _cacheMisses = _prefs?.getInt('cache_misses') ?? 0;
      _networkRequests = _prefs?.getInt('network_requests') ?? 0;
      _dataSaved = _prefs?.getDouble('data_saved') ?? 0.0;
    } catch (e) {
      _logger.e('Failed to load cache statistics: $e');
    }
  }

  /// Save cache statistics
  Future<void> _saveCacheStatistics() async {
    try {
      await _prefs?.setInt('cache_hits', _cacheHits);
      await _prefs?.setInt('cache_misses', _cacheMisses);
      await _prefs?.setInt('network_requests', _networkRequests);
      await _prefs?.setDouble('data_saved', _dataSaved);
    } catch (e) {
      _logger.e('Failed to save cache statistics: $e');
    }
  }

  @override
  void dispose() {
    _saveCacheStatistics();
    super.dispose();
  }
}

/// Cache entry data class
class CacheEntry {
  final dynamic data;
  final DateTime expiryTime;

  CacheEntry({
    required this.data,
    required this.expiryTime,
  });

  bool get isExpired => DateTime.now().isAfter(expiryTime);
}
