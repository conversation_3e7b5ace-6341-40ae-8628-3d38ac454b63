/// Core application configuration for Zambia Pay
/// Includes mobile money provider settings, encryption keys, and API endpoints
class AppConfig {
  // App Information
  static const String appName = 'Pay Mule';
  static const String appVersion = '1.0.0';
  static const String supportedCountry = 'ZM';
  static const String baseCurrency = 'ZMW';
  
  // Mobile Money Configuration
  static const Map<String, dynamic> mtnConfig = {
    'baseUrl': 'https://momodeveloper.mtn.com/v1',
    'currency': 'ZMW',
    'partyIdType': 'MSISDN', // Format: "26096xxxxxxx"
    'txFee': 0.0021, // Zambia's 2025 Mobile Money Levy (0.21%)
    'maxTransactionAmount': 50000.0, // ZMW 50,000 daily limit
    'minTransactionAmount': 1.0,
    'countryCode': '260',
    'operatorCode': '96', // MTN Zambia
  };
  
  static const Map<String, dynamic> airtelConfig = {
    'baseUrl': 'https://openapiuat.airtel.africa/mw/v2',
    'currency': 'ZMW',
    'partyIdType': 'MSISDN',
    'txFee': 0.0021,
    'maxTransactionAmount': 50000.0,
    'minTransactionAmount': 1.0,
    'countryCode': '260',
    'operatorCode': '97', // Airtel Zambia
  };
  
  static const Map<String, dynamic> zamtelConfig = {
    'baseUrl': 'https://api.zamtel.zm/v1',
    'currency': 'ZMW',
    'partyIdType': 'MSISDN',
    'txFee': 0.0021,
    'maxTransactionAmount': 50000.0,
    'minTransactionAmount': 1.0,
    'countryCode': '260',
    'operatorCode': '95', // Zamtel
  };
  
  // Fallback provider priority
  static const List<String> fallbackProviders = [
    'MTN',
    'AIRTEL',
    'ZAMTEL'
  ];
  
  // Offline Sync Configuration
  static const int maxRetryAttempts = 3;
  static const int syncIntervalMinutes = 15;
  static const int offlineQueueMaxSize = 1000;
  static const int connectionTimeoutSeconds = 30;
  
  // Security Configuration
  static const String encryptionAlgorithm = 'AES-256-GCM';
  static const int keyDerivationIterations = 100000;
  static const int saltLength = 32;
  static const int ivLength = 16;
  
  // Database Configuration
  static const String databaseName = 'zambia_pay.db';
  static const int databaseVersion = 1;
  static const String encryptionPassword = 'SECURE_DB_PASSWORD'; // Should be generated dynamically
  
  // Utility Providers
  static const Map<String, dynamic> utilityProviders = {
    'ZESCO': {
      'name': 'Zesco Limited',
      'type': 'electricity',
      'apiEndpoint': 'https://api.zesco.co.zm/v1',
      'supportedRegions': ['lusaka', 'copperbelt', 'southern', 'eastern', 'western', 'northern', 'central', 'muchinga', 'luapula', 'northwestern'],
    },
    'LWSC': {
      'name': 'Lusaka Water and Sewerage Company',
      'type': 'water',
      'apiEndpoint': 'https://api.lwsc.co.zm/v1',
      'supportedRegions': ['lusaka'],
    },
    'NWASCO': {
      'name': 'National Water Supply and Sanitation Council',
      'type': 'water',
      'apiEndpoint': 'https://api.nwasco.org.zm/v1',
      'supportedRegions': ['all'],
    },
  };
  
  // Biometric Authentication
  static const bool biometricEnabled = true;
  static const List<String> supportedBiometrics = [
    'fingerprint',
    'face',
    'iris'
  ];
  
  // Localization
  static const List<String> supportedLanguages = [
    'en', // English
    'ny', // Chichewa/Nyanja
    'bem', // Bemba
    'ton', // Tonga
    'loz', // Lozi
  ];
  
  // Development/Production Environment
  static const bool isProduction = true;
  static const bool enableLogging = true;
  static const bool enableCrashReporting = true;
  
  // Bank of Zambia Compliance
  static const Map<String, dynamic> complianceSettings = {
    'pciDssLevel': 1,
    'dataRetentionDays': 2555, // 7 years as per BoZ requirements
    'auditLogEnabled': true,
    'encryptionStandard': 'FIPS-140-2',
    'kycRequired': true,
    'amlEnabled': true,
    'maxDailyTransactionLimit': 50000.0,
    'maxMonthlyTransactionLimit': 500000.0,
  };
  
  // Network Configuration
  static const Map<String, int> networkTimeouts = {
    'connection': 30,
    'receive': 60,
    'send': 30,
  };
  
  // Feature Flags - MOBILE MONEY MVP CONFIGURATION
  static const Map<String, bool> featureFlags = {
    // Mobile Money Core Features (ENABLED)
    'offlineMode': true,
    'biometricAuth': true,
    'qrCodePayments': true,
    'mobileMoneyTransfers': true,
    'utilityPayments': true,
    'agentLocator': true,
    'chilimbaSupport': true,
    'airtimePurchase': true,
    'transactionHistory': true,
    'balanceInquiry': true,

    // Banking Features (DISABLED for MVP)
    'bankLinking': false,
    'bankTransfers': false,
    'bankAccountManagement': false,
    'bankStatements': false,
    'bankCards': false,

    // Future Features (DISABLED)
    'bulkPayments': false,
    'merchantPayments': false,
    'internationalTransfers': false,
    'cryptoPayments': false,
    'loanServices': false,
    'investmentServices': false,
  };
}
