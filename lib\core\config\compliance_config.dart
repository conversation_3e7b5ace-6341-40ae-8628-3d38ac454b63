/// Bank of Zambia (BoZ) Compliance Configuration
/// 
/// BoZ Compliance: PCI-DSS Section 3.2.1
/// Data retention: 5 years (Zambia Financial Act 2022)
/// 
/// This file contains all compliance-related configurations
/// for regulatory adherence in Zambia's financial sector.

class ComplianceConfig {
  // BoZ Regulatory Framework
  static const String regulatoryFramework = 'Bank of Zambia Financial Services Act 2022';
  static const String dataProtectionAct = 'Zambia Data Protection Act 2021';
  static const String financialAct = 'Zambia Financial Act 2022';
  
  // PCI-DSS Configuration
  static const String pciDssVersion = '3.2.1';
  static const String pciDssLevel = 'Level 1'; // Highest security level
  
  // Data Retention (Zambia Financial Act 2022 Section 45)
  static const int dataRetentionYears = 5;
  static const int dataRetentionDays = 1825; // 5 years = 1825 days
  static const int dataRetentionHours = 43800; // 5 years in hours
  
  // Transaction Limits (BoZ Guidelines 2024)
  static const Map<String, double> transactionLimits = {
    'daily_individual_limit': 50000.0,      // K50,000 per day
    'monthly_individual_limit': 500000.0,   // K500,000 per month
    'annual_individual_limit': 2000000.0,   // K2,000,000 per year
    'single_transaction_limit': 25000.0,    // K25,000 per transaction
    'merchant_daily_limit': 200000.0,       // K200,000 for merchants
    'large_transaction_threshold': 10000.0,  // K10,000 requires additional verification
  };
  
  // AML/CFT Thresholds (Financial Intelligence Centre Act)
  static const Map<String, double> amlThresholds = {
    'suspicious_transaction_amount': 15000.0,  // K15,000
    'cash_transaction_reporting': 20000.0,     // K20,000
    'cross_border_reporting': 10000.0,         // K10,000
    'rapid_succession_threshold': 5000.0,      // K5,000 in 1 hour
    'structuring_detection_amount': 9500.0,    // Just below K10,000
  };
  
  // PCI-DSS 3.2.1 Requirements Mapping
  static const Map<String, Map<String, dynamic>> pciDssRequirements = {
    '3.2.1': {
      'title': 'Do not store sensitive authentication data after authorization',
      'description': 'Sensitive authentication data must not be stored after authorization',
      'implementation': 'Immediate deletion of CVV, PIN verification values, magnetic stripe data',
      'validation_frequency': 'Continuous',
      'compliance_level': 'CRITICAL',
    },
    '3.3': {
      'title': 'Mask PAN when displayed',
      'description': 'Primary Account Number (PAN) must be masked when displayed',
      'implementation': 'Show only first 6 and last 4 digits',
      'validation_frequency': 'Daily',
      'compliance_level': 'HIGH',
    },
    '3.4': {
      'title': 'Render PAN unreadable anywhere it is stored',
      'description': 'PAN must be encrypted or tokenized in all storage locations',
      'implementation': 'AES-256-GCM encryption with secure key management',
      'validation_frequency': 'Continuous',
      'compliance_level': 'CRITICAL',
    },
    '8.2.3': {
      'title': 'Passwords must meet complexity requirements',
      'description': 'Strong password policy enforcement',
      'implementation': 'Minimum 8 characters, mixed case, numbers, symbols',
      'validation_frequency': 'Real-time',
      'compliance_level': 'HIGH',
    },
    '10.1': {
      'title': 'Implement audit trails',
      'description': 'Link all access to system components to each individual user',
      'implementation': 'Comprehensive audit logging with user attribution',
      'validation_frequency': 'Continuous',
      'compliance_level': 'CRITICAL',
    },
    '10.2': {
      'title': 'Implement automated audit trails',
      'description': 'Automated audit trails for all system components',
      'implementation': 'Real-time audit log generation and monitoring',
      'validation_frequency': 'Continuous',
      'compliance_level': 'CRITICAL',
    },
  };
  
  // Data Classification Levels
  static const Map<String, Map<String, dynamic>> dataClassification = {
    'PUBLIC': {
      'retention_days': 365,
      'encryption_required': false,
      'access_level': 'ALL_USERS',
      'examples': ['Marketing materials', 'Public announcements'],
    },
    'INTERNAL': {
      'retention_days': 1095, // 3 years
      'encryption_required': true,
      'access_level': 'EMPLOYEES_ONLY',
      'examples': ['Internal policies', 'System configurations'],
    },
    'CONFIDENTIAL': {
      'retention_days': 1825, // 5 years
      'encryption_required': true,
      'access_level': 'AUTHORIZED_PERSONNEL',
      'examples': ['Customer data', 'Transaction records'],
    },
    'FINANCIAL_RECORD': {
      'retention_days': 1825, // 5 years (BoZ requirement)
      'encryption_required': true,
      'access_level': 'COMPLIANCE_OFFICERS',
      'examples': ['Payment transactions', 'Account balances', 'Audit logs'],
    },
    'CARDHOLDER_DATA': {
      'retention_days': 0, // Immediate deletion after authorization (PCI-DSS 3.2.1)
      'encryption_required': true,
      'access_level': 'PAYMENT_PROCESSORS',
      'examples': ['CVV codes', 'PIN verification values', 'Magnetic stripe data'],
    },
  };
  
  // Compliance Monitoring Intervals
  static const Map<String, int> monitoringIntervals = {
    'real_time_monitoring_seconds': 1,
    'fraud_detection_minutes': 5,
    'compliance_check_hours': 24,
    'data_retention_cleanup_days': 7,
    'pci_dss_assessment_days': 90,
    'boz_reporting_days': 90, // Quarterly reporting
    'annual_compliance_audit_days': 365,
  };
  
  // Encryption Standards (PCI-DSS Compliant)
  static const Map<String, dynamic> encryptionStandards = {
    'algorithm': 'AES-256-GCM',
    'key_length': 256,
    'key_derivation': 'PBKDF2',
    'key_derivation_iterations': 100000,
    'salt_length': 32,
    'iv_length': 16,
    'hash_algorithm': 'SHA-256',
    'signature_algorithm': 'HMAC-SHA256',
    'key_rotation_days': 90,
  };
  
  // Audit Event Types
  static const List<String> auditEventTypes = [
    'USER_LOGIN',
    'USER_LOGOUT',
    'LOGIN_FAILED',
    'PASSWORD_CHANGE',
    'TRANSACTION_CREATED',
    'TRANSACTION_COMPLETED',
    'TRANSACTION_FAILED',
    'PAYMENT_PROCESSED',
    'DATA_ACCESS',
    'DATA_MODIFICATION',
    'SYSTEM_CONFIGURATION_CHANGE',
    'SECURITY_VIOLATION',
    'COMPLIANCE_VIOLATION',
    'DATA_RETENTION_CLEANUP',
    'SECURE_DATA_DELETION',
    'PCI_DSS_ASSESSMENT',
    'BOZ_COMPLIANCE_VERIFICATION',
    'AML_FLAG_TRIGGERED',
    'SUSPICIOUS_ACTIVITY_DETECTED',
    'LARGE_TRANSACTION_ALERT',
  ];
  
  // Compliance Violation Severity Levels
  static const Map<String, Map<String, dynamic>> violationSeverity = {
    'LOW': {
      'escalation_required': false,
      'boz_notification_required': false,
      'resolution_time_hours': 72,
      'examples': ['Minor policy deviations', 'Documentation gaps'],
    },
    'MEDIUM': {
      'escalation_required': true,
      'boz_notification_required': false,
      'resolution_time_hours': 24,
      'examples': ['Access control issues', 'Audit trail gaps'],
    },
    'HIGH': {
      'escalation_required': true,
      'boz_notification_required': true,
      'resolution_time_hours': 8,
      'examples': ['Data encryption failures', 'Transaction limit breaches'],
    },
    'CRITICAL': {
      'escalation_required': true,
      'boz_notification_required': true,
      'resolution_time_hours': 1,
      'examples': ['Data breaches', 'PCI-DSS violations', 'AML failures'],
    },
  };
  
  // BoZ Reporting Requirements
  static const Map<String, dynamic> bozReporting = {
    'quarterly_report_required': true,
    'annual_audit_required': true,
    'incident_reporting_hours': 24,
    'large_transaction_reporting_hours': 48,
    'suspicious_activity_reporting_hours': 72,
    'data_breach_reporting_hours': 2,
    'compliance_officer_contact': '<EMAIL>',
    'boz_contact_email': '<EMAIL>',
    'boz_emergency_contact': '+260-211-399-300',
  };
  
  // Risk Scoring Matrix
  static const Map<String, int> riskScoring = {
    'large_amount_points': 30,
    'unusual_time_points': 15,
    'rapid_succession_points': 20,
    'cross_border_points': 25,
    'new_payee_points': 10,
    'failed_authentication_points': 40,
    'suspicious_location_points': 35,
    'round_amount_points': 15,
    'maximum_risk_score': 100,
    'high_risk_threshold': 70,
    'critical_risk_threshold': 90,
  };
  
  // Secure Deletion Standards
  static const Map<String, dynamic> secureDeletion = {
    'overwrite_passes': 3,
    'overwrite_pattern': 'RANDOM',
    'verification_required': true,
    'certificate_generation': true,
    'audit_trail_required': true,
    'compliance_verification': true,
  };
  
  /// Get retention period for data type
  static int getRetentionPeriod(String dataType) {
    final classification = dataClassification[dataType];
    return classification?['retention_days'] ?? dataRetentionDays;
  }
  
  /// Check if encryption is required for data type
  static bool isEncryptionRequired(String dataType) {
    final classification = dataClassification[dataType];
    return classification?['encryption_required'] ?? true;
  }
  
  /// Get violation resolution time
  static int getViolationResolutionTime(String severity) {
    final severityInfo = violationSeverity[severity];
    return severityInfo?['resolution_time_hours'] ?? 24;
  }
  
  /// Check if BoZ notification is required
  static bool isBozNotificationRequired(String severity) {
    final severityInfo = violationSeverity[severity];
    return severityInfo?['boz_notification_required'] ?? false;
  }
  
  /// Get PCI-DSS requirement details
  static Map<String, dynamic>? getPciDssRequirement(String requirementId) {
    return pciDssRequirements[requirementId];
  }
  
  /// Validate transaction amount against limits
  static bool isTransactionAmountValid(double amount, String limitType) {
    final limit = transactionLimits[limitType];
    return limit != null && amount <= limit;
  }
  
  /// Check if amount triggers AML reporting
  static bool triggersAmlReporting(double amount, String transactionType) {
    final threshold = amlThresholds['${transactionType}_reporting'];
    return threshold != null && amount >= threshold;
  }
}
