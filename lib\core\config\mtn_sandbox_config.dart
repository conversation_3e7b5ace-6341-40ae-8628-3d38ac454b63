import 'package:logger/logger.dart';

/// MTN Sandbox Configuration for Testing
/// Provides test credentials and validation for MTN Mobile Money sandbox environment
/// Use this for testing real-time transactions before merging to production
class MTNSandboxConfig {
  static final Logger _logger = Logger();

  // MTN Sandbox Environment URLs
  static const String sandboxBaseUrl = 'https://sandbox.momodeveloper.mtn.com';
  static const String productionBaseUrl = 'https://momodeveloper.mtn.com';
  
  // Sandbox API Endpoints
  static const String tokenEndpoint = '/collection/token/';
  static const String requestToPayEndpoint = '/collection/v1_0/requesttopay';
  static const String transactionStatusEndpoint = '/collection/v1_0/requesttopay';
  static const String balanceEndpoint = '/collection/v1_0/account/balance';
  
  // Test Credentials (Sandbox)
  static const Map<String, String> sandboxCredentials = {
    'subscriptionKey': 'YOUR_SANDBOX_SUBSCRIPTION_KEY',
    'apiUserId': 'YOUR_SANDBOX_API_USER_ID',
    'apiKey': 'YOUR_SANDBOX_API_KEY',
    'targetEnvironment': 'sandbox',
    'callbackUrl': 'https://webhook.site/your-webhook-url',
  };

  // Test Phone Numbers for Sandbox
  static const Map<String, String> testPhoneNumbers = {
    'validMTN': '26096XXXXXXX', // Replace with actual test number
    'invalidMTN': '26097XXXXXXX', // Non-MTN number for testing
    'successScenario': '***********', // Always succeeds
    'failureScenario': '***********', // Always fails
    'pendingScenario': '***********', // Stays pending
  };

  // Test Transaction Amounts
  static const Map<String, double> testAmounts = {
    'small': 10.0,
    'medium': 100.0,
    'large': 1000.0,
    'maximum': 50000.0,
    'invalid': 100000.0, // Exceeds limit
  };

  /// Get current environment configuration
  static Map<String, String> getCurrentConfig({bool isProduction = true}) {
    if (isProduction) {
      _logger.w('Using PRODUCTION MTN configuration');
      return {
        'baseUrl': productionBaseUrl,
        'subscriptionKey': 'YOUR_PRODUCTION_SUBSCRIPTION_KEY',
        'apiUserId': 'YOUR_PRODUCTION_API_USER_ID',
        'apiKey': 'YOUR_PRODUCTION_API_KEY',
        'targetEnvironment': 'mtnglobalapi',
      };
    } else {
      _logger.i('Using SANDBOX MTN configuration');
      return {
        'baseUrl': sandboxBaseUrl,
        ...sandboxCredentials,
      };
    }
  }

  /// Validate sandbox credentials
  static bool validateSandboxCredentials() {
    final requiredKeys = ['subscriptionKey', 'apiUserId', 'apiKey'];
    
    for (final key in requiredKeys) {
      final value = sandboxCredentials[key];
      if (value == null || value.isEmpty || value.startsWith('YOUR_')) {
        _logger.e('Invalid or missing sandbox credential: $key');
        return false;
      }
    }
    
    return true;
  }

  /// Get test scenario configuration
  static Map<String, dynamic> getTestScenario(String scenarioName) {
    switch (scenarioName) {
      case 'success':
        return {
          'phoneNumber': testPhoneNumbers['successScenario'],
          'amount': testAmounts['medium'],
          'expectedStatus': 'SUCCESSFUL',
          'description': 'Test successful transaction',
        };
      
      case 'failure':
        return {
          'phoneNumber': testPhoneNumbers['failureScenario'],
          'amount': testAmounts['small'],
          'expectedStatus': 'FAILED',
          'description': 'Test failed transaction',
        };
      
      case 'pending':
        return {
          'phoneNumber': testPhoneNumbers['pendingScenario'],
          'amount': testAmounts['large'],
          'expectedStatus': 'PENDING',
          'description': 'Test pending transaction',
        };
      
      case 'invalid_amount':
        return {
          'phoneNumber': testPhoneNumbers['validMTN'],
          'amount': testAmounts['invalid'],
          'expectedStatus': 'FAILED',
          'description': 'Test invalid amount',
        };
      
      default:
        return {
          'phoneNumber': testPhoneNumbers['validMTN'],
          'amount': testAmounts['small'],
          'expectedStatus': 'SUCCESSFUL',
          'description': 'Default test scenario',
        };
    }
  }

  /// Generate test transaction ID
  static String generateTestTransactionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'TEST_TX_$timestamp';
  }

  /// Validate test phone number
  static bool isValidTestPhoneNumber(String phoneNumber) {
    return testPhoneNumbers.values.contains(phoneNumber) ||
           phoneNumber.startsWith('26096'); // MTN Zambia prefix
  }

  /// Get sandbox headers for API requests
  static Map<String, String> getSandboxHeaders() {
    final config = getCurrentConfig(isProduction: false);
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_ACCESS_TOKEN', // Replace with actual token
      'X-Reference-Id': generateTestTransactionId(),
      'X-Target-Environment': config['targetEnvironment']!,
      'Ocp-Apim-Subscription-Key': config['subscriptionKey']!,
    };
  }

  /// Log test transaction for debugging
  static void logTestTransaction({
    required String transactionId,
    required String scenario,
    required String phoneNumber,
    required double amount,
    required String status,
  }) {
    _logger.i('''
    MTN Sandbox Test Transaction:
    - ID: $transactionId
    - Scenario: $scenario
    - Phone: $phoneNumber
    - Amount: K $amount
    - Status: $status
    - Timestamp: ${DateTime.now().toIso8601String()}
    ''');
  }

  /// Simulate sandbox response for testing
  static Map<String, dynamic> simulateSandboxResponse(String scenario) {
    final testScenario = getTestScenario(scenario);
    final transactionId = generateTestTransactionId();
    
    return {
      'transactionId': transactionId,
      'status': testScenario['expectedStatus'],
      'amount': testScenario['amount'],
      'currency': 'ZMW',
      'phoneNumber': testScenario['phoneNumber'],
      'timestamp': DateTime.now().toIso8601String(),
      'referenceId': 'MTN_REF_$transactionId',
      'message': testScenario['description'],
    };
  }

  /// Validate sandbox environment setup
  static Future<bool> validateSandboxSetup() async {
    try {
      _logger.i('Validating MTN sandbox setup...');
      
      // Check credentials
      if (!validateSandboxCredentials()) {
        _logger.e('Sandbox credentials validation failed');
        return false;
      }
      
      // Check test phone numbers
      if (testPhoneNumbers.values.any((phone) => phone.startsWith('YOUR_'))) {
        _logger.e('Test phone numbers not configured properly');
        return false;
      }
      
      // Simulate a test transaction
      final testResponse = simulateSandboxResponse('success');
      if (testResponse['status'] != 'SUCCESSFUL') {
        _logger.e('Sandbox simulation test failed');
        return false;
      }
      
      _logger.i('✅ MTN sandbox setup validation passed');
      return true;
      
    } catch (e) {
      _logger.e('Sandbox setup validation error: $e');
      return false;
    }
  }

  /// Get sandbox testing checklist
  static List<String> getSandboxTestingChecklist() {
    return [
      '✓ Configure sandbox credentials in MTNSandboxConfig',
      '✓ Set up test phone numbers for different scenarios',
      '✓ Test successful transaction flow',
      '✓ Test failed transaction handling',
      '✓ Test pending transaction status',
      '✓ Test invalid amount validation',
      '✓ Test network failure scenarios',
      '✓ Test real-time stream updates',
      '✓ Test cached data fallbacks',
      '✓ Validate transaction verification',
      '✓ Test balance updates',
      '✓ Verify error handling and user feedback',
    ];
  }
}
