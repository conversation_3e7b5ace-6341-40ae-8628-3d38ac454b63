/// Application-wide constants for Zambia Pay
class AppConstants {
  // API Endpoints
  static const String baseApiUrl = 'https://api.zambiapay.com/v1';
  static const String mtnApiUrl = 'https://momodeveloper.mtn.com/v1';
  static const String airtelApiUrl = 'https://openapiuat.airtel.africa/mw/v2';
  static const String zamtelApiUrl = 'https://api.zamtel.zm/v1';
  
  // Database Tables
  static const String transactionsTable = 'transactions';
  static const String usersTable = 'users';
  static const String offlineQueueTable = 'offline_queue';
  static const String utilityBillsTable = 'utility_bills';
  static const String contactsTable = 'contacts';
  static const String settingsTable = 'settings';
  
  // Shared Preferences Keys
  static const String keyUserId = 'user_id';
  static const String keyUserToken = 'user_token';
  static const String keyBiometricEnabled = 'biometric_enabled';
  static const String keySelectedLanguage = 'selected_language';
  static const String keyOfflineModeEnabled = 'offline_mode_enabled';
  static const String keyLastSyncTime = 'last_sync_time';
  static const String keyPinHash = 'pin_hash';
  static const String keyEncryptionKey = 'encryption_key';
  
  // Transaction Types
  static const String transactionTypeSend = 'SEND_MONEY';
  static const String transactionTypeReceive = 'RECEIVE_MONEY';
  static const String transactionTypeBillPayment = 'BILL_PAYMENT';
  static const String transactionTypeAirtime = 'AIRTIME_PURCHASE';
  static const String transactionTypeMerchant = 'MERCHANT_PAYMENT';
  static const String transactionTypeWithdraw = 'CASH_WITHDRAWAL';
  static const String transactionTypeDeposit = 'CASH_DEPOSIT';
  
  // Transaction Status
  static const String statusPending = 'PENDING';
  static const String statusCompleted = 'COMPLETED';
  static const String statusFailed = 'FAILED';
  static const String statusCancelled = 'CANCELLED';
  static const String statusQueued = 'QUEUED'; // For offline transactions
  static const String statusProcessing = 'PROCESSING';
  
  // Mobile Money Providers
  static const String providerMTN = 'MTN';
  static const String providerAirtel = 'AIRTEL';
  static const String providerZamtel = 'ZAMTEL';
  
  // Utility Types
  static const String utilityElectricity = 'ELECTRICITY';
  static const String utilityWater = 'WATER';
  static const String utilityInternet = 'INTERNET';
  static const String utilityTV = 'TV';
  
  // Error Codes
  static const String errorInsufficientFunds = 'INSUFFICIENT_FUNDS';
  static const String errorNetworkError = 'NETWORK_ERROR';
  static const String errorInvalidPin = 'INVALID_PIN';
  static const String errorAccountNotFound = 'ACCOUNT_NOT_FOUND';
  static const String errorTransactionLimitExceeded = 'LIMIT_EXCEEDED';
  static const String errorServiceUnavailable = 'SERVICE_UNAVAILABLE';
  static const String errorInvalidPhoneNumber = 'INVALID_PHONE_NUMBER';
  static const String errorBiometricNotAvailable = 'BIOMETRIC_NOT_AVAILABLE';
  
  // Validation Patterns
  static const String zambianPhonePattern = r'^(260|0)(9[567])\d{7}$';
  static const String pinPattern = r'^\d{4,6}$';
  static const String amountPattern = r'^\d+(\.\d{1,2})?$';
  
  // Currency Formatting
  static const String currencySymbol = 'K';
  static const String currencyCode = 'ZMW';
  static const int decimalPlaces = 2;
  
  // Limits
  static const double minTransactionAmount = 1.0;
  static const double maxTransactionAmount = 50000.0;
  static const double dailyTransactionLimit = 50000.0;
  static const double monthlyTransactionLimit = 500000.0;
  static const int maxPinAttempts = 3;
  static const int pinLockoutMinutes = 30;
  
  // Sync Settings
  static const int syncBatchSize = 50;
  static const int maxOfflineTransactions = 1000;
  static const int syncRetryDelaySeconds = 30;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;
  
  // Animation Durations
  static const int shortAnimationMs = 200;
  static const int mediumAnimationMs = 300;
  static const int longAnimationMs = 500;
  
  // Network Timeouts
  static const int connectionTimeoutMs = 30000;
  static const int receiveTimeoutMs = 60000;
  static const int sendTimeoutMs = 30000;
  
  // File Paths
  static const String logFilePath = 'logs/app.log';
  static const String backupPath = 'backups/';
  static const String tempPath = 'temp/';
  
  // Notification Channels
  static const String notificationChannelGeneral = 'general';
  static const String notificationChannelTransactions = 'transactions';
  static const String notificationChannelSync = 'sync';
  static const String notificationChannelSecurity = 'security';
  
  // Deep Link Schemes
  static const String deepLinkScheme = 'zambiapay';
  static const String deepLinkHost = 'app';
  
  // QR Code Settings
  static const int qrCodeSize = 200;
  static const String qrCodePrefix = 'ZPAY:';
  
  // Biometric Settings
  static const String biometricReason = 'Please authenticate to access your account';
  static const int biometricTimeoutSeconds = 30;
  
  // Logging Levels
  static const String logLevelDebug = 'DEBUG';
  static const String logLevelInfo = 'INFO';
  static const String logLevelWarning = 'WARNING';
  static const String logLevelError = 'ERROR';
  
  // Cache Settings
  static const int cacheExpiryHours = 24;
  static const int maxCacheSize = 100; // MB
  
  // Regional Settings
  static const List<String> zambianProvinces = [
    'Central',
    'Copperbelt',
    'Eastern',
    'Luapula',
    'Lusaka',
    'Muchinga',
    'Northern',
    'North-Western',
    'Southern',
    'Western'
  ];
  
  // Mobile Money Provider Codes (MVP Focus)
  static const Map<String, String> mobileMoneyProviderCodes = {
    'MTN': '96',
    'AIRTEL': '97',
    'ZAMTEL': '95',
  };

  // MVP Feature Categories
  static const List<String> mvpFeatures = [
    'Mobile Money Transfers',
    'Utility Payments',
    'Agent Locator',
    'Offline Sync',
    'QR Payments',
    'Transaction History',
    'Balance Inquiry',
    'Airtime Purchase',
  ];
}
