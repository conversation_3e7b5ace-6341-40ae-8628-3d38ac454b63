import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Data Usage Monitor for Zambian users with 2MB monthly limit
/// Tracks and manages data consumption to stay within budget
class DataUsageMonitor extends ChangeNotifier {
  static final DataUsageMonitor _instance = DataUsageMonitor._internal();
  factory DataUsageMonitor() => _instance;
  DataUsageMonitor._internal();

  final Logger _logger = Logger();
  
  // Data usage tracking
  double _monthlyDataUsage = 0.0; // in MB
  double _dailyDataUsage = 0.0; // in MB
  DateTime _monthStartDate = DateTime.now();
  DateTime _dayStartDate = DateTime.now();
  
  // Configuration for Zambian users
  static const double _monthlyDataLimit = 2.0; // 2MB per month
  static const double _dailyDataLimit = 0.1; // 100KB per day (conservative)
  static const double _warningThreshold = 0.8; // 80% warning
  static const double _criticalThreshold = 0.95; // 95% critical
  
  // Data usage categories
  final Map<String, double> _categoryUsage = {
    'balance_checks': 0.0,
    'transactions': 0.0,
    'sync': 0.0,
    'refresh': 0.0,
    'other': 0.0,
  };
  
  // Data compression settings
  bool _compressionEnabled = true;
  bool _imageCompressionEnabled = true;
  bool _backgroundSyncEnabled = false;
  
  // Getters
  double get monthlyDataUsage => _monthlyDataUsage;
  double get dailyDataUsage => _dailyDataUsage;
  double get monthlyDataLimit => _monthlyDataLimit;
  double get dailyDataLimit => _dailyDataLimit;
  double get remainingMonthlyData => _monthlyDataLimit - _monthlyDataUsage;
  double get remainingDailyData => _dailyDataLimit - _dailyDataUsage;
  double get monthlyUsagePercentage => (_monthlyDataUsage / _monthlyDataLimit) * 100;
  double get dailyUsagePercentage => (_dailyDataUsage / _dailyDataLimit) * 100;
  
  bool get isMonthlyWarning => monthlyUsagePercentage >= (_warningThreshold * 100);
  bool get isMonthlyCritical => monthlyUsagePercentage >= (_criticalThreshold * 100);
  bool get isDailyWarning => dailyUsagePercentage >= (_warningThreshold * 100);
  bool get isDailyCritical => dailyUsagePercentage >= (_criticalThreshold * 100);
  
  bool get isDataSaverMode => isMonthlyCritical || isDailyCritical;
  bool get canUseData => _monthlyDataUsage < _monthlyDataLimit && _dailyDataUsage < _dailyDataLimit;
  
  Map<String, double> get categoryUsage => Map.unmodifiable(_categoryUsage);
  
  /// Initialize data usage monitoring
  Future<void> initialize() async {
    try {
      _logger.i('Initializing DataUsageMonitor...');
      
      await _loadDataUsage();
      _checkForNewPeriods();
      
      // Enable aggressive data saving for Zambian users
      _enableZambianDataSaving();
      
      _logger.i('DataUsageMonitor initialized - Monthly: ${_monthlyDataUsage.toStringAsFixed(3)}MB');
    } catch (e) {
      _logger.e('Failed to initialize DataUsageMonitor: $e');
    }
  }
  
  /// Track data usage for an operation
  Future<void> trackDataUsage({
    required double dataUsedMB,
    required String category,
    String? operation,
  }) async {
    try {
      // Check for new periods
      _checkForNewPeriods();
      
      // Track usage
      _monthlyDataUsage += dataUsedMB;
      _dailyDataUsage += dataUsedMB;
      
      // Track by category
      if (_categoryUsage.containsKey(category)) {
        _categoryUsage[category] = (_categoryUsage[category] ?? 0.0) + dataUsedMB;
      } else {
        _categoryUsage['other'] = (_categoryUsage['other'] ?? 0.0) + dataUsedMB;
      }
      
      // Log usage
      _logger.i('Data used: ${dataUsedMB.toStringAsFixed(3)}MB for $category${operation != null ? ' ($operation)' : ''}');
      _logger.i('Monthly total: ${_monthlyDataUsage.toStringAsFixed(3)}MB / ${_monthlyDataLimit}MB');
      
      // Save to persistent storage
      await _saveDataUsage();
      
      // Check thresholds and notify
      _checkThresholds();
      
      notifyListeners();
      
    } catch (e) {
      _logger.e('Failed to track data usage: $e');
    }
  }
  
  /// Check if operation can use data
  bool canUseDataForOperation(double estimatedDataMB, {bool isEssential = false}) {
    // Always allow essential operations (like authentication)
    if (isEssential) return true;
    
    // Check daily limit
    if ((_dailyDataUsage + estimatedDataMB) > _dailyDataLimit) {
      _logger.w('Operation blocked: would exceed daily limit');
      return false;
    }
    
    // Check monthly limit
    if ((_monthlyDataUsage + estimatedDataMB) > _monthlyDataLimit) {
      _logger.w('Operation blocked: would exceed monthly limit');
      return false;
    }
    
    return true;
  }
  
  /// Get estimated data usage for common operations
  double getEstimatedDataUsage(String operation) {
    switch (operation.toLowerCase()) {
      case 'balance_check':
        return _compressionEnabled ? 0.005 : 0.01; // 5-10KB
      case 'transaction_list':
        return _compressionEnabled ? 0.02 : 0.05; // 20-50KB
      case 'transaction_details':
        return _compressionEnabled ? 0.008 : 0.015; // 8-15KB
      case 'send_money':
        return _compressionEnabled ? 0.01 : 0.02; // 10-20KB
      case 'receive_money':
        return _compressionEnabled ? 0.008 : 0.015; // 8-15KB
      case 'sync_transactions':
        return _compressionEnabled ? 0.03 : 0.08; // 30-80KB
      case 'full_refresh':
        return _compressionEnabled ? 0.05 : 0.12; // 50-120KB
      case 'profile_update':
        return _compressionEnabled ? 0.01 : 0.025; // 10-25KB
      default:
        return _compressionEnabled ? 0.01 : 0.02; // 10-20KB default
    }
  }
  
  /// Enable Zambian-specific data saving features
  void _enableZambianDataSaving() {
    _compressionEnabled = true;
    _imageCompressionEnabled = true;
    _backgroundSyncEnabled = false;
    
    _logger.i('Zambian data saving mode enabled');
  }
  
  /// Check for new day/month and reset counters
  void _checkForNewPeriods() {
    final now = DateTime.now();
    
    // Check for new month
    if (now.month != _monthStartDate.month || now.year != _monthStartDate.year) {
      _resetMonthlyUsage();
    }
    
    // Check for new day
    if (now.day != _dayStartDate.day || now.month != _dayStartDate.month || now.year != _dayStartDate.year) {
      _resetDailyUsage();
    }
  }
  
  /// Reset monthly usage
  void _resetMonthlyUsage() {
    _monthlyDataUsage = 0.0;
    _monthStartDate = DateTime(DateTime.now().year, DateTime.now().month, 1);
    
    // Reset category usage
    _categoryUsage.updateAll((key, value) => 0.0);
    
    _logger.i('Monthly data usage reset');
  }
  
  /// Reset daily usage
  void _resetDailyUsage() {
    _dailyDataUsage = 0.0;
    _dayStartDate = DateTime.now();
    
    _logger.i('Daily data usage reset');
  }
  
  /// Check usage thresholds and trigger warnings
  void _checkThresholds() {
    // Monthly warnings
    if (isMonthlyCritical && !_hasShownMonthlyCritical) {
      _showDataUsageAlert('Monthly data usage critical: ${monthlyUsagePercentage.toStringAsFixed(1)}%');
      _hasShownMonthlyCritical = true;
    } else if (isMonthlyWarning && !_hasShownMonthlyWarning) {
      _showDataUsageAlert('Monthly data usage warning: ${monthlyUsagePercentage.toStringAsFixed(1)}%');
      _hasShownMonthlyWarning = true;
    }
    
    // Daily warnings
    if (isDailyCritical && !_hasShownDailyCritical) {
      _showDataUsageAlert('Daily data usage critical: ${dailyUsagePercentage.toStringAsFixed(1)}%');
      _hasShownDailyCritical = true;
    } else if (isDailyWarning && !_hasShownDailyWarning) {
      _showDataUsageAlert('Daily data usage warning: ${dailyUsagePercentage.toStringAsFixed(1)}%');
      _hasShownDailyWarning = true;
    }
  }
  
  bool _hasShownMonthlyWarning = false;
  bool _hasShownMonthlyCritical = false;
  bool _hasShownDailyWarning = false;
  bool _hasShownDailyCritical = false;
  
  /// Show data usage alert
  void _showDataUsageAlert(String message) {
    _logger.w('DATA USAGE ALERT: $message');
    // In a real app, this would show a user notification
  }
  
  /// Load data usage from persistent storage
  Future<void> _loadDataUsage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load monthly usage
      _monthlyDataUsage = prefs.getDouble('monthly_data_usage') ?? 0.0;
      final monthStartString = prefs.getString('month_start_date');
      if (monthStartString != null) {
        _monthStartDate = DateTime.parse(monthStartString);
      }
      
      // Load daily usage
      _dailyDataUsage = prefs.getDouble('daily_data_usage') ?? 0.0;
      final dayStartString = prefs.getString('day_start_date');
      if (dayStartString != null) {
        _dayStartDate = DateTime.parse(dayStartString);
      }
      
      // Load category usage
      final categoryUsageString = prefs.getString('category_usage');
      if (categoryUsageString != null) {
        final categoryData = jsonDecode(categoryUsageString) as Map<String, dynamic>;
        categoryData.forEach((key, value) {
          _categoryUsage[key] = (value as num).toDouble();
        });
      }
      
    } catch (e) {
      _logger.e('Failed to load data usage: $e');
    }
  }
  
  /// Save data usage to persistent storage
  Future<void> _saveDataUsage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save monthly usage
      await prefs.setDouble('monthly_data_usage', _monthlyDataUsage);
      await prefs.setString('month_start_date', _monthStartDate.toIso8601String());
      
      // Save daily usage
      await prefs.setDouble('daily_data_usage', _dailyDataUsage);
      await prefs.setString('day_start_date', _dayStartDate.toIso8601String());
      
      // Save category usage
      await prefs.setString('category_usage', jsonEncode(_categoryUsage));
      
    } catch (e) {
      _logger.e('Failed to save data usage: $e');
    }
  }
  
  /// Get data usage summary
  Map<String, dynamic> getUsageSummary() {
    return {
      'monthly': {
        'used': _monthlyDataUsage,
        'limit': _monthlyDataLimit,
        'remaining': remainingMonthlyData,
        'percentage': monthlyUsagePercentage,
        'isWarning': isMonthlyWarning,
        'isCritical': isMonthlyCritical,
      },
      'daily': {
        'used': _dailyDataUsage,
        'limit': _dailyDataLimit,
        'remaining': remainingDailyData,
        'percentage': dailyUsagePercentage,
        'isWarning': isDailyWarning,
        'isCritical': isDailyCritical,
      },
      'categories': _categoryUsage,
      'settings': {
        'compressionEnabled': _compressionEnabled,
        'imageCompressionEnabled': _imageCompressionEnabled,
        'backgroundSyncEnabled': _backgroundSyncEnabled,
        'isDataSaverMode': isDataSaverMode,
      },
    };
  }
  
  /// Reset all data usage (for testing or new month)
  Future<void> resetAllUsage() async {
    _monthlyDataUsage = 0.0;
    _dailyDataUsage = 0.0;
    _categoryUsage.updateAll((key, value) => 0.0);
    
    _hasShownMonthlyWarning = false;
    _hasShownMonthlyCritical = false;
    _hasShownDailyWarning = false;
    _hasShownDailyCritical = false;
    
    await _saveDataUsage();
    notifyListeners();
    
    _logger.i('All data usage reset');
  }
}
