import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../constants/app_constants.dart';
import '../../features/mobile_money/data/services/offline_storage.dart';

/// Comprehensive error handling for mobile money streaming system
/// Provides graceful fallbacks to cached data and proper user feedback
class MomoErrorHandler {
  static final Logger _logger = Logger();

  /// Handle stream errors with appropriate fallbacks
  static Future<T> handleStreamError<T>({
    required Future<T> Function() operation,
    required Future<T> Function() fallback,
    String? operationName,
    bool logError = true,
  }) async {
    try {
      return await operation();
    } catch (error) {
      if (logError) {
        _logger.e('${operationName ?? 'Operation'} failed: $error');
      }
      
      try {
        return await fallback();
      } catch (fallbackError) {
        _logger.e('Fallback also failed: $fallbackError');
        rethrow;
      }
    }
  }

  /// Handle network-related errors
  static Future<T> handleNetworkError<T>({
    required Future<T> Function() networkOperation,
    required Future<T> Function() offlineOperation,
    String? operationName,
  }) async {
    try {
      return await networkOperation();
    } on SocketException catch (e) {
      _logger.w('Network error in ${operationName ?? 'operation'}: $e');
      return await offlineOperation();
    } on TimeoutException catch (e) {
      _logger.w('Timeout error in ${operationName ?? 'operation'}: $e');
      return await offlineOperation();
    } catch (e) {
      _logger.e('Unexpected error in ${operationName ?? 'operation'}: $e');
      return await offlineOperation();
    }
  }

  /// Get error message for user display
  static String getUserFriendlyErrorMessage(dynamic error) {
    if (error is SocketException) {
      return 'No internet connection. Showing cached data.';
    } else if (error is TimeoutException) {
      return 'Request timed out. Please try again.';
    } else if (error is FormatException) {
      return 'Invalid data format received.';
    } else if (error.toString().contains('authentication')) {
      return 'Authentication failed. Please check your credentials.';
    } else if (error.toString().contains('insufficient')) {
      return 'Insufficient balance for this transaction.';
    } else if (error.toString().contains('limit')) {
      return 'Transaction limit exceeded.';
    } else {
      return 'An error occurred. Please try again later.';
    }
  }

  /// Show error snackbar to user
  static void showErrorSnackbar(BuildContext context, dynamic error) {
    final message = getUserFriendlyErrorMessage(error);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Handle transaction errors with specific fallbacks
  static Future<List<dynamic>> handleTransactionStreamError(dynamic error) async {
    _logger.e('Transaction stream error: $error');
    
    try {
      // Fallback to cached transactions
      final cachedTransactions = await OfflineStorage.getCachedTransactions(limit: 20);
      _logger.i('Fallback: Retrieved ${cachedTransactions.length} cached transactions');
      return cachedTransactions;
    } catch (cacheError) {
      _logger.e('Cache fallback failed: $cacheError');
      return []; // Return empty list as last resort
    }
  }

  /// Handle balance errors with cached fallbacks
  static Future<Map<String, double>> handleBalanceStreamError(dynamic error) async {
    _logger.e('Balance stream error: $error');
    
    try {
      // Fallback to cached balances
      final cachedBalances = <String, double>{};
      final providers = [AppConstants.providerMTN, AppConstants.providerAirtel, AppConstants.providerZamtel];
      
      for (final provider in providers) {
        cachedBalances[provider] = await OfflineStorage.getCachedBalance(provider);
      }
      
      _logger.i('Fallback: Retrieved cached balances for ${cachedBalances.length} providers');
      return cachedBalances;
    } catch (cacheError) {
      _logger.e('Balance cache fallback failed: $cacheError');
      return {}; // Return empty map as last resort
    }
  }

  /// Retry operation with exponential backoff
  static Future<T> retryWithBackoff<T>({
    required Future<T> Function() operation,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    String? operationName,
  }) async {
    int attempt = 0;
    Duration delay = initialDelay;
    
    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        attempt++;
        
        if (attempt >= maxRetries) {
          _logger.e('${operationName ?? 'Operation'} failed after $maxRetries attempts: $error');
          rethrow;
        }
        
        _logger.w('${operationName ?? 'Operation'} attempt $attempt failed: $error. Retrying in ${delay.inSeconds}s...');
        await Future.delayed(delay);
        delay = Duration(milliseconds: (delay.inMilliseconds * backoffMultiplier).round());
      }
    }
    
    throw Exception('Max retries exceeded');
  }

  /// Handle provider-specific errors
  static String handleProviderError(String provider, dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    switch (provider) {
      case AppConstants.providerMTN:
        if (errorString.contains('invalid msisdn')) {
          return 'Invalid MTN phone number format';
        } else if (errorString.contains('insufficient funds')) {
          return 'Insufficient MTN Mobile Money balance';
        } else if (errorString.contains('service unavailable')) {
          return 'MTN Mobile Money service is temporarily unavailable';
        }
        break;
        
      case AppConstants.providerAirtel:
        if (errorString.contains('invalid subscriber')) {
          return 'Invalid Airtel Money phone number';
        } else if (errorString.contains('transaction limit')) {
          return 'Airtel Money transaction limit exceeded';
        } else if (errorString.contains('service down')) {
          return 'Airtel Money service is currently down';
        }
        break;
        
      case AppConstants.providerZamtel:
        if (errorString.contains('invalid account')) {
          return 'Invalid Zamtel Kwacha account';
        } else if (errorString.contains('balance insufficient')) {
          return 'Insufficient Zamtel Kwacha balance';
        } else if (errorString.contains('maintenance')) {
          return 'Zamtel Kwacha is under maintenance';
        }
        break;
    }
    
    return getUserFriendlyErrorMessage(error);
  }

  /// Create error widget for UI display
  static Widget createErrorWidget({
    required String message,
    VoidCallback? onRetry,
    IconData? icon,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: 48,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Log error with context
  static void logErrorWithContext({
    required dynamic error,
    required String operation,
    Map<String, dynamic>? context,
    StackTrace? stackTrace,
  }) {
    final contextString = context != null ? ' Context: $context' : '';
    _logger.e('Error in $operation: $error$contextString');
  }

  /// Check if error is recoverable
  static bool isRecoverableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // Network errors are usually recoverable
    if (error is SocketException || error is TimeoutException) {
      return true;
    }
    
    // Temporary service errors
    if (errorString.contains('service unavailable') ||
        errorString.contains('temporarily') ||
        errorString.contains('maintenance') ||
        errorString.contains('timeout')) {
      return true;
    }
    
    // Authentication errors might be recoverable with re-auth
    if (errorString.contains('authentication') || errorString.contains('unauthorized')) {
      return true;
    }
    
    return false;
  }

  /// Get retry delay based on error type
  static Duration getRetryDelay(dynamic error, int attemptNumber) {
    if (error is SocketException) {
      // Longer delay for network errors
      return Duration(seconds: 5 * attemptNumber);
    } else if (error is TimeoutException) {
      // Medium delay for timeouts
      return Duration(seconds: 3 * attemptNumber);
    } else {
      // Standard exponential backoff
      return Duration(seconds: 2 * attemptNumber);
    }
  }
}
