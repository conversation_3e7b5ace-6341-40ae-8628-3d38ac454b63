import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;

/// Network Quality Detector for Zambian mobile networks
/// Detects and adapts to 2G/3G/4G/WiFi conditions for optimal data usage
class NetworkQualityDetector extends ChangeNotifier {
  static final NetworkQualityDetector _instance = NetworkQualityDetector._internal();
  factory NetworkQualityDetector() => _instance;
  NetworkQualityDetector._internal();

  final Logger _logger = Logger();
  final Connectivity _connectivity = Connectivity();

  // Network state
  ConnectivityResult _currentConnection = ConnectivityResult.none;
  NetworkQuality _networkQuality = NetworkQuality.unknown;
  NetworkSpeed _networkSpeed = NetworkSpeed.unknown;
  double _latency = 0.0; // in milliseconds
  double _downloadSpeed = 0.0; // in Mbps
  bool _isDetecting = false;

  // Quality detection history
  final List<NetworkQualityMeasurement> _qualityHistory = [];
  static const int _maxHistorySize = 10;

  // Test endpoints (lightweight for Zambian networks)
  static const String _speedTestUrl = 'https://httpbin.org/bytes/1024'; // 1KB test
  static const String _latencyTestUrl = 'https://httpbin.org/get'; // Minimal response
  static const Duration _testTimeout = Duration(seconds: 10);

  // Getters
  ConnectivityResult get currentConnection => _currentConnection;
  NetworkQuality get networkQuality => _networkQuality;
  NetworkSpeed get networkSpeed => _networkSpeed;
  double get latency => _latency;
  double get downloadSpeed => _downloadSpeed;
  bool get isDetecting => _isDetecting;
  bool get isOnline => _currentConnection != ConnectivityResult.none;
  bool get isWiFi => _currentConnection == ConnectivityResult.wifi;
  bool get isMobile => _currentConnection == ConnectivityResult.mobile;
  
  List<NetworkQualityMeasurement> get qualityHistory => List.unmodifiable(_qualityHistory);

  /// Initialize network quality detection
  Future<void> initialize() async {
    try {
      _logger.i('Initializing NetworkQualityDetector...');
      
      // Check initial connectivity
      _currentConnection = await _connectivity.checkConnectivity();
      
      // Listen to connectivity changes
      _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);
      
      // Perform initial quality detection
      await detectNetworkQuality();
      
      _logger.i('NetworkQualityDetector initialized - Quality: $_networkQuality');
    } catch (e) {
      _logger.e('Failed to initialize NetworkQualityDetector: $e');
    }
  }

  /// Detect current network quality
  Future<void> detectNetworkQuality() async {
    if (_isDetecting) {
      _logger.w('Quality detection already in progress');
      return;
    }

    _isDetecting = true;
    notifyListeners();

    try {
      _logger.i('Detecting network quality for $_currentConnection...');

      if (_currentConnection == ConnectivityResult.none) {
        _setNetworkQuality(NetworkQuality.offline, NetworkSpeed.none);
        return;
      }

      // Perform speed and latency tests
      final latencyResult = await _measureLatency();
      final speedResult = await _measureDownloadSpeed();

      // Determine quality based on connection type and measurements
      final quality = _determineQuality(latencyResult, speedResult);
      final speed = _determineSpeed(speedResult);

      _setNetworkQuality(quality, speed);
      _recordMeasurement(latencyResult, speedResult, quality, speed);

      _logger.i('Network quality detected: $quality (${speedResult.toStringAsFixed(2)} Mbps, ${latencyResult.toStringAsFixed(0)}ms)');

    } catch (e) {
      _logger.e('Failed to detect network quality: $e');
      _setNetworkQuality(NetworkQuality.unknown, NetworkSpeed.unknown);
    } finally {
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Measure network latency
  Future<double> _measureLatency() async {
    try {
      final stopwatch = Stopwatch()..start();
      
      final response = await http.get(
        Uri.parse(_latencyTestUrl),
        headers: {'Cache-Control': 'no-cache'},
      ).timeout(_testTimeout);
      
      stopwatch.stop();
      
      if (response.statusCode == 200) {
        return stopwatch.elapsedMilliseconds.toDouble();
      } else {
        throw Exception('Latency test failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.w('Latency measurement failed: $e');
      return 9999.0; // High latency indicates poor connection
    }
  }

  /// Measure download speed
  Future<double> _measureDownloadSpeed() async {
    try {
      final stopwatch = Stopwatch()..start();
      
      final response = await http.get(
        Uri.parse(_speedTestUrl),
        headers: {'Cache-Control': 'no-cache'},
      ).timeout(_testTimeout);
      
      stopwatch.stop();
      
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes.length;
        final seconds = stopwatch.elapsedMilliseconds / 1000.0;
        final bitsPerSecond = (bytes * 8) / seconds;
        final mbps = bitsPerSecond / (1024 * 1024);
        
        return mbps;
      } else {
        throw Exception('Speed test failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.w('Speed measurement failed: $e');
      return 0.0; // No speed indicates poor connection
    }
  }

  /// Determine network quality based on measurements
  NetworkQuality _determineQuality(double latency, double speedMbps) {
    // WiFi quality determination
    if (_currentConnection == ConnectivityResult.wifi) {
      if (latency < 50 && speedMbps > 5.0) return NetworkQuality.excellent;
      if (latency < 100 && speedMbps > 2.0) return NetworkQuality.good;
      if (latency < 200 && speedMbps > 0.5) return NetworkQuality.fair;
      return NetworkQuality.poor;
    }

    // Mobile network quality determination (Zambian context)
    if (_currentConnection == ConnectivityResult.mobile) {
      // 4G/LTE characteristics
      if (latency < 100 && speedMbps > 2.0) return NetworkQuality.excellent;
      
      // 3G characteristics
      if (latency < 300 && speedMbps > 0.5) return NetworkQuality.good;
      
      // 2G characteristics
      if (latency < 1000 && speedMbps > 0.1) return NetworkQuality.fair;
      
      // Very poor connection
      return NetworkQuality.poor;
    }

    return NetworkQuality.unknown;
  }

  /// Determine network speed category
  NetworkSpeed _determineSpeed(double speedMbps) {
    if (speedMbps > 10.0) return NetworkSpeed.veryFast; // 4G+/WiFi
    if (speedMbps > 2.0) return NetworkSpeed.fast;      // Good 4G
    if (speedMbps > 0.5) return NetworkSpeed.medium;    // 3G
    if (speedMbps > 0.1) return NetworkSpeed.slow;      // 2G
    if (speedMbps > 0.0) return NetworkSpeed.verySlow;  // Poor 2G
    return NetworkSpeed.none;
  }

  /// Set network quality and notify listeners
  void _setNetworkQuality(NetworkQuality quality, NetworkSpeed speed) {
    final changed = _networkQuality != quality || _networkSpeed != speed;
    
    _networkQuality = quality;
    _networkSpeed = speed;
    
    if (changed) {
      notifyListeners();
    }
  }

  /// Record quality measurement
  void _recordMeasurement(double latency, double speed, NetworkQuality quality, NetworkSpeed speedCategory) {
    final measurement = NetworkQualityMeasurement(
      timestamp: DateTime.now(),
      connectionType: _currentConnection,
      quality: quality,
      speed: speedCategory,
      latencyMs: latency,
      downloadSpeedMbps: speed,
    );

    _qualityHistory.add(measurement);
    
    // Keep only recent measurements
    if (_qualityHistory.length > _maxHistorySize) {
      _qualityHistory.removeAt(0);
    }

    _latency = latency;
    _downloadSpeed = speed;
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    _logger.i('Connectivity changed: $_currentConnection -> $result');
    _currentConnection = result;
    
    // Detect quality for new connection
    detectNetworkQuality();
  }

  /// Get recommended settings for current network quality
  NetworkSettings getRecommendedSettings() {
    switch (_networkQuality) {
      case NetworkQuality.excellent:
        return NetworkSettings(
          enableImages: true,
          enableBackgroundSync: true,
          refreshInterval: const Duration(minutes: 1),
          requestTimeout: const Duration(seconds: 10),
          enableCompression: false,
          maxConcurrentRequests: 5,
        );
      
      case NetworkQuality.good:
        return NetworkSettings(
          enableImages: true,
          enableBackgroundSync: true,
          refreshInterval: const Duration(minutes: 2),
          requestTimeout: const Duration(seconds: 15),
          enableCompression: true,
          maxConcurrentRequests: 3,
        );
      
      case NetworkQuality.fair:
        return NetworkSettings(
          enableImages: false,
          enableBackgroundSync: false,
          refreshInterval: const Duration(minutes: 5),
          requestTimeout: const Duration(seconds: 20),
          enableCompression: true,
          maxConcurrentRequests: 2,
        );
      
      case NetworkQuality.poor:
        return NetworkSettings(
          enableImages: false,
          enableBackgroundSync: false,
          refreshInterval: const Duration(minutes: 10),
          requestTimeout: const Duration(seconds: 30),
          enableCompression: true,
          maxConcurrentRequests: 1,
        );
      
      case NetworkQuality.offline:
        return NetworkSettings(
          enableImages: false,
          enableBackgroundSync: false,
          refreshInterval: const Duration(hours: 1),
          requestTimeout: const Duration(seconds: 5),
          enableCompression: true,
          maxConcurrentRequests: 0,
        );
      
      case NetworkQuality.unknown:
        return NetworkSettings(
          enableImages: false,
          enableBackgroundSync: false,
          refreshInterval: const Duration(minutes: 5),
          requestTimeout: const Duration(seconds: 15),
          enableCompression: true,
          maxConcurrentRequests: 2,
        );
    }
  }

  /// Get network quality description for UI
  String getQualityDescription() {
    switch (_networkQuality) {
      case NetworkQuality.excellent:
        return 'Excellent connection';
      case NetworkQuality.good:
        return 'Good connection';
      case NetworkQuality.fair:
        return 'Fair connection - data saver active';
      case NetworkQuality.poor:
        return 'Poor connection - limited features';
      case NetworkQuality.offline:
        return 'No connection - offline mode';
      case NetworkQuality.unknown:
        return 'Checking connection...';
    }
  }

  /// Get average quality from recent measurements
  NetworkQuality getAverageQuality() {
    if (_qualityHistory.isEmpty) return NetworkQuality.unknown;
    
    final recentMeasurements = _qualityHistory.take(5).toList();
    final qualityScores = recentMeasurements.map((m) => m.quality.index).toList();
    final averageScore = qualityScores.reduce((a, b) => a + b) / qualityScores.length;
    
    return NetworkQuality.values[averageScore.round().clamp(0, NetworkQuality.values.length - 1)];
  }

  /// Check if network is suitable for operation
  bool isSuitableForOperation(String operation) {
    switch (operation.toLowerCase()) {
      case 'critical': // Authentication, send money
        return _networkQuality != NetworkQuality.offline;
      case 'important': // Balance check, transaction history
        return _networkQuality.index >= NetworkQuality.poor.index;
      case 'normal': // Refresh, sync
        return _networkQuality.index >= NetworkQuality.fair.index;
      case 'background': // Auto-sync, updates
        return _networkQuality.index >= NetworkQuality.good.index;
      default:
        return _networkQuality.index >= NetworkQuality.fair.index;
    }
  }
}

/// Network quality enumeration
enum NetworkQuality {
  offline,   // No connection
  poor,      // 2G or very slow
  fair,      // Slow 3G
  good,      // Fast 3G or slow 4G
  excellent, // Fast 4G or WiFi
  unknown,   // Unable to determine
}

/// Network speed enumeration
enum NetworkSpeed {
  none,      // No connection
  verySlow,  // < 0.1 Mbps
  slow,      // 0.1 - 0.5 Mbps
  medium,    // 0.5 - 2 Mbps
  fast,      // 2 - 10 Mbps
  veryFast,  // > 10 Mbps
  unknown,   // Unable to determine
}

/// Network quality measurement data class
class NetworkQualityMeasurement {
  final DateTime timestamp;
  final ConnectivityResult connectionType;
  final NetworkQuality quality;
  final NetworkSpeed speed;
  final double latencyMs;
  final double downloadSpeedMbps;

  const NetworkQualityMeasurement({
    required this.timestamp,
    required this.connectionType,
    required this.quality,
    required this.speed,
    required this.latencyMs,
    required this.downloadSpeedMbps,
  });
}

/// Network settings based on quality
class NetworkSettings {
  final bool enableImages;
  final bool enableBackgroundSync;
  final Duration refreshInterval;
  final Duration requestTimeout;
  final bool enableCompression;
  final int maxConcurrentRequests;

  const NetworkSettings({
    required this.enableImages,
    required this.enableBackgroundSync,
    required this.refreshInterval,
    required this.requestTimeout,
    required this.enableCompression,
    required this.maxConcurrentRequests,
  });
}
