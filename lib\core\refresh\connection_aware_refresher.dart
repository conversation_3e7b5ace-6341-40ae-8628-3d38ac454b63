import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../network/network_quality_detector.dart';
import '../data_usage/data_usage_monitor.dart';

/// Connection-aware refresher that adapts behavior based on network conditions
/// Optimized for Zambian mobile networks (2G/3G/4G/WiFi)
class ConnectionAwareRefresher extends ChangeNotifier {
  static final ConnectionAwareRefresher _instance = ConnectionAwareRefresher._internal();
  factory ConnectionAwareRefresher() => _instance;
  ConnectionAwareRefresher._internal();

  final Logger _logger = Logger();
  final NetworkQualityDetector _networkDetector = NetworkQualityDetector();
  final DataUsageMonitor _dataMonitor = DataUsageMonitor();

  // Refresh strategies by network type
  final Map<NetworkQuality, RefreshStrategy> _strategies = {};
  
  // Current refresh state
  RefreshStrategy? _currentStrategy;
  Timer? _adaptiveRefreshTimer;
  bool _isAdaptiveRefreshEnabled = true;

  /// Initialize connection-aware refreshing
  Future<void> initialize() async {
    try {
      _logger.i('Initializing ConnectionAwareRefresher...');
      
      await _networkDetector.initialize();
      await _dataMonitor.initialize();
      
      _setupRefreshStrategies();
      _updateCurrentStrategy();
      
      // Listen to network quality changes
      _networkDetector.addListener(_onNetworkQualityChanged);
      
      _logger.i('ConnectionAwareRefresher initialized');
    } catch (e) {
      _logger.e('Failed to initialize ConnectionAwareRefresher: $e');
    }
  }

  /// Setup refresh strategies for different network qualities
  void _setupRefreshStrategies() {
    _strategies[NetworkQuality.excellent] = RefreshStrategy(
      name: 'Excellent (WiFi/4G+)',
      refreshInterval: const Duration(minutes: 1),
      requestTimeout: const Duration(seconds: 10),
      enableBackgroundRefresh: true,
      enableImageLoading: true,
      enableCompression: false,
      maxConcurrentRequests: 5,
      retryAttempts: 3,
      retryDelay: const Duration(seconds: 2),
      dataUsageMultiplier: 1.0,
    );

    _strategies[NetworkQuality.good] = RefreshStrategy(
      name: 'Good (4G)',
      refreshInterval: const Duration(minutes: 2),
      requestTimeout: const Duration(seconds: 15),
      enableBackgroundRefresh: true,
      enableImageLoading: true,
      enableCompression: true,
      maxConcurrentRequests: 3,
      retryAttempts: 3,
      retryDelay: const Duration(seconds: 3),
      dataUsageMultiplier: 0.8,
    );

    _strategies[NetworkQuality.fair] = RefreshStrategy(
      name: 'Fair (3G)',
      refreshInterval: const Duration(minutes: 5),
      requestTimeout: const Duration(seconds: 20),
      enableBackgroundRefresh: false,
      enableImageLoading: false,
      enableCompression: true,
      maxConcurrentRequests: 2,
      retryAttempts: 2,
      retryDelay: const Duration(seconds: 5),
      dataUsageMultiplier: 0.6,
    );

    _strategies[NetworkQuality.poor] = RefreshStrategy(
      name: 'Poor (2G)',
      refreshInterval: const Duration(minutes: 10),
      requestTimeout: const Duration(seconds: 30),
      enableBackgroundRefresh: false,
      enableImageLoading: false,
      enableCompression: true,
      maxConcurrentRequests: 1,
      retryAttempts: 1,
      retryDelay: const Duration(seconds: 10),
      dataUsageMultiplier: 0.4,
    );

    _strategies[NetworkQuality.offline] = RefreshStrategy(
      name: 'Offline',
      refreshInterval: const Duration(hours: 1),
      requestTimeout: const Duration(seconds: 5),
      enableBackgroundRefresh: false,
      enableImageLoading: false,
      enableCompression: true,
      maxConcurrentRequests: 0,
      retryAttempts: 0,
      retryDelay: const Duration(seconds: 30),
      dataUsageMultiplier: 0.0,
    );

    _strategies[NetworkQuality.unknown] = RefreshStrategy(
      name: 'Unknown',
      refreshInterval: const Duration(minutes: 5),
      requestTimeout: const Duration(seconds: 15),
      enableBackgroundRefresh: false,
      enableImageLoading: false,
      enableCompression: true,
      maxConcurrentRequests: 2,
      retryAttempts: 2,
      retryDelay: const Duration(seconds: 5),
      dataUsageMultiplier: 0.7,
    );
  }

  /// Update current strategy based on network quality
  void _updateCurrentStrategy() {
    final networkQuality = _networkDetector.networkQuality;
    final newStrategy = _strategies[networkQuality];
    
    if (newStrategy != _currentStrategy) {
      _currentStrategy = newStrategy;
      _logger.i('Refresh strategy updated: ${newStrategy?.name}');
      
      // Restart adaptive refresh timer with new interval
      _restartAdaptiveRefresh();
      
      notifyListeners();
    }
  }

  /// Handle network quality changes
  void _onNetworkQualityChanged() {
    _updateCurrentStrategy();
  }

  /// Perform connection-aware refresh
  Future<T> performRefresh<T>({
    required Future<T> Function() refreshFunction,
    required Future<T> Function() fallbackFunction,
    String? operationName,
    bool isEssential = false,
  }) async {
    final strategy = _currentStrategy;
    if (strategy == null) {
      _logger.w('No refresh strategy available, using fallback');
      return await fallbackFunction();
    }

    // Check if we can perform refresh based on network quality
    if (!_canPerformRefresh(operationName, isEssential)) {
      _logger.i('Refresh blocked by network conditions, using fallback');
      return await fallbackFunction();
    }

    // Estimate data usage
    final estimatedDataUsage = _estimateDataUsage(operationName, strategy);
    
    // Check data usage limits
    if (!_dataMonitor.canUseDataForOperation(estimatedDataUsage, isEssential: isEssential)) {
      _logger.w('Refresh blocked by data usage limits, using fallback');
      return await fallbackFunction();
    }

    // Perform refresh with strategy-specific settings
    try {
      _logger.i('Performing refresh with strategy: ${strategy.name}');
      
      final result = await refreshFunction().timeout(strategy.requestTimeout);
      
      // Track data usage
      await _dataMonitor.trackDataUsage(
        dataUsedMB: estimatedDataUsage,
        category: 'refresh',
        operation: operationName,
      );
      
      return result;
      
    } catch (e) {
      _logger.e('Refresh failed: $e');
      
      // Retry if strategy allows
      if (strategy.retryAttempts > 0) {
        return await _retryRefresh(
          refreshFunction: refreshFunction,
          fallbackFunction: fallbackFunction,
          strategy: strategy,
          operationName: operationName,
          isEssential: isEssential,
        );
      }
      
      return await fallbackFunction();
    }
  }

  /// Retry refresh with exponential backoff
  Future<T> _retryRefresh<T>({
    required Future<T> Function() refreshFunction,
    required Future<T> Function() fallbackFunction,
    required RefreshStrategy strategy,
    String? operationName,
    bool isEssential = false,
  }) async {
    for (int attempt = 1; attempt <= strategy.retryAttempts; attempt++) {
      try {
        _logger.i('Retry attempt $attempt/${strategy.retryAttempts}');
        
        // Wait before retry
        await Future.delayed(strategy.retryDelay * attempt);
        
        // Check if network conditions changed
        if (!_canPerformRefresh(operationName, isEssential)) {
          break;
        }
        
        final result = await refreshFunction().timeout(strategy.requestTimeout);
        
        _logger.i('Retry successful on attempt $attempt');
        return result;
        
      } catch (e) {
        _logger.w('Retry attempt $attempt failed: $e');
        
        if (attempt == strategy.retryAttempts) {
          _logger.e('All retry attempts exhausted');
        }
      }
    }
    
    return await fallbackFunction();
  }

  /// Check if refresh can be performed
  bool _canPerformRefresh(String? operationName, bool isEssential) {
    final networkQuality = _networkDetector.networkQuality;
    
    // Always allow essential operations if we have any connection
    if (isEssential && networkQuality != NetworkQuality.offline) {
      return true;
    }
    
    // Block non-essential operations on poor networks
    if (!isEssential && networkQuality == NetworkQuality.poor) {
      return false;
    }
    
    // Block all operations when offline
    if (networkQuality == NetworkQuality.offline) {
      return false;
    }
    
    return true;
  }

  /// Estimate data usage based on operation and strategy
  double _estimateDataUsage(String? operationName, RefreshStrategy strategy) {
    double baseUsage = _dataMonitor.getEstimatedDataUsage(operationName ?? 'refresh');
    return baseUsage * strategy.dataUsageMultiplier;
  }

  /// Start adaptive refresh timer
  void _restartAdaptiveRefresh() {
    _adaptiveRefreshTimer?.cancel();
    
    if (!_isAdaptiveRefreshEnabled || _currentStrategy == null) {
      return;
    }
    
    _adaptiveRefreshTimer = Timer.periodic(_currentStrategy!.refreshInterval, (timer) {
      _performAdaptiveRefresh();
    });
  }

  /// Perform adaptive background refresh
  void _performAdaptiveRefresh() {
    if (!_currentStrategy!.enableBackgroundRefresh) {
      return;
    }
    
    _logger.i('Performing adaptive background refresh');
    // This would trigger background refresh in the app
    // Implementation depends on specific app requirements
  }

  /// Get current refresh strategy
  RefreshStrategy? getCurrentStrategy() => _currentStrategy;

  /// Get strategy for specific network quality
  RefreshStrategy? getStrategyForQuality(NetworkQuality quality) => _strategies[quality];

  /// Enable/disable adaptive refresh
  void setAdaptiveRefreshEnabled(bool enabled) {
    _isAdaptiveRefreshEnabled = enabled;
    
    if (enabled) {
      _restartAdaptiveRefresh();
    } else {
      _adaptiveRefreshTimer?.cancel();
    }
    
    _logger.i('Adaptive refresh ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Get refresh recommendations for current network
  Map<String, dynamic> getRefreshRecommendations() {
    final strategy = _currentStrategy;
    final networkQuality = _networkDetector.networkQuality;
    
    return {
      'networkQuality': networkQuality.name,
      'strategy': strategy?.toMap(),
      'recommendations': _getRecommendationsForQuality(networkQuality),
    };
  }

  /// Get specific recommendations for network quality
  List<String> _getRecommendationsForQuality(NetworkQuality quality) {
    switch (quality) {
      case NetworkQuality.excellent:
        return [
          'All features available',
          'Real-time updates enabled',
          'High-quality images loaded',
        ];
      
      case NetworkQuality.good:
        return [
          'Most features available',
          'Compressed images for data saving',
          'Regular updates enabled',
        ];
      
      case NetworkQuality.fair:
        return [
          'Essential features only',
          'Images disabled to save data',
          'Manual refresh recommended',
        ];
      
      case NetworkQuality.poor:
        return [
          'Limited functionality',
          'Use cached data when possible',
          'Avoid unnecessary refreshes',
        ];
      
      case NetworkQuality.offline:
        return [
          'Offline mode active',
          'Using cached data only',
          'Connect to internet for updates',
        ];
      
      case NetworkQuality.unknown:
        return [
          'Network quality unknown',
          'Conservative data usage',
          'Manual refresh available',
        ];
    }
  }

  @override
  void dispose() {
    _adaptiveRefreshTimer?.cancel();
    _networkDetector.removeListener(_onNetworkQualityChanged);
    super.dispose();
  }
}

/// Refresh strategy configuration
class RefreshStrategy {
  final String name;
  final Duration refreshInterval;
  final Duration requestTimeout;
  final bool enableBackgroundRefresh;
  final bool enableImageLoading;
  final bool enableCompression;
  final int maxConcurrentRequests;
  final int retryAttempts;
  final Duration retryDelay;
  final double dataUsageMultiplier;

  const RefreshStrategy({
    required this.name,
    required this.refreshInterval,
    required this.requestTimeout,
    required this.enableBackgroundRefresh,
    required this.enableImageLoading,
    required this.enableCompression,
    required this.maxConcurrentRequests,
    required this.retryAttempts,
    required this.retryDelay,
    required this.dataUsageMultiplier,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'refreshInterval': refreshInterval.inSeconds,
      'requestTimeout': requestTimeout.inSeconds,
      'enableBackgroundRefresh': enableBackgroundRefresh,
      'enableImageLoading': enableImageLoading,
      'enableCompression': enableCompression,
      'maxConcurrentRequests': maxConcurrentRequests,
      'retryAttempts': retryAttempts,
      'retryDelay': retryDelay.inSeconds,
      'dataUsageMultiplier': dataUsageMultiplier,
    };
  }
}
