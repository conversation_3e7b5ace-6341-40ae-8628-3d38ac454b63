import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Smart Refresh Controller with exponential backoff for Zambian data-saving
/// Implements intelligent refresh strategies to minimize data usage while maintaining UX
class SmartRefreshController extends ChangeNotifier {
  static final SmartRefreshController _instance = SmartRefreshController._internal();
  factory SmartRefreshController() => _instance;
  SmartRefreshController._internal();

  final Logger _logger = Logger();
  final Connectivity _connectivity = Connectivity();

  // Refresh state management
  bool _isRefreshing = false;
  bool _isLoading = false;
  DateTime? _lastRefreshTime;
  int _failureCount = 0;
  Timer? _backoffTimer;

  // Data usage tracking
  double _monthlyDataUsage = 0.0; // in MB
  static const double _monthlyDataLimit = 2.0; // 2MB limit for Zambian users
  DateTime _monthStartDate = DateTime.now();

  // Network quality tracking
  ConnectivityResult _currentConnection = ConnectivityResult.none;
  NetworkQuality _networkQuality = NetworkQuality.unknown;

  // Exponential backoff configuration
  static const Duration _baseDelay = Duration(seconds: 2);
  static const Duration _maxDelay = Duration(minutes: 5);
  static const int _maxRetries = 5;

  // Getters
  bool get isRefreshing => _isRefreshing;
  bool get isLoading => _isLoading;
  bool get canRefresh => !_isRefreshing && !_isLoading && _hasDataBudget();
  double get monthlyDataUsage => _monthlyDataUsage;
  double get dataUsagePercentage => (_monthlyDataUsage / _monthlyDataLimit) * 100;
  bool get isDataSaverMode => _monthlyDataUsage >= (_monthlyDataLimit * 0.8);
  NetworkQuality get networkQuality => _networkQuality;

  /// Initialize the refresh controller
  Future<void> initialize() async {
    try {
      _logger.i('Initializing SmartRefreshController...');
      
      // Check current connectivity
      _currentConnection = await _connectivity.checkConnectivity();
      
      // Listen to connectivity changes
      _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);
      
      // Initialize monthly data tracking
      _initializeDataTracking();
      
      // Detect network quality
      await _detectNetworkQuality();
      
      _logger.i('SmartRefreshController initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize SmartRefreshController: $e');
    }
  }

  /// Perform smart refresh with exponential backoff
  Future<T> smartRefresh<T>({
    required Future<T> Function() refreshFunction,
    required Future<T> Function() fallbackFunction,
    String? operationName,
    bool forceRefresh = false,
  }) async {
    if (!forceRefresh && !canRefresh) {
      _logger.w('Refresh blocked: ${_getRefreshBlockReason()}');
      return await fallbackFunction();
    }

    _setRefreshing(true);
    
    try {
      // Check if we should use cached data based on network quality
      if (_shouldUseCachedData() && !forceRefresh) {
        _logger.i('Using cached data due to poor network or data saving mode');
        return await fallbackFunction();
      }

      // Estimate data usage for this operation
      final estimatedDataUsage = _estimateDataUsage(operationName ?? 'refresh');
      
      if (!_canUseData(estimatedDataUsage)) {
        _logger.w('Data usage limit reached, using cached data');
        return await fallbackFunction();
      }

      // Perform the refresh with timeout based on network quality
      final timeout = _getTimeoutForNetworkQuality();
      final result = await refreshFunction().timeout(timeout);
      
      // Track successful refresh
      _onRefreshSuccess(estimatedDataUsage);
      
      return result;
      
    } catch (e) {
      _logger.e('Refresh failed for ${operationName ?? 'operation'}: $e');
      
      // Handle failure with exponential backoff
      await _handleRefreshFailure(e);
      
      // Return cached data as fallback
      return await fallbackFunction();
      
    } finally {
      _setRefreshing(false);
    }
  }

  /// Handle refresh failure with exponential backoff
  Future<void> _handleRefreshFailure(dynamic error) async {
    _failureCount++;
    
    if (_failureCount >= _maxRetries) {
      _logger.e('Max retries reached, resetting failure count');
      _failureCount = 0;
      return;
    }

    // Calculate exponential backoff delay
    final delaySeconds = min(
      _baseDelay.inSeconds * pow(2, _failureCount - 1),
      _maxDelay.inSeconds,
    );
    
    final delay = Duration(seconds: delaySeconds.toInt());
    
    _logger.w('Refresh failed (attempt $_failureCount/$_maxRetries), backing off for ${delay.inSeconds}s');
    
    // Set backoff timer
    _backoffTimer?.cancel();
    _backoffTimer = Timer(delay, () {
      _logger.i('Backoff period ended, refresh available again');
      notifyListeners();
    });
  }

  /// Handle successful refresh
  void _onRefreshSuccess(double dataUsed) {
    _failureCount = 0;
    _lastRefreshTime = DateTime.now();
    _trackDataUsage(dataUsed);
    _backoffTimer?.cancel();
    
    _logger.i('Refresh successful, data used: ${dataUsed.toStringAsFixed(2)}MB');
  }

  /// Check if we should use cached data
  bool _shouldUseCachedData() {
    // Use cached data if in data saver mode
    if (isDataSaverMode) return true;
    
    // Use cached data on poor network quality
    if (_networkQuality == NetworkQuality.poor) return true;
    
    // Use cached data if no connection
    if (_currentConnection == ConnectivityResult.none) return true;
    
    // Use cached data if recently refreshed (within last 5 minutes on 2G)
    if (_lastRefreshTime != null && _networkQuality == NetworkQuality.poor) {
      final timeSinceRefresh = DateTime.now().difference(_lastRefreshTime!);
      if (timeSinceRefresh.inMinutes < 5) return true;
    }
    
    return false;
  }

  /// Get timeout duration based on network quality
  Duration _getTimeoutForNetworkQuality() {
    switch (_networkQuality) {
      case NetworkQuality.excellent:
        return const Duration(seconds: 10);
      case NetworkQuality.good:
        return const Duration(seconds: 15);
      case NetworkQuality.fair:
        return const Duration(seconds: 20);
      case NetworkQuality.poor:
        return const Duration(seconds: 30);
      case NetworkQuality.unknown:
        return const Duration(seconds: 15);
    }
  }

  /// Estimate data usage for an operation
  double _estimateDataUsage(String operation) {
    // Estimated data usage in MB for different operations
    switch (operation.toLowerCase()) {
      case 'balance':
        return 0.01; // 10KB
      case 'transactions':
        return 0.05; // 50KB
      case 'full_refresh':
        return 0.1; // 100KB
      case 'sync':
        return 0.02; // 20KB
      default:
        return 0.03; // 30KB default
    }
  }

  /// Check if we can use data for this operation
  bool _canUseData(double estimatedUsage) {
    return (_monthlyDataUsage + estimatedUsage) <= _monthlyDataLimit;
  }

  /// Track data usage
  void _trackDataUsage(double dataUsed) {
    // Reset monthly usage if new month
    if (_isNewMonth()) {
      _resetMonthlyUsage();
    }
    
    _monthlyDataUsage += dataUsed;
    _logger.i('Data usage: ${_monthlyDataUsage.toStringAsFixed(2)}MB / ${_monthlyDataLimit}MB');
    
    notifyListeners();
  }

  /// Initialize data tracking
  void _initializeDataTracking() {
    // In a real app, this would load from persistent storage
    _monthStartDate = DateTime(DateTime.now().year, DateTime.now().month, 1);
    _monthlyDataUsage = 0.0; // Load from storage
  }

  /// Check if it's a new month
  bool _isNewMonth() {
    final now = DateTime.now();
    return now.month != _monthStartDate.month || now.year != _monthStartDate.year;
  }

  /// Reset monthly usage
  void _resetMonthlyUsage() {
    _monthlyDataUsage = 0.0;
    _monthStartDate = DateTime(DateTime.now().year, DateTime.now().month, 1);
    _logger.i('Monthly data usage reset');
  }

  /// Check if we have data budget
  bool _hasDataBudget() {
    return _monthlyDataUsage < _monthlyDataLimit;
  }

  /// Get reason why refresh is blocked
  String _getRefreshBlockReason() {
    if (_isRefreshing) return 'Already refreshing';
    if (_isLoading) return 'Currently loading';
    if (!_hasDataBudget()) return 'Monthly data limit reached';
    if (_backoffTimer?.isActive == true) return 'In backoff period';
    return 'Unknown reason';
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    _currentConnection = result;
    _logger.i('Connectivity changed: $result');
    
    // Detect network quality when connectivity changes
    _detectNetworkQuality();
    
    notifyListeners();
  }

  /// Detect network quality
  Future<void> _detectNetworkQuality() async {
    try {
      switch (_currentConnection) {
        case ConnectivityResult.wifi:
          _networkQuality = NetworkQuality.excellent;
          break;
        case ConnectivityResult.mobile:
          // In a real app, you would test actual speed
          _networkQuality = NetworkQuality.good; // Assume good for mobile
          break;
        case ConnectivityResult.ethernet:
          _networkQuality = NetworkQuality.excellent;
          break;
        case ConnectivityResult.none:
          _networkQuality = NetworkQuality.unknown;
          break;
        default:
          _networkQuality = NetworkQuality.unknown;
      }
      
      _logger.i('Network quality detected: $_networkQuality');
    } catch (e) {
      _logger.e('Failed to detect network quality: $e');
      _networkQuality = NetworkQuality.unknown;
    }
  }

  /// Set refreshing state
  void _setRefreshing(bool refreshing) {
    if (_isRefreshing != refreshing) {
      _isRefreshing = refreshing;
      notifyListeners();
    }
  }

  /// Set loading state
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Force refresh (ignores data limits)
  Future<T> forceRefresh<T>({
    required Future<T> Function() refreshFunction,
    required Future<T> Function() fallbackFunction,
    String? operationName,
  }) async {
    return smartRefresh(
      refreshFunction: refreshFunction,
      fallbackFunction: fallbackFunction,
      operationName: operationName,
      forceRefresh: true,
    );
  }

  /// Get data usage summary
  Map<String, dynamic> getDataUsageSummary() {
    return {
      'monthlyUsage': _monthlyDataUsage,
      'monthlyLimit': _monthlyDataLimit,
      'usagePercentage': dataUsagePercentage,
      'remainingData': _monthlyDataLimit - _monthlyDataUsage,
      'isDataSaverMode': isDataSaverMode,
      'monthStartDate': _monthStartDate.toIso8601String(),
    };
  }

  @override
  void dispose() {
    _backoffTimer?.cancel();
    super.dispose();
  }
}

/// Network quality enumeration
enum NetworkQuality {
  excellent, // WiFi, fast mobile
  good,      // 4G
  fair,      // 3G
  poor,      // 2G
  unknown,   // Unable to determine
}
