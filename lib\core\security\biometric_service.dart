import 'package:local_auth/local_auth.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../config/app_config.dart';
import '../constants/app_constants.dart';

/// Biometric authentication service for secure access
/// Supports fingerprint, face recognition, and iris scanning
class BiometricService {
  static final BiometricService _instance = BiometricService._internal();
  factory BiometricService() => _instance;
  BiometricService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  final Logger _logger = Logger();
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      
      return isAvailable && isDeviceSupported;
    } catch (e) {
      _logger.e('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      if (!await isBiometricAvailable()) {
        return [];
      }
      
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      _logger.e('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Check if specific biometric type is available
  Future<bool> isBiometricTypeAvailable(BiometricType type) async {
    final availableBiometrics = await getAvailableBiometrics();
    return availableBiometrics.contains(type);
  }

  /// Authenticate using biometrics
  Future<BiometricAuthResult> authenticate({
    String? reason,
    bool useErrorDialogs = true,
    bool stickyAuth = true,
  }) async {
    try {
      if (!await isBiometricAvailable()) {
        return BiometricAuthResult(
          success: false,
          error: AppConstants.errorBiometricNotAvailable,
          errorMessage: 'Biometric authentication not available',
        );
      }

      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: reason ?? AppConstants.biometricReason,
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: true,
        ),
      );

      if (isAuthenticated) {
        await _recordSuccessfulAuth();
        return BiometricAuthResult(success: true);
      } else {
        await _recordFailedAuth();
        return BiometricAuthResult(
          success: false,
          error: 'AUTHENTICATION_FAILED',
          errorMessage: 'Biometric authentication failed',
        );
      }
    } on PlatformException catch (e) {
      _logger.e('Biometric authentication error: ${e.code} - ${e.message}');
      
      String errorCode;
      String errorMessage;
      
      switch (e.code) {
        case 'NotAvailable':
          errorCode = AppConstants.errorBiometricNotAvailable;
          errorMessage = 'Biometric authentication not available';
          break;
        case 'NotEnrolled':
          errorCode = 'BIOMETRIC_NOT_ENROLLED';
          errorMessage = 'No biometrics enrolled on device';
          break;
        case 'LockedOut':
          errorCode = 'BIOMETRIC_LOCKED_OUT';
          errorMessage = 'Biometric authentication locked out';
          break;
        case 'PermanentlyLockedOut':
          errorCode = 'BIOMETRIC_PERMANENTLY_LOCKED_OUT';
          errorMessage = 'Biometric authentication permanently locked out';
          break;
        default:
          errorCode = 'BIOMETRIC_ERROR';
          errorMessage = e.message ?? 'Unknown biometric error';
      }
      
      return BiometricAuthResult(
        success: false,
        error: errorCode,
        errorMessage: errorMessage,
      );
    } catch (e) {
      _logger.e('Unexpected biometric error: $e');
      return BiometricAuthResult(
        success: false,
        error: 'BIOMETRIC_ERROR',
        errorMessage: 'Unexpected biometric error',
      );
    }
  }

  /// Enable biometric authentication for user
  Future<bool> enableBiometric(String userId) async {
    try {
      // First authenticate to ensure user can use biometrics
      final authResult = await authenticate(
        reason: 'Enable biometric authentication for your account',
      );
      
      if (!authResult.success) {
        return false;
      }
      
      // Store biometric preference
      await _secureStorage.write(
        key: '${AppConstants.keyBiometricEnabled}_$userId',
        value: 'true',
      );
      
      _logger.i('Biometric authentication enabled for user: $userId');
      return true;
    } catch (e) {
      _logger.e('Failed to enable biometric authentication: $e');
      return false;
    }
  }

  /// Disable biometric authentication for user
  Future<void> disableBiometric(String userId) async {
    try {
      await _secureStorage.delete(
        key: '${AppConstants.keyBiometricEnabled}_$userId',
      );
      
      // Clear biometric auth history
      await _secureStorage.delete(
        key: 'biometric_auth_count_$userId',
      );
      await _secureStorage.delete(
        key: 'biometric_last_auth_$userId',
      );
      
      _logger.i('Biometric authentication disabled for user: $userId');
    } catch (e) {
      _logger.e('Failed to disable biometric authentication: $e');
    }
  }

  /// Check if biometric is enabled for user
  Future<bool> isBiometricEnabled(String userId) async {
    try {
      final enabled = await _secureStorage.read(
        key: '${AppConstants.keyBiometricEnabled}_$userId',
      );
      return enabled == 'true';
    } catch (e) {
      _logger.e('Error checking biometric status: $e');
      return false;
    }
  }

  /// Authenticate user with biometrics if enabled
  Future<BiometricAuthResult> authenticateUser(String userId) async {
    if (!await isBiometricEnabled(userId)) {
      return BiometricAuthResult(
        success: false,
        error: 'BIOMETRIC_NOT_ENABLED',
        errorMessage: 'Biometric authentication not enabled for this user',
      );
    }
    
    return await authenticate(
      reason: 'Authenticate to access your Zambia Pay account',
    );
  }

  /// Record successful authentication
  Future<void> _recordSuccessfulAuth() async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      await _secureStorage.write(
        key: 'biometric_last_auth',
        value: now.toString(),
      );
      
      // Increment success count
      final countStr = await _secureStorage.read(key: 'biometric_auth_count') ?? '0';
      final count = int.parse(countStr) + 1;
      await _secureStorage.write(
        key: 'biometric_auth_count',
        value: count.toString(),
      );
    } catch (e) {
      _logger.e('Failed to record successful auth: $e');
    }
  }

  /// Record failed authentication
  Future<void> _recordFailedAuth() async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      await _secureStorage.write(
        key: 'biometric_last_failed_auth',
        value: now.toString(),
      );
      
      // Increment failure count
      final countStr = await _secureStorage.read(key: 'biometric_failed_count') ?? '0';
      final count = int.parse(countStr) + 1;
      await _secureStorage.write(
        key: 'biometric_failed_count',
        value: count.toString(),
      );
    } catch (e) {
      _logger.e('Failed to record failed auth: $e');
    }
  }

  /// Get biometric authentication statistics
  Future<BiometricStats> getAuthStats() async {
    try {
      final successCountStr = await _secureStorage.read(key: 'biometric_auth_count') ?? '0';
      final failureCountStr = await _secureStorage.read(key: 'biometric_failed_count') ?? '0';
      final lastAuthStr = await _secureStorage.read(key: 'biometric_last_auth');
      final lastFailedAuthStr = await _secureStorage.read(key: 'biometric_last_failed_auth');
      
      return BiometricStats(
        successCount: int.parse(successCountStr),
        failureCount: int.parse(failureCountStr),
        lastSuccessfulAuth: lastAuthStr != null 
            ? DateTime.fromMillisecondsSinceEpoch(int.parse(lastAuthStr))
            : null,
        lastFailedAuth: lastFailedAuthStr != null 
            ? DateTime.fromMillisecondsSinceEpoch(int.parse(lastFailedAuthStr))
            : null,
      );
    } catch (e) {
      _logger.e('Failed to get auth stats: $e');
      return BiometricStats(successCount: 0, failureCount: 0);
    }
  }

  /// Check if biometric authentication is required
  Future<bool> isAuthRequired() async {
    try {
      final lastAuthStr = await _secureStorage.read(key: 'biometric_last_auth');
      if (lastAuthStr == null) return true;
      
      final lastAuth = DateTime.fromMillisecondsSinceEpoch(int.parse(lastAuthStr));
      final now = DateTime.now();
      final timeDifference = now.difference(lastAuth);
      
      // Require re-authentication after 30 seconds for security
      return timeDifference.inSeconds > AppConstants.biometricTimeoutSeconds;
    } catch (e) {
      _logger.e('Error checking auth requirement: $e');
      return true;
    }
  }

  /// Get supported biometric types as strings
  Future<List<String>> getSupportedBiometricTypes() async {
    final availableBiometrics = await getAvailableBiometrics();
    return availableBiometrics.map((type) {
      switch (type) {
        case BiometricType.fingerprint:
          return 'fingerprint';
        case BiometricType.face:
          return 'face';
        case BiometricType.iris:
          return 'iris';
        case BiometricType.weak:
          return 'weak';
        case BiometricType.strong:
          return 'strong';
        default:
          return 'unknown';
      }
    }).toList();
  }

  /// Clear all biometric data
  Future<void> clearBiometricData() async {
    try {
      final keys = [
        'biometric_auth_count',
        'biometric_failed_count',
        'biometric_last_auth',
        'biometric_last_failed_auth',
      ];
      
      for (final key in keys) {
        await _secureStorage.delete(key: key);
      }
      
      _logger.i('Biometric data cleared');
    } catch (e) {
      _logger.e('Failed to clear biometric data: $e');
    }
  }
}

/// Biometric authentication result
class BiometricAuthResult {
  final bool success;
  final String? error;
  final String? errorMessage;

  BiometricAuthResult({
    required this.success,
    this.error,
    this.errorMessage,
  });

  @override
  String toString() {
    return 'BiometricAuthResult(success: $success, error: $error, errorMessage: $errorMessage)';
  }
}

/// Biometric authentication statistics
class BiometricStats {
  final int successCount;
  final int failureCount;
  final DateTime? lastSuccessfulAuth;
  final DateTime? lastFailedAuth;

  BiometricStats({
    required this.successCount,
    required this.failureCount,
    this.lastSuccessfulAuth,
    this.lastFailedAuth,
  });

  double get successRate {
    final total = successCount + failureCount;
    return total > 0 ? successCount / total : 0.0;
  }

  @override
  String toString() {
    return 'BiometricStats(success: $successCount, failures: $failureCount, successRate: ${(successRate * 100).toStringAsFixed(1)}%)';
  }
}
