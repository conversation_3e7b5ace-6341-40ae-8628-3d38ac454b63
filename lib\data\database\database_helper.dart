import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite_sqlcipher/sqflite.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:typed_data';

import '../../core/config/app_config.dart';
import '../../core/constants/app_constants.dart';

/// Encrypted SQLite database helper for offline-first architecture
/// Implements AES-256 encryption for PCI-DSS Level 1 compliance
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConfig.databaseName);
    
    // Generate or retrieve encryption key
    final encryptionKey = await _getOrCreateEncryptionKey();
    
    return await openDatabase(
      path,
      version: AppConfig.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      password: encryptionKey,
    );
  }

  Future<String> _getOrCreateEncryptionKey() async {
    String? key = await _secureStorage.read(key: AppConstants.keyEncryptionKey);
    
    if (key == null) {
      // Generate a new 256-bit encryption key
      final bytes = List<int>.generate(32, (i) => 
        DateTime.now().millisecondsSinceEpoch.hashCode + i);
      key = base64Encode(bytes);
      await _secureStorage.write(key: AppConstants.keyEncryptionKey, value: key);
    }
    
    return key;
  }

  Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database schema upgrades
    if (oldVersion < newVersion) {
      await _createTables(db);
    }
  }

  Future<void> _createTables(Database db) async {
    // Users table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.usersTable} (
        id TEXT PRIMARY KEY,
        phone_number TEXT UNIQUE NOT NULL,
        pin_hash TEXT NOT NULL,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        email TEXT,
        profile_image TEXT,
        is_verified INTEGER DEFAULT 0,
        kyc_status TEXT DEFAULT 'PENDING',
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        last_login INTEGER,
        biometric_enabled INTEGER DEFAULT 0,
        preferred_language TEXT DEFAULT 'en',
        balance_zmw REAL DEFAULT 0.0
      )
    ''');

    // Transactions table with encryption for sensitive data
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.transactionsTable} (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        transaction_type TEXT NOT NULL,
        amount REAL NOT NULL,
        currency TEXT DEFAULT 'ZMW',
        fee REAL DEFAULT 0.0,
        total_amount REAL NOT NULL,
        sender_phone TEXT,
        receiver_phone TEXT,
        provider TEXT NOT NULL,
        status TEXT NOT NULL,
        reference_number TEXT,
        description TEXT,
        metadata TEXT, -- JSON string for additional data
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        completed_at INTEGER,
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id)
      )
    ''');

    // Offline queue for transactions when network is unavailable
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.offlineQueueTable} (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        transaction_data TEXT NOT NULL, -- Encrypted JSON
        transaction_type TEXT NOT NULL,
        priority INTEGER DEFAULT 1,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3,
        created_at INTEGER NOT NULL,
        last_retry_at INTEGER,
        next_retry_at INTEGER,
        status TEXT DEFAULT 'QUEUED',
        error_message TEXT,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id)
      )
    ''');

    // Utility bills table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.utilityBillsTable} (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        provider TEXT NOT NULL,
        account_number TEXT NOT NULL,
        customer_name TEXT,
        utility_type TEXT NOT NULL,
        amount_due REAL,
        due_date INTEGER,
        bill_period TEXT,
        status TEXT DEFAULT 'UNPAID',
        last_updated INTEGER NOT NULL,
        auto_pay_enabled INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id)
      )
    ''');

    // Contacts table for frequent recipients
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.contactsTable} (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        phone_number TEXT NOT NULL,
        provider TEXT,
        is_favorite INTEGER DEFAULT 0,
        transaction_count INTEGER DEFAULT 0,
        last_transaction_at INTEGER,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id)
      )
    ''');

    // Settings table for user preferences
    await db.execute('''
      CREATE TABLE IF NOT EXISTS ${AppConstants.settingsTable} (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        setting_key TEXT NOT NULL,
        setting_value TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id),
        UNIQUE(user_id, setting_key)
      )
    ''');

    // Create indexes for better performance
    await _createIndexes(db);
  }

  Future<void> _createIndexes(Database db) async {
    await db.execute('CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON ${AppConstants.transactionsTable} (user_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_transactions_status ON ${AppConstants.transactionsTable} (status)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON ${AppConstants.transactionsTable} (created_at)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_offline_queue_status ON ${AppConstants.offlineQueueTable} (status)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_offline_queue_next_retry ON ${AppConstants.offlineQueueTable} (next_retry_at)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_utility_bills_user_id ON ${AppConstants.utilityBillsTable} (user_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_contacts_user_id ON ${AppConstants.contactsTable} (user_id)');
  }

  // Generic CRUD operations with encryption support
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    data['created_at'] = DateTime.now().millisecondsSinceEpoch;
    data['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert(table, data);
  }

  Future<List<Map<String, dynamic>>> query(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    data['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.update(table, data, where: where, whereArgs: whereArgs);
  }

  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  // Batch operations for better performance
  Future<void> batch(List<String> statements) async {
    final db = await database;
    final batch = db.batch();
    for (final statement in statements) {
      batch.rawInsert(statement);
    }
    await batch.commit();
  }

  // Database maintenance
  Future<void> vacuum() async {
    final db = await database;
    await db.execute('VACUUM');
  }

  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }

  // Security: Clear sensitive data on logout
  Future<void> clearUserData(String userId) async {
    final db = await database;
    final batch = db.batch();
    
    batch.delete(AppConstants.transactionsTable, where: 'user_id = ?', whereArgs: [userId]);
    batch.delete(AppConstants.offlineQueueTable, where: 'user_id = ?', whereArgs: [userId]);
    batch.delete(AppConstants.utilityBillsTable, where: 'user_id = ?', whereArgs: [userId]);
    batch.delete(AppConstants.contactsTable, where: 'user_id = ?', whereArgs: [userId]);
    batch.delete(AppConstants.settingsTable, where: 'user_id = ?', whereArgs: [userId]);
    
    await batch.commit();
  }
}
