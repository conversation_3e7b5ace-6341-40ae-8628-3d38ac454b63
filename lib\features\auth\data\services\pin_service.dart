/// PIN Service for Secure PIN Management
/// Implements BoZ-compliant PIN storage and verification
/// Uses FIPS-140-2 encryption standards

import 'package:logger/logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'package:sqflite/sqflite.dart';
import 'dart:convert';
import 'dart:typed_data';
import '../../../../core/security/encryption_service.dart';
import '../../../../core/config/app_config.dart';
import '../../../../data/database/database_helper.dart';

class PINService {
  static final PINService _instance = PINService._internal();
  factory PINService() => _instance;
  PINService._internal();

  static final Logger _logger = Logger();
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  final EncryptionService _encryption = EncryptionService();
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// Setup secure PIN with BoZ-compliant encryption
  Future<PINSetupResult> setupPIN({
    required String userId,
    required String pin,
    String? encryptionStandard,
  }) async {
    try {
      _logger.i('🔐 Setting up secure PIN for user: $userId');

      // Validate encryption standard
      final standard = encryptionStandard ?? AppConfig.complianceSettings['encryptionStandard'];
      if (standard != 'FIPS-140-2') {
        _logger.w('⚠️ Non-FIPS encryption standard requested: $standard');
      }

      // Generate salt for PIN hashing
      final salt = _generateSalt();
      
      // Hash PIN with salt using PBKDF2
      final hashedPIN = await _hashPIN(pin, salt);
      
      // Encrypt hashed PIN
      await _encryption.initialize();
      final encryptedPIN = await _encryption.encryptData(hashedPIN);
      
      // Store PIN data securely
      await _storePINData(userId, encryptedPIN, salt);
      
      // Log PIN setup for audit
      await _logPINSetup(userId);

      _logger.i('✅ PIN setup completed successfully for user: $userId');

      return PINSetupResult(
        success: true,
        message: 'PIN setup completed successfully.',
      );

    } catch (e) {
      _logger.e('PIN setup failed for user $userId: $e');
      return PINSetupResult(
        success: false,
        error: 'PIN_SETUP_FAILED',
        message: 'Failed to setup PIN. Please try again.',
      );
    }
  }

  /// Verify PIN for authentication
  Future<PINVerificationResult> verifyPIN({
    required String userId,
    required String pin,
  }) async {
    try {
      _logger.i('🔐 Verifying PIN for user: $userId');

      // Retrieve stored PIN data
      final pinData = await _retrievePINData(userId);
      if (pinData == null) {
        return PINVerificationResult(
          success: false,
          error: 'PIN_NOT_FOUND',
          message: 'PIN not found for user.',
        );
      }

      // Decrypt stored PIN hash
      await _encryption.initialize();
      final storedHashedPIN = await _encryption.decryptData(pinData.encryptedPIN);
      
      // Hash provided PIN with stored salt
      final providedHashedPIN = await _hashPIN(pin, pinData.salt);

      // Compare hashes
      if (storedHashedPIN == providedHashedPIN) {
        _logger.i('✅ PIN verification successful for user: $userId');
        
        // Update last verification time
        await _updateLastVerification(userId);
        
        // Log successful verification
        await _logPINVerification(userId, true);

        return PINVerificationResult(
          success: true,
          message: 'PIN verified successfully.',
        );
      } else {
        _logger.w('❌ PIN verification failed for user: $userId');
        
        // Log failed verification
        await _logPINVerification(userId, false);
        
        // Check for brute force attempts
        await _checkBruteForceAttempts(userId);

        return PINVerificationResult(
          success: false,
          error: 'INVALID_PIN',
          message: 'Invalid PIN. Please try again.',
        );
      }

    } catch (e) {
      _logger.e('PIN verification error for user $userId: $e');
      return PINVerificationResult(
        success: false,
        error: 'PIN_VERIFICATION_ERROR',
        message: 'PIN verification failed. Please try again.',
      );
    }
  }

  /// Change existing PIN
  Future<PINChangeResult> changePIN({
    required String userId,
    required String currentPIN,
    required String newPIN,
  }) async {
    try {
      _logger.i('🔄 Changing PIN for user: $userId');

      // First verify current PIN
      final currentVerification = await verifyPIN(
        userId: userId,
        pin: currentPIN,
      );

      if (!currentVerification.success) {
        return PINChangeResult(
          success: false,
          error: 'CURRENT_PIN_INVALID',
          message: 'Current PIN is invalid.',
        );
      }

      // Setup new PIN
      final newPINSetup = await setupPIN(
        userId: userId,
        pin: newPIN,
      );

      if (newPINSetup.success) {
        _logger.i('✅ PIN changed successfully for user: $userId');
        
        // Log PIN change for audit
        await _logPINChange(userId);

        return PINChangeResult(
          success: true,
          message: 'PIN changed successfully.',
        );
      } else {
        return PINChangeResult(
          success: false,
          error: 'NEW_PIN_SETUP_FAILED',
          message: 'Failed to setup new PIN.',
        );
      }

    } catch (e) {
      _logger.e('PIN change failed for user $userId: $e');
      return PINChangeResult(
        success: false,
        error: 'PIN_CHANGE_ERROR',
        message: 'PIN change failed. Please try again.',
      );
    }
  }

  /// Check if user has PIN setup
  Future<bool> hasPIN(String userId) async {
    try {
      final pinData = await _retrievePINData(userId);
      return pinData != null;
    } catch (e) {
      _logger.e('Error checking PIN existence for user $userId: $e');
      return false;
    }
  }

  /// Generate cryptographically secure salt
  String _generateSalt() {
    final bytes = List<int>.generate(32, (i) => 
      DateTime.now().millisecondsSinceEpoch.hashCode + i);
    return base64Encode(bytes);
  }

  /// Hash PIN using PBKDF2 with salt
  Future<String> _hashPIN(String pin, String salt) async {
    final saltBytes = base64Decode(salt);
    final pinBytes = utf8.encode(pin);

    // Use PBKDF2 with SHA-256 and 100,000 iterations (BoZ compliant)
    var hmac = Hmac(sha256, pinBytes);
    var digest = hmac.convert(saltBytes);

    // Simple PBKDF2 implementation for PIN hashing
    for (int i = 1; i < AppConfig.keyDerivationIterations; i++) {
      hmac = Hmac(sha256, digest.bytes);
      digest = hmac.convert(saltBytes);
    }

    return base64Encode(digest.bytes);
  }

  /// Store PIN data securely
  Future<void> _storePINData(String userId, String encryptedPIN, String salt) async {
    // Store in secure storage
    await _secureStorage.write(
      key: 'pin_$userId',
      value: encryptedPIN,
    );
    
    await _secureStorage.write(
      key: 'salt_$userId',
      value: salt,
    );

    // Store metadata in database
    final db = await _dbHelper.database;
    await db.insert(
      'user_pin_metadata',
      {
        'user_id': userId,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'last_updated': DateTime.now().millisecondsSinceEpoch,
        'encryption_standard': AppConfig.complianceSettings['encryptionStandard'],
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Retrieve PIN data
  Future<PINData?> _retrievePINData(String userId) async {
    try {
      final encryptedPIN = await _secureStorage.read(key: 'pin_$userId');
      final salt = await _secureStorage.read(key: 'salt_$userId');

      if (encryptedPIN != null && salt != null) {
        return PINData(
          encryptedPIN: encryptedPIN,
          salt: salt,
        );
      }
      
      return null;
    } catch (e) {
      _logger.e('Failed to retrieve PIN data for user $userId: $e');
      return null;
    }
  }

  /// Update last verification time
  Future<void> _updateLastVerification(String userId) async {
    try {
      final db = await _dbHelper.database;
      await db.update(
        'user_pin_metadata',
        {'last_verified': DateTime.now().millisecondsSinceEpoch},
        where: 'user_id = ?',
        whereArgs: [userId],
      );
    } catch (e) {
      _logger.e('Failed to update last verification for user $userId: $e');
    }
  }

  /// Log PIN setup for audit
  Future<void> _logPINSetup(String userId) async {
    try {
      final db = await _dbHelper.database;
      await db.insert('audit_log', {
        'user_id': userId,
        'action': 'PIN_SETUP',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'details': 'PIN setup completed successfully',
        'compliance_requirement': 'BoZ_PIN_SECURITY',
      });
    } catch (e) {
      _logger.e('Failed to log PIN setup for user $userId: $e');
    }
  }

  /// Log PIN verification attempt
  Future<void> _logPINVerification(String userId, bool success) async {
    try {
      final db = await _dbHelper.database;
      await db.insert('audit_log', {
        'user_id': userId,
        'action': success ? 'PIN_VERIFICATION_SUCCESS' : 'PIN_VERIFICATION_FAILED',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'details': success ? 'PIN verified successfully' : 'PIN verification failed',
        'compliance_requirement': 'BoZ_AUTHENTICATION_LOG',
      });
    } catch (e) {
      _logger.e('Failed to log PIN verification for user $userId: $e');
    }
  }

  /// Log PIN change for audit
  Future<void> _logPINChange(String userId) async {
    try {
      final db = await _dbHelper.database;
      await db.insert('audit_log', {
        'user_id': userId,
        'action': 'PIN_CHANGED',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'details': 'PIN changed successfully',
        'compliance_requirement': 'BoZ_PIN_SECURITY',
      });
    } catch (e) {
      _logger.e('Failed to log PIN change for user $userId: $e');
    }
  }

  /// Check for brute force attempts
  Future<void> _checkBruteForceAttempts(String userId) async {
    try {
      final db = await _dbHelper.database;
      final recentFailures = await db.query(
        'audit_log',
        where: 'user_id = ? AND action = ? AND timestamp > ?',
        whereArgs: [
          userId,
          'PIN_VERIFICATION_FAILED',
          DateTime.now().subtract(Duration(minutes: 15)).millisecondsSinceEpoch,
        ],
      );

      if (recentFailures.length >= 5) {
        _logger.w('🚨 Potential brute force attack detected for user: $userId');
        
        // Log security alert
        await db.insert('security_alerts', {
          'user_id': userId,
          'alert_type': 'BRUTE_FORCE_PIN',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'details': 'Multiple failed PIN attempts detected',
          'severity': 'HIGH',
        });
      }
    } catch (e) {
      _logger.e('Failed to check brute force attempts for user $userId: $e');
    }
  }
}

/// PIN data storage class
class PINData {
  final String encryptedPIN;
  final String salt;

  PINData({
    required this.encryptedPIN,
    required this.salt,
  });
}

/// PIN setup result
class PINSetupResult {
  final bool success;
  final String? error;
  final String message;

  PINSetupResult({
    required this.success,
    this.error,
    required this.message,
  });
}

/// PIN verification result
class PINVerificationResult {
  final bool success;
  final String? error;
  final String message;

  PINVerificationResult({
    required this.success,
    this.error,
    required this.message,
  });
}

/// PIN change result
class PINChangeResult {
  final bool success;
  final String? error;
  final String message;

  PINChangeResult({
    required this.success,
    this.error,
    required this.message,
  });
}


