/// Bank of Zambia (BoZ) Compliance Implementation Example
/// 
/// This file demonstrates how to implement:
/// - BoZ Compliance: PCI-DSS Section 3.2.1
/// - Data retention: 5 years (Zambia Financial Act 2022)
/// 
/// Usage examples for developers integrating BoZ compliance

import 'dart:convert';
import 'package:logger/logger.dart';

import '../../core/config/compliance_config.dart';
import '../../core/security/compliance_service.dart';
import '../../core/security/encryption_service.dart';

class BoZComplianceExample {
  static final ComplianceService _complianceService = ComplianceService();
  static final EncryptionService _encryptionService = EncryptionService();
  static final Logger _logger = Logger();

  /// Example 1: Transaction Processing with BoZ Compliance
  /// Demonstrates PCI-DSS 3.2.1 and data retention requirements
  static Future<void> processTransactionWithCompliance({
    required String userId,
    required String transactionId,
    required double amount,
    required String senderPhone,
    required String receiverPhone,
    String? cvv, // Sensitive authentication data
    String? pin, // Sensitive authentication data
  }) async {
    try {
      // BoZ Compliance: PCI-DSS Section 3.2.1
      // Do not store sensitive authentication data after authorization
      
      // 1. Process transaction (CVV and PIN used for authorization only)
      final authResult = await _authorizeTransaction(cvv, pin);
      
      if (authResult) {
        // 2. Store transaction data (WITHOUT sensitive auth data)
        final transactionData = {
          'id': transactionId,
          'user_id': userId,
          'amount': amount,
          'sender_phone': senderPhone,
          'receiver_phone': receiverPhone,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'status': 'COMPLETED',
          // CVV and PIN are NOT stored (PCI-DSS 3.2.1 compliance)
        };

        // 3. Register for 5-year data retention (Zambia Financial Act 2022)
        await _complianceService.registerDataForRetention(
          dataType: 'FINANCIAL_RECORD',
          tableName: 'transactions',
          recordId: transactionId,
          userId: userId,
          dataClassification: 'FINANCIAL_RECORD',
        );

        // 4. Log audit event for BoZ compliance
        await _complianceService.logAuditEvent(
          userId: userId,
          eventType: 'TRANSACTION_COMPLETED',
          description: 'Transaction processed with BoZ compliance',
          pciDssRequirement: '3.2.1',
          bozRegulationRef: 'Zambia Financial Act 2022 Section 45',
          dataClassification: 'FINANCIAL_RECORD',
          additionalData: {
            'transaction_id': transactionId,
            'amount': amount,
            'compliance_verified': true,
          },
        );

        _logger.i('Transaction $transactionId processed with BoZ compliance');
      }
    } catch (e) {
      _logger.e('Transaction processing failed: $e');
      rethrow;
    }
  }

  /// Example 2: User Data Management with 5-Year Retention
  static Future<void> createUserWithDataRetention({
    required String userId,
    required String phoneNumber,
    required String email,
    required String pinHash,
    String? nationalId,
  }) async {
    try {
      // 1. Encrypt sensitive user data (PCI-DSS 3.4)
      final encryptedPhone = await _encryptionService.encryptData(phoneNumber);
      final encryptedEmail = await _encryptionService.encryptData(email);
      final encryptedNationalId = nationalId != null 
          ? await _encryptionService.encryptData(nationalId)
          : null;

      // 2. Store user data with encryption
      final userData = {
        'id': userId,
        'phone_number': encryptedPhone,
        'email': encryptedEmail,
        'pin_hash': pinHash, // Already hashed, not raw PIN
        'national_id': encryptedNationalId,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'kyc_status': 'PENDING',
      };

      // 3. Register for 5-year data retention
      await _complianceService.registerDataForRetention(
        dataType: 'CONFIDENTIAL',
        tableName: 'users',
        recordId: userId,
        userId: userId,
        dataClassification: 'CONFIDENTIAL',
      );

      // 4. Log user creation for audit
      await _complianceService.logAuditEvent(
        userId: userId,
        eventType: 'USER_CREATED',
        description: 'User account created with BoZ compliance',
        pciDssRequirement: '3.4',
        bozRegulationRef: 'Zambia Data Protection Act 2021',
        dataClassification: 'CONFIDENTIAL',
      );

      _logger.i('User $userId created with 5-year data retention');
    } catch (e) {
      _logger.e('User creation failed: $e');
      rethrow;
    }
  }

  /// Example 3: Quarterly BoZ Reporting
  static Future<String> generateQuarterlyBoZReport() async {
    try {
      // Generate comprehensive BoZ compliance report
      final report = await _complianceService.generateBoZQuarterlyReport(
        startDate: DateTime.now().subtract(const Duration(days: 90)),
        endDate: DateTime.now(),
      );

      // Export for BoZ submission
      final exportedReport = await _complianceService.exportComplianceDataForBoZ(
        startDate: DateTime.now().subtract(const Duration(days: 90)),
        endDate: DateTime.now(),
      );

      // Log report generation
      await _complianceService.logAuditEvent(
        eventType: 'BOZ_QUARTERLY_REPORT_GENERATED',
        description: 'Quarterly compliance report generated for BoZ submission',
        bozRegulationRef: ComplianceConfig.regulatoryFramework,
        dataClassification: 'CONFIDENTIAL',
        additionalData: {
          'report_period_days': 90,
          'total_transactions': report['transaction_metrics']?['total_transactions'] ?? 0,
          'compliance_violations': report['compliance_violations']?['total_violations'] ?? 0,
        },
      );

      return exportedReport;
    } catch (e) {
      _logger.e('BoZ report generation failed: $e');
      rethrow;
    }
  }

  /// Example 4: Data Retention Cleanup (5-Year Rule)
  static Future<void> performDataRetentionCleanup() async {
    try {
      // Perform secure deletion of data past 5-year retention
      await _complianceService.performSecureDataDeletion();

      // Verify BoZ compliance after cleanup
      final isCompliant = await _complianceService.verifyBoZCompliance();

      // Log cleanup completion
      await _complianceService.logAuditEvent(
        eventType: 'DATA_RETENTION_CLEANUP_COMPLETED',
        description: 'Completed 5-year data retention cleanup per Zambia Financial Act 2022',
        bozRegulationRef: 'Zambia Financial Act 2022 Section 45',
        dataClassification: 'FINANCIAL_RECORD',
        additionalData: {
          'cleanup_date': DateTime.now().toIso8601String(),
          'boz_compliant': isCompliant,
          'retention_period_years': ComplianceConfig.dataRetentionYears,
        },
      );

      _logger.i('Data retention cleanup completed. BoZ compliant: $isCompliant');
    } catch (e) {
      _logger.e('Data retention cleanup failed: $e');
      rethrow;
    }
  }

  /// Example 5: PCI-DSS 3.2.1 Validation
  static Future<bool> validatePciDssCompliance() async {
    try {
      // Validate PCI-DSS 3.2.1 requirements
      final complianceStatus = await _complianceService.validatePCIDSSCompliance();
      final overallScore = complianceStatus['overall_compliance_score'] as double;
      
      // Check specific PCI-DSS 3.2.1 requirement
      final requirement321 = ComplianceConfig.getPciDssRequirement('3.2.1');
      
      // Log compliance validation
      await _complianceService.logAuditEvent(
        eventType: 'PCI_DSS_COMPLIANCE_VALIDATION',
        description: 'PCI-DSS 3.2.1 compliance validation performed',
        pciDssRequirement: '3.2.1',
        bozRegulationRef: ComplianceConfig.regulatoryFramework,
        additionalData: {
          'compliance_score': overallScore,
          'requirement_title': requirement321?['title'],
          'validation_date': DateTime.now().toIso8601String(),
        },
      );

      return overallScore >= 95.0; // BoZ requires 95%+ compliance
    } catch (e) {
      _logger.e('PCI-DSS validation failed: $e');
      return false;
    }
  }

  /// Example 6: AML/CFT Transaction Monitoring
  static Future<void> monitorTransactionForAML({
    required String transactionId,
    required String userId,
    required double amount,
    required String transactionType,
  }) async {
    try {
      // Check if transaction triggers AML reporting
      final triggersReporting = ComplianceConfig.triggersAmlReporting(amount, transactionType);
      
      // Monitor transaction for compliance
      await _complianceService.monitorTransaction(
        transactionId: transactionId,
        userId: userId,
        amount: amount,
        currency: 'ZMW',
      );

      // Log AML monitoring
      await _complianceService.logAuditEvent(
        userId: userId,
        eventType: 'AML_TRANSACTION_MONITORED',
        description: 'Transaction monitored for AML/CFT compliance',
        bozRegulationRef: 'Financial Intelligence Centre Act',
        dataClassification: 'FINANCIAL_RECORD',
        additionalData: {
          'transaction_id': transactionId,
          'amount': amount,
          'triggers_reporting': triggersReporting,
          'transaction_type': transactionType,
        },
      );

      if (triggersReporting) {
        _logger.w('Transaction $transactionId triggers AML reporting: K${amount.toStringAsFixed(2)}');
      }
    } catch (e) {
      _logger.e('AML monitoring failed: $e');
      rethrow;
    }
  }

  /// Example 7: Compliance Violation Handling
  static Future<void> handleComplianceViolation({
    required String violationType,
    required String severity,
    required String description,
    String? userId,
  }) async {
    try {
      // Log compliance violation
      await _complianceService._logComplianceViolation(
        userId: userId,
        violationType: violationType,
        severity: severity,
        description: description,
      );

      // Check if BoZ notification is required
      final requiresNotification = ComplianceConfig.isBozNotificationRequired(severity);
      
      if (requiresNotification) {
        await _notifyBoZOfViolation(violationType, severity, description);
      }

      // Get resolution time requirement
      final resolutionHours = ComplianceConfig.getViolationResolutionTime(severity);
      
      _logger.w('Compliance violation: $violationType ($severity) - Resolution required within $resolutionHours hours');
    } catch (e) {
      _logger.e('Compliance violation handling failed: $e');
      rethrow;
    }
  }

  /// Helper method: Authorize transaction (simulation)
  static Future<bool> _authorizeTransaction(String? cvv, String? pin) async {
    // Simulate authorization process
    // In real implementation, CVV and PIN would be validated here
    // and then immediately discarded (PCI-DSS 3.2.1)
    
    await Future.delayed(const Duration(milliseconds: 500));
    
    // CVV and PIN are used for authorization but NOT stored
    return cvv != null && pin != null;
  }

  /// Helper method: Notify BoZ of violation (simulation)
  static Future<void> _notifyBoZOfViolation(
    String violationType,
    String severity,
    String description,
  ) async {
    // In real implementation, this would send notification to BoZ
    _logger.i('BoZ notification sent: $violationType ($severity)');
  }

  /// Example Usage in Application Code:
  static Future<void> demonstrateBoZCompliance() async {
    try {
      // Initialize compliance service
      await _complianceService.initialize();

      // Example 1: Process a transaction with compliance
      await processTransactionWithCompliance(
        userId: 'user123',
        transactionId: 'txn456',
        amount: 1500.0,
        senderPhone: '260961234567',
        receiverPhone: '260971234567',
        cvv: '123', // Used for auth, not stored
        pin: '1234', // Used for auth, not stored
      );

      // Example 2: Create user with data retention
      await createUserWithDataRetention(
        userId: 'user123',
        phoneNumber: '260961234567',
        email: '<EMAIL>',
        pinHash: 'hashed_pin_value',
        nationalId: '123456789',
      );

      // Example 3: Validate PCI-DSS compliance
      final isCompliant = await validatePciDssCompliance();
      print('PCI-DSS Compliant: $isCompliant');

      // Example 4: Generate BoZ quarterly report
      final report = await generateQuarterlyBoZReport();
      print('BoZ Report Generated: ${report.length} bytes');

      // Example 5: Monitor transaction for AML
      await monitorTransactionForAML(
        transactionId: 'txn789',
        userId: 'user123',
        amount: 25000.0, // Large amount
        transactionType: 'SEND_MONEY',
      );

      // Example 6: Handle compliance violation
      await handleComplianceViolation(
        violationType: 'DATA_ENCRYPTION_FAILURE',
        severity: 'HIGH',
        description: 'Encryption service temporarily unavailable',
        userId: 'user123',
      );

      print('✅ BoZ Compliance demonstration completed successfully');
    } catch (e) {
      print('❌ BoZ Compliance demonstration failed: $e');
    }
  }
}
