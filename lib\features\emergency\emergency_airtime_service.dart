import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/app_constants.dart';
import '../../core/config/environment_config.dart';
import '../../data/database/database_helper.dart';
import '../rural_resilience/sms_token_service.dart';
import '../mobile_money/data/services/mobile_money_service.dart';

/// Emergency Airtime Service for Drought Response
/// Provides airtime advances during emergencies with community guarantors
/// Designed for Zambian rural areas during drought/disaster periods
class EmergencyAirtimeService {
  static final EmergencyAirtimeService _instance = EmergencyAirtimeService._internal();
  factory EmergencyAirtimeService() => _instance;
  EmergencyAirtimeService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final SMSTokenService _smsTokenService = SMSTokenService();
  final MobileMoneyService _mobileMoneyService = MobileMoneyService();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  // Emergency thresholds and limits
  static const Map<String, dynamic> emergencyLimits = {
    'max_advance_amount': 50.0,        // K50 maximum advance
    'min_advance_amount': 5.0,         // K5 minimum advance
    'max_outstanding_advances': 3,     // Maximum 3 outstanding advances
    'repayment_fee_percentage': 0.05,  // 5% service fee
    'guarantor_requirement': 2,        // Minimum 2 guarantors
    'approval_timeout_hours': 24,      // 24 hours for guarantor approval
    'repayment_grace_days': 30,        // 30 days grace period
  };

  // Emergency types and their specific rules
  static const Map<String, Map<String, dynamic>> emergencyTypes = {
    'DROUGHT': {
      'name': 'Drought Response',
      'max_amount': 30.0,
      'auto_approve_threshold': 10.0,
      'regions': ['southern_province', 'western_province'],
      'seasonal_months': [5, 6, 7, 8, 9, 10], // May to October
    },
    'FLOOD': {
      'name': 'Flood Emergency',
      'max_amount': 50.0,
      'auto_approve_threshold': 15.0,
      'regions': ['eastern_province', 'luapula_province'],
      'seasonal_months': [11, 12, 1, 2, 3, 4], // November to April
    },
    'MEDICAL': {
      'name': 'Medical Emergency',
      'max_amount': 50.0,
      'auto_approve_threshold': 20.0,
      'regions': ['all'],
      'seasonal_months': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    },
    'FAMILY': {
      'name': 'Family Emergency',
      'max_amount': 25.0,
      'auto_approve_threshold': 10.0,
      'regions': ['all'],
      'seasonal_months': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    },
  };

  /// Initialize emergency airtime service
  Future<void> initialize() async {
    await _createEmergencyTables();
    await _checkEmergencyModes();
    _logger.i('Emergency airtime service initialized');
  }

  /// Create emergency airtime tables
  Future<void> _createEmergencyTables() async {
    final db = await _dbHelper.database;

    // Emergency advances table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS emergency_advances (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        emergency_type TEXT NOT NULL,
        amount REAL NOT NULL,
        service_fee REAL NOT NULL,
        total_repayment REAL NOT NULL,
        guarantor_user_ids TEXT NOT NULL,
        approval_status TEXT DEFAULT 'PENDING',
        approved_by TEXT,
        approved_at INTEGER,
        disbursed_at INTEGER,
        repaid_at INTEGER,
        status TEXT DEFAULT 'PENDING',
        reason TEXT,
        region TEXT,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000),
        expires_at INTEGER NOT NULL
      )
    ''');

    // Guarantor approvals table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS guarantor_approvals (
        id TEXT PRIMARY KEY,
        advance_id TEXT NOT NULL,
        guarantor_user_id TEXT NOT NULL,
        approval_status TEXT DEFAULT 'PENDING',
        approval_reason TEXT,
        approved_at INTEGER,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000)
      )
    ''');

    // Emergency repayments table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS emergency_repayments (
        id TEXT PRIMARY KEY,
        advance_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        amount REAL NOT NULL,
        repayment_method TEXT NOT NULL,
        transaction_id TEXT,
        repaid_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000),
        status TEXT DEFAULT 'COMPLETED'
      )
    ''');

    // Emergency modes table (for regional activation)
    await db.execute('''
      CREATE TABLE IF NOT EXISTS emergency_modes (
        id TEXT PRIMARY KEY,
        region TEXT NOT NULL,
        emergency_type TEXT NOT NULL,
        activated_at INTEGER NOT NULL,
        deactivated_at INTEGER,
        status TEXT DEFAULT 'ACTIVE',
        activation_reason TEXT,
        max_amount_override REAL
      )
    ''');
  }

  /// Request emergency airtime advance
  Future<Map<String, dynamic>> requestAdvance({
    required String userId,
    required double amount,
    required String emergencyType,
    required List<String> guarantorIds,
    String? reason,
  }) async {
    try {
      // Validate emergency type
      if (!emergencyTypes.containsKey(emergencyType)) {
        throw Exception('Invalid emergency type: $emergencyType');
      }

      // Validate amount
      final maxAmount = emergencyLimits['max_advance_amount'] as double;
      final minAmount = emergencyLimits['min_advance_amount'] as double;
      
      if (amount < minAmount || amount > maxAmount) {
        throw Exception('Amount must be between K${minAmount.toStringAsFixed(2)} and K${maxAmount.toStringAsFixed(2)}');
      }

      // Check regional eligibility
      final currentRegion = EnvironmentConfig.currentRegion;
      final emergencyConfig = emergencyTypes[emergencyType]!;
      final eligibleRegions = emergencyConfig['regions'] as List<dynamic>;
      
      if (!eligibleRegions.contains('all') && !eligibleRegions.contains(currentRegion)) {
        throw Exception('Emergency type $emergencyType not available in $currentRegion');
      }

      // Check seasonal eligibility
      final currentMonth = DateTime.now().month;
      final seasonalMonths = emergencyConfig['seasonal_months'] as List<dynamic>;
      
      if (!seasonalMonths.contains(currentMonth)) {
        _logger.w('Emergency type $emergencyType requested outside seasonal period');
      }

      // Validate guarantors
      if (guarantorIds.length < (emergencyLimits['guarantor_requirement'] as int)) {
        throw Exception('Minimum ${emergencyLimits['guarantor_requirement']} guarantors required');
      }

      // Check outstanding advances
      final outstandingAdvances = await _getOutstandingAdvances(userId);
      if (outstandingAdvances.length >= (emergencyLimits['max_outstanding_advances'] as int)) {
        throw Exception('Maximum outstanding advances limit reached');
      }

      // Calculate fees
      final serviceFee = amount * (emergencyLimits['repayment_fee_percentage'] as double);
      final totalRepayment = amount + serviceFee;

      // Create advance request
      final advanceId = _uuid.v4();
      final expiresAt = DateTime.now().add(
        Duration(hours: emergencyLimits['approval_timeout_hours'] as int)
      ).millisecondsSinceEpoch;

      final advance = {
        'id': advanceId,
        'user_id': userId,
        'emergency_type': emergencyType,
        'amount': amount,
        'service_fee': serviceFee,
        'total_repayment': totalRepayment,
        'guarantor_user_ids': jsonEncode(guarantorIds),
        'reason': reason ?? 'Emergency airtime request',
        'region': currentRegion,
        'expires_at': expiresAt,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('emergency_advances', advance);

      // Create guarantor approval requests
      for (final guarantorId in guarantorIds) {
        await _createGuarantorApproval(advanceId, guarantorId);
      }

      // Check for auto-approval
      final autoApproveThreshold = emergencyConfig['auto_approve_threshold'] as double;
      if (amount <= autoApproveThreshold) {
        await _autoApproveAdvance(advanceId);
      } else {
        // Send approval requests to guarantors
        await _sendGuarantorRequests(advanceId, guarantorIds, amount, emergencyType);
      }

      _logger.i('Emergency advance requested: $advanceId for K${amount.toStringAsFixed(2)}');
      
      return {
        'advance_id': advanceId,
        'amount': amount,
        'service_fee': serviceFee,
        'total_repayment': totalRepayment,
        'status': amount <= autoApproveThreshold ? 'AUTO_APPROVED' : 'PENDING_APPROVAL',
        'expires_at': expiresAt,
      };
      
    } catch (e) {
      _logger.e('Emergency advance request failed: $e');
      rethrow;
    }
  }

  /// Auto-approve small advances
  Future<void> _autoApproveAdvance(String advanceId) async {
    try {
      await _dbHelper.update(
        'emergency_advances',
        {
          'approval_status': 'AUTO_APPROVED',
          'approved_at': DateTime.now().millisecondsSinceEpoch,
          'approved_by': 'SYSTEM',
        },
        where: 'id = ?',
        whereArgs: [advanceId],
      );

      // Disburse airtime immediately
      await _disburseAirtime(advanceId);
      
    } catch (e) {
      _logger.e('Auto-approval failed: $e');
    }
  }

  /// Create guarantor approval request
  Future<void> _createGuarantorApproval(String advanceId, String guarantorId) async {
    final approval = {
      'id': _uuid.v4(),
      'advance_id': advanceId,
      'guarantor_user_id': guarantorId,
      'created_at': DateTime.now().millisecondsSinceEpoch,
    };

    await _dbHelper.insert('guarantor_approvals', approval);
  }

  /// Send approval requests to guarantors
  Future<void> _sendGuarantorRequests(
    String advanceId,
    List<String> guarantorIds,
    double amount,
    String emergencyType,
  ) async {
    try {
      for (final guarantorId in guarantorIds) {
        // Get guarantor phone number
        final guarantors = await _dbHelper.query(
          AppConstants.usersTable,
          where: 'id = ?',
          whereArgs: [guarantorId],
          limit: 1,
        );

        if (guarantors.isNotEmpty) {
          final guarantorPhone = guarantors.first['phone_number'] as String?;
          if (guarantorPhone != null) {
            await _sendGuarantorSMS(guarantorPhone, advanceId, amount, emergencyType);
          }
        }
      }
    } catch (e) {
      _logger.e('Failed to send guarantor requests: $e');
    }
  }

  /// Send SMS to guarantor for approval
  Future<void> _sendGuarantorSMS(
    String guarantorPhone,
    String advanceId,
    double amount,
    String emergencyType,
  ) async {
    final shortAdvanceId = advanceId.substring(0, 8).toUpperCase();
    final amountStr = 'K${amount.toStringAsFixed(2)}';
    
    final smsMessage = 'ZambiaPay: Emergency airtime request $amountStr '
                      'for $emergencyType. Advance ID: $shortAdvanceId. '
                      'Reply APPROVE $shortAdvanceId to approve or '
                      'REJECT $shortAdvanceId to reject. Valid 24hrs.';

    await _smsTokenService._queueSMSForSending(guarantorPhone, smsMessage);
  }

  /// Process guarantor approval
  Future<bool> processGuarantorApproval({
    required String advanceId,
    required String guarantorId,
    required bool approved,
    String? reason,
  }) async {
    try {
      // Update guarantor approval
      await _dbHelper.update(
        'guarantor_approvals',
        {
          'approval_status': approved ? 'APPROVED' : 'REJECTED',
          'approval_reason': reason,
          'approved_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'advance_id = ? AND guarantor_user_id = ?',
        whereArgs: [advanceId, guarantorId],
      );

      // Check if enough approvals received
      if (approved) {
        final approvals = await _dbHelper.query(
          'guarantor_approvals',
          where: 'advance_id = ? AND approval_status = ?',
          whereArgs: [advanceId, 'APPROVED'],
        );

        // Need at least 2 approvals
        if (approvals.length >= 2) {
          await _approveAdvance(advanceId);
          return true;
        }
      } else {
        // If any guarantor rejects, reject the advance
        await _rejectAdvance(advanceId, 'Rejected by guarantor');
        return false;
      }

      return approved;
    } catch (e) {
      _logger.e('Guarantor approval processing failed: $e');
      return false;
    }
  }

  /// Approve advance and disburse airtime
  Future<void> _approveAdvance(String advanceId) async {
    try {
      await _dbHelper.update(
        'emergency_advances',
        {
          'approval_status': 'APPROVED',
          'approved_at': DateTime.now().millisecondsSinceEpoch,
          'status': 'APPROVED',
        },
        where: 'id = ?',
        whereArgs: [advanceId],
      );

      // Disburse airtime
      await _disburseAirtime(advanceId);
      
    } catch (e) {
      _logger.e('Advance approval failed: $e');
    }
  }

  /// Reject advance
  Future<void> _rejectAdvance(String advanceId, String reason) async {
    await _dbHelper.update(
      'emergency_advances',
      {
        'approval_status': 'REJECTED',
        'status': 'REJECTED',
        'reason': reason,
      },
      where: 'id = ?',
      whereArgs: [advanceId],
    );
  }

  /// Disburse airtime to user
  Future<void> _disburseAirtime(String advanceId) async {
    try {
      final advances = await _dbHelper.query(
        'emergency_advances',
        where: 'id = ?',
        whereArgs: [advanceId],
        limit: 1,
      );

      if (advances.isEmpty) return;

      final advance = advances.first;
      final userId = advance['user_id'] as String;
      final amount = advance['amount'] as double;

      // Get user phone number
      final users = await _dbHelper.query(
        AppConstants.usersTable,
        where: 'id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      if (users.isEmpty) return;

      final userPhone = users.first['phone_number'] as String;
      final provider = _mobileMoneyService.detectProvider(userPhone);

      // Disburse airtime via mobile money service
      // In production, integrate with actual airtime APIs
      await Future.delayed(Duration(seconds: 2)); // Simulate API call

      // Update advance status
      await _dbHelper.update(
        'emergency_advances',
        {
          'status': 'DISBURSED',
          'disbursed_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [advanceId],
      );

      // Send confirmation SMS
      await _sendDisbursementConfirmation(userPhone, amount, advanceId);

      _logger.i('Emergency airtime disbursed: K${amount.toStringAsFixed(2)} to $userPhone');
      
    } catch (e) {
      _logger.e('Airtime disbursement failed: $e');
    }
  }

  /// Send disbursement confirmation SMS
  Future<void> _sendDisbursementConfirmation(
    String userPhone,
    double amount,
    String advanceId,
  ) async {
    final shortId = advanceId.substring(0, 8).toUpperCase();
    final amountStr = 'K${amount.toStringAsFixed(2)}';
    
    final smsMessage = 'ZambiaPay: Emergency airtime $amountStr credited to your account. '
                      'Ref: $shortId. Repayment will be deducted from future mobile money receipts. '
                      'Thank you for using ZambiaPay emergency services.';

    await _smsTokenService._queueSMSForSending(userPhone, smsMessage);
  }

  /// Process automatic repayment from mobile money receipts
  Future<void> processAutomaticRepayment({
    required String userId,
    required double receiptAmount,
    required String transactionId,
  }) async {
    try {
      // Get outstanding advances for user
      final outstandingAdvances = await _getOutstandingAdvances(userId);
      
      if (outstandingAdvances.isEmpty) return;

      // Process repayments in order of oldest first
      outstandingAdvances.sort((a, b) => 
        (a['created_at'] as int).compareTo(b['created_at'] as int));

      double remainingAmount = receiptAmount;
      
      for (final advance in outstandingAdvances) {
        if (remainingAmount <= 0) break;

        final advanceId = advance['id'] as String;
        final totalRepayment = advance['total_repayment'] as double;
        final repaymentAmount = remainingAmount >= totalRepayment 
            ? totalRepayment 
            : remainingAmount;

        // Record repayment
        await _recordRepayment(advanceId, userId, repaymentAmount, transactionId);
        
        remainingAmount -= repaymentAmount;

        // If fully repaid, mark advance as completed
        if (repaymentAmount >= totalRepayment) {
          await _dbHelper.update(
            'emergency_advances',
            {
              'status': 'REPAID',
              'repaid_at': DateTime.now().millisecondsSinceEpoch,
            },
            where: 'id = ?',
            whereArgs: [advanceId],
          );
        }
      }
      
    } catch (e) {
      _logger.e('Automatic repayment processing failed: $e');
    }
  }

  /// Record repayment
  Future<void> _recordRepayment(
    String advanceId,
    String userId,
    double amount,
    String transactionId,
  ) async {
    final repayment = {
      'id': _uuid.v4(),
      'advance_id': advanceId,
      'user_id': userId,
      'amount': amount,
      'repayment_method': 'AUTO_DEDUCTION',
      'transaction_id': transactionId,
      'repaid_at': DateTime.now().millisecondsSinceEpoch,
    };

    await _dbHelper.insert('emergency_repayments', repayment);
  }

  /// Get outstanding advances for user
  Future<List<Map<String, dynamic>>> _getOutstandingAdvances(String userId) async {
    return await _dbHelper.query(
      'emergency_advances',
      where: 'user_id = ? AND status IN (?, ?)',
      whereArgs: [userId, 'DISBURSED', 'PARTIALLY_REPAID'],
    );
  }

  /// Check and activate emergency modes based on region and season
  Future<void> _checkEmergencyModes() async {
    final currentRegion = EnvironmentConfig.currentRegion;
    final currentMonth = DateTime.now().month;
    
    // Check if drought mode should be activated
    if (['southern_province', 'western_province'].contains(currentRegion) &&
        [5, 6, 7, 8, 9, 10].contains(currentMonth)) {
      await _activateEmergencyMode('DROUGHT', currentRegion, 'Seasonal drought period');
    }
    
    // Check if flood mode should be activated
    if (['eastern_province', 'luapula_province'].contains(currentRegion) &&
        [11, 12, 1, 2, 3, 4].contains(currentMonth)) {
      await _activateEmergencyMode('FLOOD', currentRegion, 'Seasonal flood period');
    }
  }

  /// Activate emergency mode for region
  Future<void> _activateEmergencyMode(
    String emergencyType,
    String region,
    String reason,
  ) async {
    try {
      // Check if already active
      final existing = await _dbHelper.query(
        'emergency_modes',
        where: 'region = ? AND emergency_type = ? AND status = ?',
        whereArgs: [region, emergencyType, 'ACTIVE'],
      );

      if (existing.isNotEmpty) return;

      final emergencyMode = {
        'id': _uuid.v4(),
        'region': region,
        'emergency_type': emergencyType,
        'activated_at': DateTime.now().millisecondsSinceEpoch,
        'activation_reason': reason,
        'status': 'ACTIVE',
      };

      await _dbHelper.insert('emergency_modes', emergencyMode);
      
      _logger.i('Emergency mode activated: $emergencyType in $region');
    } catch (e) {
      _logger.e('Failed to activate emergency mode: $e');
    }
  }

  /// Get user's emergency advance history
  Future<List<Map<String, dynamic>>> getUserAdvanceHistory(String userId) async {
    return await _dbHelper.query(
      'emergency_advances',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );
  }

  /// Get emergency statistics
  Future<Map<String, dynamic>> getEmergencyStatistics() async {
    try {
      final allAdvances = await _dbHelper.query('emergency_advances');
      final approvedAdvances = allAdvances.where((a) => a['status'] == 'DISBURSED').length;
      final totalDisbursed = allAdvances
          .where((a) => a['status'] == 'DISBURSED')
          .fold(0.0, (sum, a) => sum + (a['amount'] as num).toDouble());

      return {
        'total_requests': allAdvances.length,
        'approved_requests': approvedAdvances,
        'approval_rate': allAdvances.isNotEmpty 
            ? (approvedAdvances / allAdvances.length * 100).toStringAsFixed(1)
            : '0.0',
        'total_disbursed': totalDisbursed,
        'average_advance': approvedAdvances > 0 
            ? (totalDisbursed / approvedAdvances).toStringAsFixed(2)
            : '0.00',
      };
    } catch (e) {
      _logger.e('Failed to get emergency statistics: $e');
      return {};
    }
  }
}
