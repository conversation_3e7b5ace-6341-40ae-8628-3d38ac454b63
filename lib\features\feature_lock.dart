/// Feature Lock System for Pay Mule Zambia Mobile Money MVP
/// Disables banking features while maintaining mobile money core functionality
///
/// CORE MANDATE: Mobile money-only release • No banking features • Zero breakage

import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../core/config/app_config.dart';

class Features {
  static final Logger _logger = Logger();
  
  // Banking Features (DISABLED for MVP)
  static const String BANK_LINKING = 'bank_linking';
  static const String BANK_TRANSFERS = 'bank_transfers';
  static const String BANK_ACCOUNT_MANAGEMENT = 'bank_account_management';
  static const String BANK_STATEMENTS = 'bank_statements';
  static const String BANK_CARDS = 'bank_cards';
  
  // Mobile Money Core Features (ENABLED for MVP)
  static const String MOBILE_MONEY = 'mobile_money';
  static const String CHILIMBA = 'chilimba';
  static const String UTILITY_PAYMENTS = 'utility_payments';
  static const String AGENT_LOCATOR = 'agent_locator';
  static const String OFFLINE_SYNC = 'offline_sync';
  static const String QR_PAYMENTS = 'qr_payments';
  static const String TRANSACTION_HISTORY = 'transaction_history';
  static const String BALANCE_INQUIRY = 'balance_inquiry';
  static const String SEND_MONEY = 'send_money';
  static const String RECEIVE_MONEY = 'receive_money';
  static const String AIRTIME_PURCHASE = 'airtime_purchase';
  
  // Feature state storage
  static final Map<String, bool> _featureStates = {};
  static bool _isInitialized = false;
  
  /// Initialize feature lock system for mobile money MVP
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    _logger.i('🇿🇲 INITIALIZING PAY MULE ZAMBIA MOBILE MONEY MVP');
    _logger.i('🔒 Feature Lock: Banking features disabled');
    
    // Disable all banking features
    await disableBankingFeatures();
    
    // Enable mobile money core features
    await enableMobileMoneyCore();
    
    _isInitialized = true;
    _logger.i('✅ Feature lock system initialized successfully');
  }
  
  /// Disable all banking-related features
  static Future<void> disableBankingFeatures() async {
    _logger.i('🚫 Disabling banking features for mobile money MVP...');
    
    // Disable banking components
    disable(BANK_LINKING);
    disable(BANK_TRANSFERS);
    disable(BANK_ACCOUNT_MANAGEMENT);
    disable(BANK_STATEMENTS);
    disable(BANK_CARDS);
    
    _logger.i('✅ Banking features disabled successfully');
  }
  
  /// Enable mobile money core functionality
  static Future<void> enableMobileMoneyCore() async {
    _logger.i('📱 Enabling mobile money core features...');
    
    // Core mobile money features
    enable(MOBILE_MONEY);
    enable(CHILIMBA);
    enable(UTILITY_PAYMENTS);
    enable(AGENT_LOCATOR);
    enable(OFFLINE_SYNC);
    enable(QR_PAYMENTS);
    enable(TRANSACTION_HISTORY);
    enable(BALANCE_INQUIRY);
    enable(SEND_MONEY);
    enable(RECEIVE_MONEY);
    enable(AIRTIME_PURCHASE);
    
    _logger.i('✅ Mobile money core features enabled successfully');
  }
  
  /// Enable a specific feature
  static void enable(String feature) {
    _featureStates[feature] = true;
    _logger.d('✅ Feature enabled: $feature');
  }
  
  /// Disable a specific feature
  static void disable(String feature) {
    _featureStates[feature] = false;
    _logger.d('🚫 Feature disabled: $feature');
  }
  
  /// Check if a feature is enabled
  static bool isEnabled(String feature) {
    if (!_isInitialized) {
      _logger.w('⚠️ Feature lock system not initialized. Defaulting to false for: $feature');
      return false;
    }
    return _featureStates[feature] ?? false;
  }
  
  /// Check if banking features are disabled (should be true for MVP)
  static bool areBankingFeaturesDisabled() {
    return !isEnabled(BANK_LINKING) && 
           !isEnabled(BANK_TRANSFERS) && 
           !isEnabled(BANK_ACCOUNT_MANAGEMENT) && 
           !isEnabled(BANK_STATEMENTS) && 
           !isEnabled(BANK_CARDS);
  }
  
  /// Check if mobile money core is enabled (should be true for MVP)
  static bool isMobileMoneyEnabled() {
    return isEnabled(MOBILE_MONEY) && 
           isEnabled(CHILIMBA) && 
           isEnabled(UTILITY_PAYMENTS) && 
           isEnabled(AGENT_LOCATOR);
  }
  
  /// Get all enabled features
  static List<String> getEnabledFeatures() {
    return _featureStates.entries
        .where((entry) => entry.value == true)
        .map((entry) => entry.key)
        .toList();
  }
  
  /// Get all disabled features
  static List<String> getDisabledFeatures() {
    return _featureStates.entries
        .where((entry) => entry.value == false)
        .map((entry) => entry.key)
        .toList();
  }
  
  /// Get feature lock status summary
  static Map<String, dynamic> getFeatureLockStatus() {
    return {
      'mvp_mode': 'mobile_money_only',
      'banking_features_disabled': areBankingFeaturesDisabled(),
      'mobile_money_enabled': isMobileMoneyEnabled(),
      'enabled_features': getEnabledFeatures(),
      'disabled_features': getDisabledFeatures(),
      'total_features': _featureStates.length,
      'initialized': _isInitialized,
    };
  }
}

/// UI Refactor utilities for removing banking components
class UIRefactor {
  static final Logger _logger = Logger();
  
  /// Remove banking tabs from navigation
  static void removeBankingTabs() {
    _logger.i('🎨 UI Refactor: Removing banking tabs for mobile money MVP');
    
    // Banking tabs are removed by not including them in navigation
    // This is handled in the UI layer by checking Features.isEnabled()
    
    _logger.i('✅ Banking tabs removed from navigation');
  }
  
  /// Simplify UI for mobile money focus
  static void simplifyForMobileMoneyMVP() {
    _logger.i('🎨 UI Refactor: Simplifying UI for mobile money MVP');
    
    // Remove banking-related UI components
    removeBankingTabs();
    
    // Focus on mobile money features
    _logger.i('📱 Focusing UI on mobile money features');
    
    _logger.i('✅ UI simplified for mobile money MVP');
  }
}

/// Feature-aware widget mixin for conditional rendering
mixin FeatureAware {
  /// Render widget only if feature is enabled
  Widget? renderIfEnabled(String feature, Widget widget) {
    return Features.isEnabled(feature) ? widget : null;
  }
  
  /// Render different widgets based on feature state
  Widget renderConditional(String feature, Widget enabledWidget, Widget disabledWidget) {
    return Features.isEnabled(feature) ? enabledWidget : disabledWidget;
  }
  
  /// Check if banking features should be hidden
  bool shouldHideBankingFeatures() {
    return Features.areBankingFeaturesDisabled();
  }
  
  /// Check if mobile money features should be shown
  bool shouldShowMobileMoneyFeatures() {
    return Features.isMobileMoneyEnabled();
  }
}
