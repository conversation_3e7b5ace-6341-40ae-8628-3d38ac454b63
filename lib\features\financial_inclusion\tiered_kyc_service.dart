import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/app_constants.dart';
import '../../core/security/encryption_service.dart';
import '../../data/database/database_helper.dart';

/// Tiered KYC Service for Financial Inclusion
/// Basic access with phone number, full features with NRC
/// Designed for Zambian regulatory requirements and rural accessibility
class TieredKYCService {
  static final TieredKYCService _instance = TieredKYCService._internal();
  factory TieredKYCService() => _instance;
  TieredKYCService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final EncryptionService _encryptionService = EncryptionService();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  // KYC Tiers and Limits (Bank of Zambia Guidelines)
  static const Map<String, Map<String, dynamic>> kycTiers = {
    'BASIC': {
      'name': 'Basic Account',
      'requirements': ['phone_number'],
      'daily_limit': 1000.0,      // K1,000 per day
      'monthly_limit': 10000.0,   // K10,000 per month
      'annual_limit': 50000.0,    // K50,000 per year
      'features': ['send_money', 'receive_money', 'buy_airtime', 'basic_bills'],
      'verification_method': 'SMS_OTP',
      'max_balance': 5000.0,      // K5,000 maximum balance
    },
    'INTERMEDIATE': {
      'name': 'Verified Account',
      'requirements': ['phone_number', 'national_id', 'selfie'],
      'daily_limit': 10000.0,     // K10,000 per day
      'monthly_limit': 100000.0,  // K100,000 per month
      'annual_limit': 500000.0,   // K500,000 per year
      'features': ['all_basic', 'utility_bills', 'merchant_payments', 'savings'],
      'verification_method': 'BIOMETRIC',
      'max_balance': 50000.0,     // K50,000 maximum balance
    },
    'FULL': {
      'name': 'Full KYC Account',
      'requirements': ['phone_number', 'national_id', 'selfie', 'proof_of_address', 'income_verification'],
      'daily_limit': 50000.0,     // K50,000 per day
      'monthly_limit': 500000.0,  // K500,000 per month
      'annual_limit': 2000000.0,  // K2,000,000 per year
      'features': ['all_intermediate', 'international_transfers', 'loans', 'investments'],
      'verification_method': 'FULL_BIOMETRIC',
      'max_balance': 200000.0,    // K200,000 maximum balance
    },
  };

  /// Initialize KYC service
  Future<void> initialize() async {
    await _createKYCTables();
    _logger.i('Tiered KYC service initialized');
  }

  /// Create KYC tracking tables
  Future<void> _createKYCTables() async {
    final db = await _dbHelper.database;

    // KYC profiles table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS kyc_profiles (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL UNIQUE,
        current_tier TEXT NOT NULL DEFAULT 'BASIC',
        phone_number TEXT NOT NULL,
        phone_verified INTEGER DEFAULT 0,
        national_id TEXT,
        national_id_verified INTEGER DEFAULT 0,
        selfie_path TEXT,
        selfie_verified INTEGER DEFAULT 0,
        proof_of_address_path TEXT,
        address_verified INTEGER DEFAULT 0,
        income_verification_path TEXT,
        income_verified INTEGER DEFAULT 0,
        verification_score INTEGER DEFAULT 0,
        risk_rating TEXT DEFAULT 'LOW',
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000),
        verified_at INTEGER,
        verification_notes TEXT
      )
    ''');

    // KYC documents table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS kyc_documents (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        document_type TEXT NOT NULL,
        document_path TEXT NOT NULL,
        verification_status TEXT DEFAULT 'PENDING',
        verification_notes TEXT,
        uploaded_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000),
        verified_at INTEGER,
        verified_by TEXT
      )
    ''');

    // Transaction limits tracking
    await db.execute('''
      CREATE TABLE IF NOT EXISTS kyc_limits_usage (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        period_type TEXT NOT NULL,
        period_start INTEGER NOT NULL,
        period_end INTEGER NOT NULL,
        amount_used REAL DEFAULT 0.0,
        transaction_count INTEGER DEFAULT 0,
        last_transaction_at INTEGER,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000)
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX IF NOT EXISTS idx_kyc_profiles_user_id ON kyc_profiles (user_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_kyc_limits_user_period ON kyc_limits_usage (user_id, period_type)');
  }

  /// Create basic KYC profile with phone number only
  Future<String> createBasicProfile({
    required String userId,
    required String phoneNumber,
  }) async {
    try {
      // Encrypt phone number
      final encryptedPhone = await _encryptionService.encryptData(phoneNumber);
      
      final kycProfile = {
        'id': _uuid.v4(),
        'user_id': userId,
        'current_tier': 'BASIC',
        'phone_number': encryptedPhone,
        'phone_verified': 0,
        'verification_score': 10, // Basic score for phone registration
        'risk_rating': 'LOW',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('kyc_profiles', kycProfile);

      // Initialize usage tracking
      await _initializeUsageTracking(userId);

      _logger.i('Basic KYC profile created for user: $userId');
      return kycProfile['id'] as String;
      
    } catch (e) {
      _logger.e('Failed to create basic KYC profile: $e');
      rethrow;
    }
  }

  /// Verify phone number via SMS OTP
  Future<bool> verifyPhoneNumber({
    required String userId,
    required String otpCode,
  }) async {
    try {
      // In production, verify OTP against sent code
      // For demo, accept any 6-digit code
      if (otpCode.length != 6 || !RegExp(r'^\d{6}$').hasMatch(otpCode)) {
        return false;
      }

      await _dbHelper.update(
        'kyc_profiles',
        {
          'phone_verified': 1,
          'verification_score': 25, // Increase score for verified phone
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      _logger.i('Phone number verified for user: $userId');
      return true;
      
    } catch (e) {
      _logger.e('Phone verification failed: $e');
      return false;
    }
  }

  /// Upload and verify National ID (NRC)
  Future<bool> uploadNationalId({
    required String userId,
    required String nationalIdNumber,
    required String documentPath,
  }) async {
    try {
      // Encrypt NRC number
      final encryptedNRC = await _encryptionService.encryptData(nationalIdNumber);
      
      // Update KYC profile
      await _dbHelper.update(
        'kyc_profiles',
        {
          'national_id': encryptedNRC,
          'national_id_verified': 1, // Auto-verify for demo
          'verification_score': 60, // Significant score increase
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      // Store document record
      final document = {
        'id': _uuid.v4(),
        'user_id': userId,
        'document_type': 'NATIONAL_ID',
        'document_path': documentPath,
        'verification_status': 'VERIFIED',
        'uploaded_at': DateTime.now().millisecondsSinceEpoch,
        'verified_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('kyc_documents', document);

      // Check for tier upgrade
      await _checkTierUpgrade(userId);

      _logger.i('National ID uploaded and verified for user: $userId');
      return true;
      
    } catch (e) {
      _logger.e('National ID upload failed: $e');
      return false;
    }
  }

  /// Upload selfie for biometric verification
  Future<bool> uploadSelfie({
    required String userId,
    required String selfiePath,
  }) async {
    try {
      await _dbHelper.update(
        'kyc_profiles',
        {
          'selfie_path': selfiePath,
          'selfie_verified': 1, // Auto-verify for demo
          'verification_score': 80, // High score for biometric
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      // Store document record
      final document = {
        'id': _uuid.v4(),
        'user_id': userId,
        'document_type': 'SELFIE',
        'document_path': selfiePath,
        'verification_status': 'VERIFIED',
        'uploaded_at': DateTime.now().millisecondsSinceEpoch,
        'verified_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('kyc_documents', document);

      // Check for tier upgrade
      await _checkTierUpgrade(userId);

      _logger.i('Selfie uploaded and verified for user: $userId');
      return true;
      
    } catch (e) {
      _logger.e('Selfie upload failed: $e');
      return false;
    }
  }

  /// Check and upgrade KYC tier based on verification status
  Future<void> _checkTierUpgrade(String userId) async {
    try {
      final profiles = await _dbHelper.query(
        'kyc_profiles',
        where: 'user_id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      if (profiles.isEmpty) return;

      final profile = profiles.first;
      final phoneVerified = (profile['phone_verified'] as int) == 1;
      final nrcVerified = (profile['national_id_verified'] as int) == 1;
      final selfieVerified = (profile['selfie_verified'] as int) == 1;
      final addressVerified = (profile['address_verified'] as int) == 1;
      final incomeVerified = (profile['income_verified'] as int) == 1;

      String newTier = 'BASIC';
      
      if (phoneVerified && nrcVerified && selfieVerified && addressVerified && incomeVerified) {
        newTier = 'FULL';
      } else if (phoneVerified && nrcVerified && selfieVerified) {
        newTier = 'INTERMEDIATE';
      } else if (phoneVerified) {
        newTier = 'BASIC';
      }

      if (newTier != profile['current_tier']) {
        await _dbHelper.update(
          'kyc_profiles',
          {
            'current_tier': newTier,
            'verified_at': DateTime.now().millisecondsSinceEpoch,
            'updated_at': DateTime.now().millisecondsSinceEpoch,
          },
          where: 'user_id = ?',
          whereArgs: [userId],
        );

        _logger.i('KYC tier upgraded to $newTier for user: $userId');
      }
      
    } catch (e) {
      _logger.e('Tier upgrade check failed: $e');
    }
  }

  /// Check if transaction is within KYC limits
  Future<bool> checkTransactionLimits({
    required String userId,
    required double amount,
    required String transactionType,
  }) async {
    try {
      final profile = await getUserKYCProfile(userId);
      if (profile == null) return false;

      final tier = profile['current_tier'] as String;
      final tierLimits = kycTiers[tier];
      if (tierLimits == null) return false;

      // Check daily limit
      final dailyUsage = await _getUsageForPeriod(userId, 'DAILY');
      final dailyLimit = tierLimits['daily_limit'] as double;
      if (dailyUsage + amount > dailyLimit) {
        _logger.w('Daily limit exceeded for user $userId: ${dailyUsage + amount} > $dailyLimit');
        return false;
      }

      // Check monthly limit
      final monthlyUsage = await _getUsageForPeriod(userId, 'MONTHLY');
      final monthlyLimit = tierLimits['monthly_limit'] as double;
      if (monthlyUsage + amount > monthlyLimit) {
        _logger.w('Monthly limit exceeded for user $userId: ${monthlyUsage + amount} > $monthlyLimit');
        return false;
      }

      // Check annual limit
      final annualUsage = await _getUsageForPeriod(userId, 'ANNUAL');
      final annualLimit = tierLimits['annual_limit'] as double;
      if (annualUsage + amount > annualLimit) {
        _logger.w('Annual limit exceeded for user $userId: ${annualUsage + amount} > $annualLimit');
        return false;
      }

      return true;
      
    } catch (e) {
      _logger.e('Transaction limit check failed: $e');
      return false;
    }
  }

  /// Record transaction usage for limit tracking
  Future<void> recordTransactionUsage({
    required String userId,
    required double amount,
    required String transactionType,
  }) async {
    try {
      final now = DateTime.now();
      
      // Update daily usage
      await _updateUsageForPeriod(userId, 'DAILY', amount, now);
      
      // Update monthly usage
      await _updateUsageForPeriod(userId, 'MONTHLY', amount, now);
      
      // Update annual usage
      await _updateUsageForPeriod(userId, 'ANNUAL', amount, now);
      
    } catch (e) {
      _logger.e('Failed to record transaction usage: $e');
    }
  }

  /// Get usage for specific period
  Future<double> _getUsageForPeriod(String userId, String periodType) async {
    final now = DateTime.now();
    final periodStart = _getPeriodStart(now, periodType);
    
    final usage = await _dbHelper.query(
      'kyc_limits_usage',
      where: 'user_id = ? AND period_type = ? AND period_start = ?',
      whereArgs: [userId, periodType, periodStart.millisecondsSinceEpoch],
      limit: 1,
    );

    return usage.isNotEmpty ? (usage.first['amount_used'] as num).toDouble() : 0.0;
  }

  /// Update usage for specific period
  Future<void> _updateUsageForPeriod(String userId, String periodType, double amount, DateTime now) async {
    final periodStart = _getPeriodStart(now, periodType);
    final periodEnd = _getPeriodEnd(now, periodType);
    
    final existing = await _dbHelper.query(
      'kyc_limits_usage',
      where: 'user_id = ? AND period_type = ? AND period_start = ?',
      whereArgs: [userId, periodType, periodStart.millisecondsSinceEpoch],
      limit: 1,
    );

    if (existing.isNotEmpty) {
      // Update existing record
      final currentUsage = (existing.first['amount_used'] as num).toDouble();
      final currentCount = existing.first['transaction_count'] as int;
      
      await _dbHelper.update(
        'kyc_limits_usage',
        {
          'amount_used': currentUsage + amount,
          'transaction_count': currentCount + 1,
          'last_transaction_at': now.millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [existing.first['id']],
      );
    } else {
      // Create new record
      final usageRecord = {
        'id': _uuid.v4(),
        'user_id': userId,
        'period_type': periodType,
        'period_start': periodStart.millisecondsSinceEpoch,
        'period_end': periodEnd.millisecondsSinceEpoch,
        'amount_used': amount,
        'transaction_count': 1,
        'last_transaction_at': now.millisecondsSinceEpoch,
        'created_at': now.millisecondsSinceEpoch,
      };

      await _dbHelper.insert('kyc_limits_usage', usageRecord);
    }
  }

  /// Get period start date
  DateTime _getPeriodStart(DateTime date, String periodType) {
    switch (periodType) {
      case 'DAILY':
        return DateTime(date.year, date.month, date.day);
      case 'MONTHLY':
        return DateTime(date.year, date.month, 1);
      case 'ANNUAL':
        return DateTime(date.year, 1, 1);
      default:
        return date;
    }
  }

  /// Get period end date
  DateTime _getPeriodEnd(DateTime date, String periodType) {
    switch (periodType) {
      case 'DAILY':
        return DateTime(date.year, date.month, date.day, 23, 59, 59);
      case 'MONTHLY':
        return DateTime(date.year, date.month + 1, 0, 23, 59, 59);
      case 'ANNUAL':
        return DateTime(date.year, 12, 31, 23, 59, 59);
      default:
        return date;
    }
  }

  /// Initialize usage tracking for new user
  Future<void> _initializeUsageTracking(String userId) async {
    // Pre-create current period records with zero usage
    final now = DateTime.now();
    
    for (final periodType in ['DAILY', 'MONTHLY', 'ANNUAL']) {
      final periodStart = _getPeriodStart(now, periodType);
      final periodEnd = _getPeriodEnd(now, periodType);
      
      final usageRecord = {
        'id': _uuid.v4(),
        'user_id': userId,
        'period_type': periodType,
        'period_start': periodStart.millisecondsSinceEpoch,
        'period_end': periodEnd.millisecondsSinceEpoch,
        'amount_used': 0.0,
        'transaction_count': 0,
        'created_at': now.millisecondsSinceEpoch,
      };

      await _dbHelper.insert('kyc_limits_usage', usageRecord);
    }
  }

  /// Get user's KYC profile
  Future<Map<String, dynamic>?> getUserKYCProfile(String userId) async {
    final profiles = await _dbHelper.query(
      'kyc_profiles',
      where: 'user_id = ?',
      whereArgs: [userId],
      limit: 1,
    );

    return profiles.isNotEmpty ? profiles.first : null;
  }

  /// Get user's current limits and usage
  Future<Map<String, dynamic>> getUserLimitsAndUsage(String userId) async {
    try {
      final profile = await getUserKYCProfile(userId);
      if (profile == null) {
        return {'error': 'KYC profile not found'};
      }

      final tier = profile['current_tier'] as String;
      final tierLimits = kycTiers[tier];
      
      final dailyUsage = await _getUsageForPeriod(userId, 'DAILY');
      final monthlyUsage = await _getUsageForPeriod(userId, 'MONTHLY');
      final annualUsage = await _getUsageForPeriod(userId, 'ANNUAL');

      return {
        'tier': tier,
        'tier_name': tierLimits?['name'],
        'limits': tierLimits,
        'usage': {
          'daily': dailyUsage,
          'monthly': monthlyUsage,
          'annual': annualUsage,
        },
        'remaining': {
          'daily': (tierLimits?['daily_limit'] as double? ?? 0.0) - dailyUsage,
          'monthly': (tierLimits?['monthly_limit'] as double? ?? 0.0) - monthlyUsage,
          'annual': (tierLimits?['annual_limit'] as double? ?? 0.0) - annualUsage,
        },
      };
    } catch (e) {
      _logger.e('Failed to get user limits and usage: $e');
      return {'error': e.toString()};
    }
  }

  /// Get KYC requirements for next tier
  Future<Map<String, dynamic>> getNextTierRequirements(String userId) async {
    final profile = await getUserKYCProfile(userId);
    if (profile == null) return {};

    final currentTier = profile['current_tier'] as String;
    
    switch (currentTier) {
      case 'BASIC':
        return {
          'next_tier': 'INTERMEDIATE',
          'requirements': ['national_id', 'selfie'],
          'benefits': [
            'K10,000 daily limit (10x increase)',
            'K100,000 monthly limit',
            'Utility bill payments',
            'Merchant payments',
            'Savings features',
          ],
        };
      case 'INTERMEDIATE':
        return {
          'next_tier': 'FULL',
          'requirements': ['proof_of_address', 'income_verification'],
          'benefits': [
            'K50,000 daily limit (5x increase)',
            'K500,000 monthly limit',
            'International transfers',
            'Loan eligibility',
            'Investment products',
          ],
        };
      default:
        return {
          'message': 'You have the highest KYC tier available',
        };
    }
  }
}
