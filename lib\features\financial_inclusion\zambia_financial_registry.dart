/// Zambia Financial Registry Service
/// Integrates with Bank of Zambia approved agent registry
/// Manages licensed financial service providers and agents
/// Ensures compliance with BoZ regulations

import 'package:logger/logger.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../core/production_lock.dart';
import '../../core/config/production_config.dart';
import '../../services/agent_service.dart';

/// Production mode flag - controlled by ProductionLock system
bool get kProductionMode => ProductionLock().isProductionMode;

class ZambiaFinancialRegistry {
  static final Logger _logger = Logger();

  // BoZ Registry endpoints
  static const String _bozRegistryBaseUrl = 'https://api.boz.zm';
  static const String _agentRegistryEndpoint = '/financial-services/agents';
  static const String _licenseVerificationEndpoint = '/licenses/verify';

  /// Get approved agents from Zambian Financial Registry
  static Future<List<ZambianAgent>> getApprovedAgents() async {
    try {
      _logger.i('🏛️ Fetching approved agents from Zambian Financial Registry');

      if (kProductionMode) {
        return await _fetchProductionAgents();
      } else {
        return await _getMockAgents();
      }
    } catch (e) {
      _logger.e('❌ Failed to get approved agents: $e');
      return await _getMockAgents(); // Fallback to mock data
    }
  }

  /// Fetch latest agents from BoZ registry
  static Future<List<ZambianAgent>> fetchLatestAgents() async {
    try {
      _logger.i('🔄 Fetching latest agents from BoZ registry');

      if (kProductionMode) {
        return await _fetchProductionAgents();
      } else {
        return await _getMockAgents();
      }
    } catch (e) {
      _logger.e('❌ Failed to fetch latest agents: $e');
      rethrow;
    }
  }

  /// Verify agent license with BoZ
  static Future<bool> verifyAgentLicense(String licenseNumber) async {
    try {
      _logger.i('🔍 Verifying agent license: $licenseNumber');

      if (kProductionMode) {
        return await _verifyProductionLicense(licenseNumber);
      } else {
        // Development: Simulate license verification
        return licenseNumber.startsWith('BOZ');
      }
    } catch (e) {
      _logger.e('❌ License verification failed: $e');
      return false;
    }
  }

  /// Fetch agents from production BoZ API
  static Future<List<ZambianAgent>> _fetchProductionAgents() async {
    try {
      final config = ProductionConfig.getProductionConfig('BOZ');
      final apiKey = config['apiKey'] ?? 'PROD_BOZ_API_KEY';

      final response = await http.get(
        Uri.parse('$_bozRegistryBaseUrl$_agentRegistryEndpoint'),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
          'X-Country': 'ZM',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final agentList = data['agents'] as List;
        
        return agentList.map((agentData) => _parseAgentData(agentData)).toList();
      } else {
        throw Exception('BoZ API error: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Production agent fetch failed: $e');
      rethrow;
    }
  }

  /// Verify license with production BoZ API
  static Future<bool> _verifyProductionLicense(String licenseNumber) async {
    try {
      final config = ProductionConfig.getProductionConfig('BOZ');
      final apiKey = config['apiKey'] ?? 'PROD_BOZ_API_KEY';

      final response = await http.post(
        Uri.parse('$_bozRegistryBaseUrl$_licenseVerificationEndpoint'),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'license_number': licenseNumber,
          'service_type': 'financial_agent',
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['valid'] == true && data['status'] == 'active';
      }

      return false;
    } catch (e) {
      _logger.e('Production license verification failed: $e');
      return false;
    }
  }

  /// Parse agent data from BoZ API response
  static ZambianAgent _parseAgentData(Map<String, dynamic> data) {
    return ZambianAgent(
      agentId: data['agent_id'] as String,
      name: data['business_name'] as String,
      phoneNumber: data['contact_phone'] as String,
      locationName: data['location_description'] as String,
      latitude: (data['coordinates']['latitude'] as num).toDouble(),
      longitude: (data['coordinates']['longitude'] as num).toDouble(),
      services: List<String>.from(data['authorized_services']),
      operatingHours: Map<String, String>.from(data['operating_hours']),
      commissionRates: Map<String, double>.from(
        data['commission_rates'].map((k, v) => MapEntry(k, (v as num).toDouble()))
      ),
      cashLimit: (data['daily_cash_limit'] as num).toDouble(),
      status: data['status'] as String,
      bozLicense: data['boz_license_number'] as String,
      lastUpdated: DateTime.parse(data['last_updated'] as String),
    );
  }

  /// Get mock agents for development and testing
  static Future<List<ZambianAgent>> _getMockAgents() async {
    _logger.i('🧪 Using mock Zambian agents for development');

    // Simulate network delay
    await Future.delayed(Duration(seconds: 1));

    return [
      // Lusaka agents
      ZambianAgent(
        agentId: 'ZM_AGENT_001',
        name: 'Shoprite Manda Hill',
        phoneNumber: '+260211234567',
        locationName: 'Manda Hill Shopping Mall, Lusaka',
        latitude: -15.3875,
        longitude: 28.3228,
        services: ['cash_in', 'cash_out', 'money_transfer', 'bill_payment'],
        operatingHours: {
          'monday': '08:00-20:00',
          'tuesday': '08:00-20:00',
          'wednesday': '08:00-20:00',
          'thursday': '08:00-20:00',
          'friday': '08:00-20:00',
          'saturday': '08:00-20:00',
          'sunday': '09:00-18:00',
        },
        commissionRates: {
          'cash_in': 0.01,
          'cash_out': 0.015,
          'money_transfer': 0.02,
          'bill_payment': 0.005,
        },
        cashLimit: 50000.0,
        status: 'active',
        bozLicense: 'BOZ/FA/2024/001',
        lastUpdated: DateTime.now(),
      ),
      
      ZambianAgent(
        agentId: 'ZM_AGENT_002',
        name: 'Pick n Pay Arcades',
        phoneNumber: '+260211234568',
        locationName: 'Arcades Shopping Centre, Lusaka',
        latitude: -15.4067,
        longitude: 28.2833,
        services: ['cash_in', 'cash_out', 'airtime_sales'],
        operatingHours: {
          'monday': '07:30-21:00',
          'tuesday': '07:30-21:00',
          'wednesday': '07:30-21:00',
          'thursday': '07:30-21:00',
          'friday': '07:30-21:00',
          'saturday': '07:30-21:00',
          'sunday': '08:00-20:00',
        },
        commissionRates: {
          'cash_in': 0.01,
          'cash_out': 0.015,
          'airtime_sales': 0.05,
        },
        cashLimit: 75000.0,
        status: 'active',
        bozLicense: 'BOZ/FA/2024/002',
        lastUpdated: DateTime.now(),
      ),

      ZambianAgent(
        agentId: 'ZM_AGENT_003',
        name: 'Game Stores Levy',
        phoneNumber: '+260211234569',
        locationName: 'Levy Junction Mall, Lusaka',
        latitude: -15.3928,
        longitude: 28.3225,
        services: ['cash_in', 'cash_out', 'money_transfer', 'bill_payment', 'zesco_payment'],
        operatingHours: {
          'monday': '09:00-19:00',
          'tuesday': '09:00-19:00',
          'wednesday': '09:00-19:00',
          'thursday': '09:00-19:00',
          'friday': '09:00-19:00',
          'saturday': '09:00-19:00',
          'sunday': '10:00-17:00',
        },
        commissionRates: {
          'cash_in': 0.01,
          'cash_out': 0.015,
          'money_transfer': 0.02,
          'bill_payment': 0.005,
          'zesco_payment': 0.003,
        },
        cashLimit: 100000.0,
        status: 'active',
        bozLicense: 'BOZ/FA/2024/003',
        lastUpdated: DateTime.now(),
      ),

      // Kitwe agents
      ZambianAgent(
        agentId: 'ZM_AGENT_004',
        name: 'Mukuba Mall Agent',
        phoneNumber: '+260212345678',
        locationName: 'Mukuba Mall, Kitwe',
        latitude: -12.8044,
        longitude: 28.2133,
        services: ['cash_in', 'cash_out', 'money_transfer'],
        operatingHours: {
          'monday': '08:00-18:00',
          'tuesday': '08:00-18:00',
          'wednesday': '08:00-18:00',
          'thursday': '08:00-18:00',
          'friday': '08:00-18:00',
          'saturday': '08:00-18:00',
          'sunday': 'closed',
        },
        commissionRates: {
          'cash_in': 0.012,
          'cash_out': 0.017,
          'money_transfer': 0.022,
        },
        cashLimit: 30000.0,
        status: 'active',
        bozLicense: 'BOZ/FA/2024/004',
        lastUpdated: DateTime.now(),
      ),

      // Ndola agents
      ZambianAgent(
        agentId: 'ZM_AGENT_005',
        name: 'Jacaranda Mall Agent',
        phoneNumber: '+260212345679',
        locationName: 'Jacaranda Mall, Ndola',
        latitude: -12.9587,
        longitude: 28.6366,
        services: ['cash_in', 'cash_out', 'bill_payment', 'airtime_sales'],
        operatingHours: {
          'monday': '08:30-19:00',
          'tuesday': '08:30-19:00',
          'wednesday': '08:30-19:00',
          'thursday': '08:30-19:00',
          'friday': '08:30-19:00',
          'saturday': '08:30-19:00',
          'sunday': '09:00-17:00',
        },
        commissionRates: {
          'cash_in': 0.011,
          'cash_out': 0.016,
          'bill_payment': 0.005,
          'airtime_sales': 0.05,
        },
        cashLimit: 40000.0,
        status: 'active',
        bozLicense: 'BOZ/FA/2024/005',
        lastUpdated: DateTime.now(),
      ),

      // Livingstone agents
      ZambianAgent(
        agentId: 'ZM_AGENT_006',
        name: 'Mosi-oa-Tunya Agent',
        phoneNumber: '+260213456789',
        locationName: 'Mosi-oa-Tunya Road, Livingstone',
        latitude: -17.8419,
        longitude: 25.8561,
        services: ['cash_in', 'cash_out', 'money_transfer', 'forex_exchange'],
        operatingHours: {
          'monday': '08:00-17:00',
          'tuesday': '08:00-17:00',
          'wednesday': '08:00-17:00',
          'thursday': '08:00-17:00',
          'friday': '08:00-17:00',
          'saturday': '08:00-16:00',
          'sunday': 'closed',
        },
        commissionRates: {
          'cash_in': 0.013,
          'cash_out': 0.018,
          'money_transfer': 0.025,
          'forex_exchange': 0.03,
        },
        cashLimit: 25000.0,
        status: 'active',
        bozLicense: 'BOZ/FA/2024/006',
        lastUpdated: DateTime.now(),
      ),
    ];
  }
}
