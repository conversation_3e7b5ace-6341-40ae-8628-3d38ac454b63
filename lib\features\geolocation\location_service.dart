/// Location Service for Geolocation and Agent Finding
/// Handles GPS location, permissions, and distance calculations
/// Optimized for Zambian geographic conditions

import 'package:logger/logger.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  static final Logger _logger = Logger();
  Position? _lastKnownPosition;
  DateTime? _lastLocationUpdate;

  // Zambian geographic bounds for validation
  static const double _zambiaMinLat = -18.0;
  static const double _zambiaMaxLat = -8.0;
  static const double _zambiaMinLng = 22.0;
  static const double _zambiaMaxLng = 34.0;

  /// Get current user location
  Future<Position?> getCurrentLocation() async {
    try {
      _logger.i('📍 Getting current location');

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _logger.w('⚠️ Location services are disabled');
        return _lastKnownPosition;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _logger.w('⚠️ Location permissions denied');
          return _lastKnownPosition;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _logger.e('❌ Location permissions permanently denied');
        return _lastKnownPosition;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 10),
      );

      // Validate position is within Zambia
      if (_isValidZambianLocation(position)) {
        _lastKnownPosition = position;
        _lastLocationUpdate = DateTime.now();
        _logger.i('✅ Location obtained: ${position.latitude}, ${position.longitude}');
        return position;
      } else {
        _logger.w('⚠️ Location outside Zambia bounds');
        return _lastKnownPosition;
      }
    } catch (e) {
      _logger.e('❌ Failed to get location: $e');
      return _lastKnownPosition;
    }
  }

  /// Get last known location
  Position? getLastKnownLocation() {
    return _lastKnownPosition;
  }

  /// Check if location is within Zambian bounds
  bool _isValidZambianLocation(Position position) {
    return position.latitude >= _zambiaMinLat &&
           position.latitude <= _zambiaMaxLat &&
           position.longitude >= _zambiaMinLng &&
           position.longitude <= _zambiaMaxLng;
  }

  /// Calculate distance between two points in kilometers
  double calculateDistance(
    double startLat,
    double startLng,
    double endLat,
    double endLng,
  ) {
    return Geolocator.distanceBetween(startLat, startLng, endLat, endLng) / 1000;
  }

  /// Check if location update is needed
  bool get needsLocationUpdate {
    if (_lastLocationUpdate == null) return true;
    return DateTime.now().difference(_lastLocationUpdate!).inMinutes > 5;
  }
}
