import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/app_constants.dart';
import '../../data/database/database_helper.dart';
import '../mobile_money/data/services/mobile_money_service.dart';

/// Chilimba Service for Informal Market Support
/// Traditional Zambian rotating savings and credit associations (ROSCAs)
/// with community guarantors and digital integration
class ChilimbaService {
  static final ChilimbaService _instance = ChilimbaService._internal();
  factory ChilimbaService() => _instance;
  ChilimbaService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final MobileMoneyService _mobileMoneyService = MobileMoneyService();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  /// Create a new Chilimba group
  Future<String> createChilimbaGroup({
    required String creatorUserId,
    required String groupName,
    required double contributionAmount,
    required int totalMembers,
    required int payoutFrequencyDays,
    required List<String> guarantorUserIds,
    String? description,
  }) async {
    try {
      final groupId = _uuid.v4();
      final now = DateTime.now().millisecondsSinceEpoch;
      
      // Validate guarantors
      if (guarantorUserIds.length < 2) {
        throw Exception('Chilimba requires at least 2 community guarantors');
      }

      // Create Chilimba group
      final chilimbaGroup = {
        'id': groupId,
        'creator_user_id': creatorUserId,
        'group_name': groupName,
        'description': description ?? '',
        'contribution_amount': contributionAmount,
        'total_members': totalMembers,
        'current_members': 1, // Creator is first member
        'payout_frequency_days': payoutFrequencyDays,
        'total_pool': contributionAmount * totalMembers,
        'current_round': 0,
        'status': 'RECRUITING',
        'created_at': now,
        'next_payout_date': now + (payoutFrequencyDays * 24 * 60 * 60 * 1000),
        'guarantor_user_ids': jsonEncode(guarantorUserIds),
      };

      await _dbHelper.insert('chilimba_groups', chilimbaGroup);

      // Add creator as first member
      await _addChilimbaMember(groupId, creatorUserId, 1);

      // Notify guarantors
      await _notifyGuarantors(groupId, guarantorUserIds);

      _logger.i('Chilimba group created: $groupName ($groupId)');
      return groupId;
      
    } catch (e) {
      _logger.e('Failed to create Chilimba group: $e');
      rethrow;
    }
  }

  /// Join a Chilimba group
  Future<bool> joinChilimbaGroup({
    required String groupId,
    required String userId,
    required String guarantorUserId,
  }) async {
    try {
      // Get group details
      final groups = await _dbHelper.query(
        'chilimba_groups',
        where: 'id = ?',
        whereArgs: [groupId],
        limit: 1,
      );

      if (groups.isEmpty) {
        throw Exception('Chilimba group not found');
      }

      final group = groups.first;
      
      // Check if group is still recruiting
      if (group['status'] != 'RECRUITING') {
        throw Exception('Chilimba group is not accepting new members');
      }

      // Check if group is full
      final currentMembers = group['current_members'] as int;
      final totalMembers = group['total_members'] as int;
      
      if (currentMembers >= totalMembers) {
        throw Exception('Chilimba group is full');
      }

      // Verify guarantor is approved for this group
      final guarantorIds = jsonDecode(group['guarantor_user_ids']) as List;
      if (!guarantorIds.contains(guarantorUserId)) {
        throw Exception('Invalid guarantor for this Chilimba group');
      }

      // Add member
      final memberPosition = currentMembers + 1;
      await _addChilimbaMember(groupId, userId, memberPosition);

      // Update group member count
      await _dbHelper.update(
        'chilimba_groups',
        {'current_members': memberPosition},
        where: 'id = ?',
        whereArgs: [groupId],
      );

      // If group is now full, activate it
      if (memberPosition >= totalMembers) {
        await _activateChilimbaGroup(groupId);
      }

      _logger.i('User $userId joined Chilimba group $groupId');
      return true;
      
    } catch (e) {
      _logger.e('Failed to join Chilimba group: $e');
      return false;
    }
  }

  /// Make Chilimba contribution
  Future<Map<String, dynamic>> makeContribution({
    required String groupId,
    required String userId,
    required double amount,
    required String paymentMethod,
  }) async {
    try {
      // Get group and member details
      final group = await _getChilimbaGroup(groupId);
      final member = await _getChilimbaMember(groupId, userId);

      if (group == null || member == null) {
        throw Exception('Invalid Chilimba group or member');
      }

      // Verify contribution amount
      final expectedAmount = group['contribution_amount'] as double;
      if (amount != expectedAmount) {
        throw Exception('Contribution amount must be K${expectedAmount.toStringAsFixed(2)}');
      }

      // Check if already contributed for current round
      final currentRound = group['current_round'] as int;
      final existingContributions = await _dbHelper.query(
        'chilimba_contributions',
        where: 'group_id = ? AND user_id = ? AND round_number = ?',
        whereArgs: [groupId, userId, currentRound],
      );

      if (existingContributions.isNotEmpty) {
        throw Exception('Already contributed for round $currentRound');
      }

      // Process payment
      final transactionId = _uuid.v4();
      final contribution = {
        'id': _uuid.v4(),
        'group_id': groupId,
        'user_id': userId,
        'round_number': currentRound,
        'amount': amount,
        'payment_method': paymentMethod,
        'transaction_id': transactionId,
        'status': 'COMPLETED',
        'contributed_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('chilimba_contributions', contribution);

      // Check if round is complete
      await _checkRoundCompletion(groupId);

      _logger.i('Chilimba contribution made: $userId to $groupId, K${amount.toStringAsFixed(2)}');
      
      return {
        'transaction_id': transactionId,
        'amount': amount,
        'round': currentRound,
        'status': 'COMPLETED',
      };
      
    } catch (e) {
      _logger.e('Chilimba contribution failed: $e');
      rethrow;
    }
  }

  /// Process Chilimba payout
  Future<Map<String, dynamic>> processChilimbaPayout(String groupId) async {
    try {
      final group = await _getChilimbaGroup(groupId);
      if (group == null) {
        throw Exception('Chilimba group not found');
      }

      final currentRound = group['current_round'] as int;
      final totalPool = group['total_pool'] as double;

      // Get recipient for this round (round-robin)
      final recipient = await _getRoundRecipient(groupId, currentRound);
      if (recipient == null) {
        throw Exception('No recipient found for round $currentRound');
      }

      // Create payout record
      final payoutId = _uuid.v4();
      final payout = {
        'id': payoutId,
        'group_id': groupId,
        'recipient_user_id': recipient['user_id'],
        'round_number': currentRound,
        'amount': totalPool,
        'status': 'COMPLETED',
        'paid_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('chilimba_payouts', payout);

      // Start next round
      await _startNextRound(groupId);

      _logger.i('Chilimba payout processed: K${totalPool.toStringAsFixed(2)} to ${recipient['user_id']}');
      
      return {
        'payout_id': payoutId,
        'recipient': recipient['user_id'],
        'amount': totalPool,
        'round': currentRound,
      };
      
    } catch (e) {
      _logger.e('Chilimba payout failed: $e');
      rethrow;
    }
  }

  /// Add member to Chilimba group
  Future<void> _addChilimbaMember(String groupId, String userId, int position) async {
    final member = {
      'id': _uuid.v4(),
      'group_id': groupId,
      'user_id': userId,
      'position': position,
      'joined_at': DateTime.now().millisecondsSinceEpoch,
      'status': 'ACTIVE',
      'total_contributions': 0.0,
      'total_received': 0.0,
    };

    await _dbHelper.insert('chilimba_members', member);
  }

  /// Activate Chilimba group when full
  Future<void> _activateChilimbaGroup(String groupId) async {
    await _dbHelper.update(
      'chilimba_groups',
      {
        'status': 'ACTIVE',
        'current_round': 1,
        'activated_at': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [groupId],
    );

    _logger.i('Chilimba group activated: $groupId');
  }

  /// Check if contribution round is complete
  Future<void> _checkRoundCompletion(String groupId) async {
    final group = await _getChilimbaGroup(groupId);
    if (group == null) return;

    final currentRound = group['current_round'] as int;
    final totalMembers = group['total_members'] as int;

    // Count contributions for current round
    final contributions = await _dbHelper.query(
      'chilimba_contributions',
      where: 'group_id = ? AND round_number = ?',
      whereArgs: [groupId, currentRound],
    );

    // If all members have contributed, process payout
    if (contributions.length >= totalMembers) {
      await processChilimbaPayout(groupId);
    }
  }

  /// Get round recipient (round-robin system)
  Future<Map<String, dynamic>?> _getRoundRecipient(String groupId, int round) async {
    final members = await _dbHelper.query(
      'chilimba_members',
      where: 'group_id = ? AND status = ?',
      whereArgs: [groupId, 'ACTIVE'],
      orderBy: 'position ASC',
    );

    if (members.isEmpty) return null;

    // Round-robin: recipient position = (round - 1) % total_members
    final recipientIndex = (round - 1) % members.length;
    return members[recipientIndex];
  }

  /// Start next round
  Future<void> _startNextRound(String groupId) async {
    final group = await _getChilimbaGroup(groupId);
    if (group == null) return;

    final currentRound = group['current_round'] as int;
    final totalMembers = group['total_members'] as int;
    final payoutFrequencyDays = group['payout_frequency_days'] as int;

    // Check if all members have received payout
    if (currentRound >= totalMembers) {
      // Complete the Chilimba cycle
      await _dbHelper.update(
        'chilimba_groups',
        {'status': 'COMPLETED'},
        where: 'id = ?',
        whereArgs: [groupId],
      );
      _logger.i('Chilimba cycle completed: $groupId');
    } else {
      // Start next round
      final nextRound = currentRound + 1;
      final nextPayoutDate = DateTime.now()
          .add(Duration(days: payoutFrequencyDays))
          .millisecondsSinceEpoch;

      await _dbHelper.update(
        'chilimba_groups',
        {
          'current_round': nextRound,
          'next_payout_date': nextPayoutDate,
        },
        where: 'id = ?',
        whereArgs: [groupId],
      );
      
      _logger.i('Chilimba round $nextRound started for group $groupId');
    }
  }

  /// Notify guarantors about new group
  Future<void> _notifyGuarantors(String groupId, List<String> guarantorIds) async {
    // In production, send notifications to guarantors
    for (final guarantorId in guarantorIds) {
      _logger.i('Notifying guarantor $guarantorId about Chilimba group $groupId');
    }
  }

  /// Get Chilimba group details
  Future<Map<String, dynamic>?> _getChilimbaGroup(String groupId) async {
    final groups = await _dbHelper.query(
      'chilimba_groups',
      where: 'id = ?',
      whereArgs: [groupId],
      limit: 1,
    );
    
    return groups.isNotEmpty ? groups.first : null;
  }

  /// Get Chilimba member details
  Future<Map<String, dynamic>?> _getChilimbaMember(String groupId, String userId) async {
    final members = await _dbHelper.query(
      'chilimba_members',
      where: 'group_id = ? AND user_id = ?',
      whereArgs: [groupId, userId],
      limit: 1,
    );
    
    return members.isNotEmpty ? members.first : null;
  }

  /// Get user's Chilimba groups
  Future<List<Map<String, dynamic>>> getUserChilimbaGroups(String userId) async {
    final memberGroups = await _dbHelper.query(
      'chilimba_members',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    final groups = <Map<String, dynamic>>[];
    
    for (final member in memberGroups) {
      final group = await _getChilimbaGroup(member['group_id']);
      if (group != null) {
        group['member_position'] = member['position'];
        group['member_status'] = member['status'];
        groups.add(group);
      }
    }

    return groups;
  }

  /// Get Chilimba statistics
  Future<Map<String, dynamic>> getChilimbaStatistics() async {
    try {
      final allGroups = await _dbHelper.query('chilimba_groups');
      final activeGroups = allGroups.where((g) => g['status'] == 'ACTIVE').length;
      final completedGroups = allGroups.where((g) => g['status'] == 'COMPLETED').length;
      
      final allContributions = await _dbHelper.query('chilimba_contributions');
      final totalContributions = allContributions.fold(0.0, 
          (sum, c) => sum + (c['amount'] as num).toDouble());

      return {
        'total_groups': allGroups.length,
        'active_groups': activeGroups,
        'completed_groups': completedGroups,
        'total_contributions': totalContributions,
        'average_group_size': allGroups.isNotEmpty 
            ? allGroups.fold(0, (sum, g) => sum + (g['total_members'] as int)) / allGroups.length
            : 0,
      };
    } catch (e) {
      _logger.e('Failed to get Chilimba statistics: $e');
      return {};
    }
  }
}
