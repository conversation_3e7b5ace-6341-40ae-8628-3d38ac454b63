import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transaction_model.g.dart';

/// Transaction model for mobile money operations
/// Supports MTN, Airtel, and Zamtel providers with Zambian formatting
@JsonSerializable()
class TransactionModel extends Equatable {
  final String id;
  final String userId;
  final String transactionType;
  final double amount;
  final String currency;
  final double fee;
  final double totalAmount;
  final String? senderPhone;
  final String? receiverPhone;
  final String provider;
  final String status;
  final String? referenceNumber;
  final String? description;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? completedAt;
  final String? errorMessage;
  final int retryCount;

  const TransactionModel({
    required this.id,
    required this.userId,
    required this.transactionType,
    required this.amount,
    this.currency = 'ZMW',
    this.fee = 0.0,
    required this.totalAmount,
    this.senderPhone,
    this.receiverPhone,
    required this.provider,
    required this.status,
    this.referenceNumber,
    this.description,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.completedAt,
    this.errorMessage,
    this.retryCount = 0,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) =>
      _$TransactionModelFromJson(json);

  Map<String, dynamic> toJson() => _$TransactionModelToJson(this);

  factory TransactionModel.fromDatabase(Map<String, dynamic> map) {
    return TransactionModel(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      transactionType: map['transaction_type'] as String,
      amount: (map['amount'] as num).toDouble(),
      currency: map['currency'] as String? ?? 'ZMW',
      fee: (map['fee'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (map['total_amount'] as num).toDouble(),
      senderPhone: map['sender_phone'] as String?,
      receiverPhone: map['receiver_phone'] as String?,
      provider: map['provider'] as String,
      status: map['status'] as String,
      referenceNumber: map['reference_number'] as String?,
      description: map['description'] as String?,
      metadata: map['metadata'] != null 
          ? Map<String, dynamic>.from(map['metadata'] as Map)
          : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      completedAt: map['completed_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['completed_at'] as int)
          : null,
      errorMessage: map['error_message'] as String?,
      retryCount: map['retry_count'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'user_id': userId,
      'transaction_type': transactionType,
      'amount': amount,
      'currency': currency,
      'fee': fee,
      'total_amount': totalAmount,
      'sender_phone': senderPhone,
      'receiver_phone': receiverPhone,
      'provider': provider,
      'status': status,
      'reference_number': referenceNumber,
      'description': description,
      'metadata': metadata,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'completed_at': completedAt?.millisecondsSinceEpoch,
      'error_message': errorMessage,
      'retry_count': retryCount,
    };
  }

  TransactionModel copyWith({
    String? id,
    String? userId,
    String? transactionType,
    double? amount,
    String? currency,
    double? fee,
    double? totalAmount,
    String? senderPhone,
    String? receiverPhone,
    String? provider,
    String? status,
    String? referenceNumber,
    String? description,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
    String? errorMessage,
    int? retryCount,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      transactionType: transactionType ?? this.transactionType,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      fee: fee ?? this.fee,
      totalAmount: totalAmount ?? this.totalAmount,
      senderPhone: senderPhone ?? this.senderPhone,
      receiverPhone: receiverPhone ?? this.receiverPhone,
      provider: provider ?? this.provider,
      status: status ?? this.status,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
      errorMessage: errorMessage ?? this.errorMessage,
      retryCount: retryCount ?? this.retryCount,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        transactionType,
        amount,
        currency,
        fee,
        totalAmount,
        senderPhone,
        receiverPhone,
        provider,
        status,
        referenceNumber,
        description,
        metadata,
        createdAt,
        updatedAt,
        completedAt,
        errorMessage,
        retryCount,
      ];

  @override
  String toString() {
    return 'TransactionModel(id: $id, type: $transactionType, amount: $amount $currency, status: $status, provider: $provider)';
  }
}

/// Mobile Money Provider model
@JsonSerializable()
class MobileMoneyProvider extends Equatable {
  final String code;
  final String name;
  final String operatorCode;
  final String countryCode;
  final bool isActive;
  final double transactionFee;
  final double maxAmount;
  final double minAmount;
  final List<String> supportedServices;

  const MobileMoneyProvider({
    required this.code,
    required this.name,
    required this.operatorCode,
    required this.countryCode,
    this.isActive = true,
    this.transactionFee = 0.0021,
    this.maxAmount = 50000.0,
    this.minAmount = 1.0,
    this.supportedServices = const [],
  });

  factory MobileMoneyProvider.fromJson(Map<String, dynamic> json) =>
      _$MobileMoneyProviderFromJson(json);

  Map<String, dynamic> toJson() => _$MobileMoneyProviderToJson(this);

  @override
  List<Object?> get props => [
        code,
        name,
        operatorCode,
        countryCode,
        isActive,
        transactionFee,
        maxAmount,
        minAmount,
        supportedServices,
      ];
}

/// Transaction request model for API calls
@JsonSerializable()
class TransactionRequest extends Equatable {
  final String amount;
  final String currency;
  final String externalId;
  final String payerPartyId;
  final String payerPartyIdType;
  final String payeePartyId;
  final String payeePartyIdType;
  final String payerMessage;
  final String payeeNote;

  const TransactionRequest({
    required this.amount,
    this.currency = 'ZMW',
    required this.externalId,
    required this.payerPartyId,
    this.payerPartyIdType = 'MSISDN',
    required this.payeePartyId,
    this.payeePartyIdType = 'MSISDN',
    this.payerMessage = '',
    this.payeeNote = '',
  });

  factory TransactionRequest.fromJson(Map<String, dynamic> json) =>
      _$TransactionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$TransactionRequestToJson(this);

  @override
  List<Object?> get props => [
        amount,
        currency,
        externalId,
        payerPartyId,
        payerPartyIdType,
        payeePartyId,
        payeePartyIdType,
        payerMessage,
        payeeNote,
      ];
}

/// Transaction response model from API
@JsonSerializable()
class TransactionResponse extends Equatable {
  final String? financialTransactionId;
  final String? externalId;
  final String? amount;
  final String? currency;
  final String? status;
  final String? reason;
  final String? referenceIdToCancel;

  const TransactionResponse({
    this.financialTransactionId,
    this.externalId,
    this.amount,
    this.currency,
    this.status,
    this.reason,
    this.referenceIdToCancel,
  });

  factory TransactionResponse.fromJson(Map<String, dynamic> json) =>
      _$TransactionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TransactionResponseToJson(this);

  @override
  List<Object?> get props => [
        financialTransactionId,
        externalId,
        amount,
        currency,
        status,
        reason,
        referenceIdToCancel,
      ];
}
