// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TransactionModel _$TransactionModelFromJson(Map<String, dynamic> json) =>
    TransactionModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      transactionType: json['transactionType'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'ZMW',
      fee: (json['fee'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      senderPhone: json['senderPhone'] as String?,
      receiverPhone: json['receiverPhone'] as String?,
      provider: json['provider'] as String,
      status: json['status'] as String,
      referenceNumber: json['referenceNumber'] as String?,
      description: json['description'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      errorMessage: json['errorMessage'] as String?,
      retryCount: json['retryCount'] as int? ?? 0,
    );

Map<String, dynamic> _$TransactionModelToJson(TransactionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'transactionType': instance.transactionType,
      'amount': instance.amount,
      'currency': instance.currency,
      'fee': instance.fee,
      'totalAmount': instance.totalAmount,
      'senderPhone': instance.senderPhone,
      'receiverPhone': instance.receiverPhone,
      'provider': instance.provider,
      'status': instance.status,
      'referenceNumber': instance.referenceNumber,
      'description': instance.description,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'errorMessage': instance.errorMessage,
      'retryCount': instance.retryCount,
    };

MobileMoneyProvider _$MobileMoneyProviderFromJson(Map<String, dynamic> json) =>
    MobileMoneyProvider(
      code: json['code'] as String,
      name: json['name'] as String,
      operatorCode: json['operatorCode'] as String,
      countryCode: json['countryCode'] as String,
      isActive: json['isActive'] as bool? ?? true,
      transactionFee: (json['transactionFee'] as num?)?.toDouble() ?? 0.0021,
      maxAmount: (json['maxAmount'] as num?)?.toDouble() ?? 50000.0,
      minAmount: (json['minAmount'] as num?)?.toDouble() ?? 1.0,
      supportedServices: (json['supportedServices'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$MobileMoneyProviderToJson(
        MobileMoneyProvider instance) =>
    <String, dynamic>{
      'code': instance.code,
      'name': instance.name,
      'operatorCode': instance.operatorCode,
      'countryCode': instance.countryCode,
      'isActive': instance.isActive,
      'transactionFee': instance.transactionFee,
      'maxAmount': instance.maxAmount,
      'minAmount': instance.minAmount,
      'supportedServices': instance.supportedServices,
    };

TransactionRequest _$TransactionRequestFromJson(Map<String, dynamic> json) =>
    TransactionRequest(
      amount: json['amount'] as String,
      currency: json['currency'] as String? ?? 'ZMW',
      externalId: json['externalId'] as String,
      payerPartyId: json['payerPartyId'] as String,
      payerPartyIdType: json['payerPartyIdType'] as String? ?? 'MSISDN',
      payeePartyId: json['payeePartyId'] as String,
      payeePartyIdType: json['payeePartyIdType'] as String? ?? 'MSISDN',
      payerMessage: json['payerMessage'] as String? ?? '',
      payeeNote: json['payeeNote'] as String? ?? '',
    );

Map<String, dynamic> _$TransactionRequestToJson(TransactionRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'currency': instance.currency,
      'externalId': instance.externalId,
      'payerPartyId': instance.payerPartyId,
      'payerPartyIdType': instance.payerPartyIdType,
      'payeePartyId': instance.payeePartyId,
      'payeePartyIdType': instance.payeePartyIdType,
      'payerMessage': instance.payerMessage,
      'payeeNote': instance.payeeNote,
    };

TransactionResponse _$TransactionResponseFromJson(Map<String, dynamic> json) =>
    TransactionResponse(
      financialTransactionId: json['financialTransactionId'] as String?,
      externalId: json['externalId'] as String?,
      amount: json['amount'] as String?,
      currency: json['currency'] as String?,
      status: json['status'] as String?,
      reason: json['reason'] as String?,
      referenceIdToCancel: json['referenceIdToCancel'] as String?,
    );

Map<String, dynamic> _$TransactionResponseToJson(
        TransactionResponse instance) =>
    <String, dynamic>{
      'financialTransactionId': instance.financialTransactionId,
      'externalId': instance.externalId,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': instance.status,
      'reason': instance.reason,
      'referenceIdToCancel': instance.referenceIdToCancel,
    };
