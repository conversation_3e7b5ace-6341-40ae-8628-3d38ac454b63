import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';
import 'package:logger/logger.dart';
import 'package:crypto/crypto.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/constants/app_constants.dart';
import '../models/transaction_model.dart';

/// Airtel Money API service for Zambia
/// Implements Airtel Money API v2.0 with proper Zambian phone number formatting
class AirtelApiService {
  static final AirtelApiService _instance = AirtelApiService._internal();
  factory AirtelApiService() => _instance;
  AirtelApiService._internal();

  late final Dio _dio;
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  // Airtel API credentials
  String? _accessToken;
  String? _clientId;
  String? _clientSecret;
  DateTime? _tokenExpiry;

  void initialize({
    required String clientId,
    required String clientSecret,
  }) {
    _clientId = clientId;
    _clientSecret = clientSecret;
    
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.airtelConfig['baseUrl'] as String,
      connectTimeout: Duration(milliseconds: AppConstants.connectionTimeoutMs),
      receiveTimeout: Duration(milliseconds: AppConstants.receiveTimeoutMs),
      sendTimeout: Duration(milliseconds: AppConstants.sendTimeoutMs),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Country': 'ZM',
        'X-Currency': 'ZMW',
      },
    ));

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add authorization header if token exists
        if (_accessToken != null && _isTokenValid()) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        
        // Add unique transaction ID
        options.headers['X-Reference-Id'] = _uuid.v4();
        
        _logger.d('Airtel API Request: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        _logger.d('Airtel API Response: ${response.statusCode} ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) {
        _logger.e('Airtel API Error: ${error.response?.statusCode} ${error.message}');
        handler.next(error);
      },
    ));
  }

  /// Authenticate with Airtel API and get access token
  Future<bool> authenticate() async {
    try {
      final response = await _dio.post('/auth/oauth2/token', data: {
        'client_id': _clientId,
        'client_secret': _clientSecret,
        'grant_type': 'client_credentials',
      });

      if (response.statusCode == 200) {
        _accessToken = response.data['access_token'];
        final expiresIn = response.data['expires_in'] as int;
        _tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn - 60));
        
        _logger.i('Airtel authentication successful');
        return true;
      }
    } catch (e) {
      _logger.e('Airtel authentication failed: $e');
    }
    
    return false;
  }

  bool _isTokenValid() {
    return _tokenExpiry != null && DateTime.now().isBefore(_tokenExpiry!);
  }

  /// Format Zambian phone number for Airtel API
  String _formatZambianPhone(String phoneNumber) {
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleaned.startsWith('260')) {
      return cleaned;
    } else if (cleaned.startsWith('0')) {
      return '260${cleaned.substring(1)}';
    } else if (cleaned.length == 9) {
      return '260$cleaned';
    }
    
    return cleaned;
  }

  /// Validate if phone number is Airtel Zambia
  bool _isAirtelNumber(String phoneNumber) {
    final formatted = _formatZambianPhone(phoneNumber);
    // Airtel Zambia numbers start with 26097
    return formatted.startsWith('26097');
  }

  /// Send money to another Airtel user
  Future<TransactionResponse> sendMoney({
    required String senderPhone,
    required String receiverPhone,
    required double amount,
    required String externalId,
    String? message,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    final formattedSender = _formatZambianPhone(senderPhone);
    final formattedReceiver = _formatZambianPhone(receiverPhone);

    if (!_isAirtelNumber(formattedReceiver)) {
      throw Exception('Receiver is not an Airtel Zambia number');
    }

    final requestData = {
      'reference': externalId,
      'subscriber': {
        'country': 'ZM',
        'currency': 'ZMW',
        'msisdn': formattedReceiver,
      },
      'transaction': {
        'amount': amount.toStringAsFixed(2),
        'country': 'ZM',
        'currency': 'ZMW',
        'id': externalId,
      },
    };

    try {
      final response = await _dio.post(
        '/standard/v1/payments/',
        data: requestData,
        options: Options(
          headers: {
            'X-Reference-Id': externalId,
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return TransactionResponse(
          financialTransactionId: response.data['transaction']?['id'],
          externalId: externalId,
          amount: amount.toStringAsFixed(2),
          currency: 'ZMW',
          status: response.data['status']?['code'] == '200' ? 'SUCCESSFUL' : 'PENDING',
        );
      } else {
        throw Exception('Transaction failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Airtel send money failed: $e');
      rethrow;
    }
  }

  /// Request payment from Airtel user
  Future<TransactionResponse> requestPayment({
    required String payerPhone,
    required String payeePhone,
    required double amount,
    required String externalId,
    String? message,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    final formattedPayer = _formatZambianPhone(payerPhone);

    if (!_isAirtelNumber(formattedPayer)) {
      throw Exception('Payer is not an Airtel Zambia number');
    }

    final requestData = {
      'reference': externalId,
      'subscriber': {
        'country': 'ZM',
        'currency': 'ZMW',
        'msisdn': formattedPayer,
      },
      'transaction': {
        'amount': amount.toStringAsFixed(2),
        'country': 'ZM',
        'currency': 'ZMW',
        'id': externalId,
      },
    };

    try {
      final response = await _dio.post(
        '/merchant/v1/payments/',
        data: requestData,
        options: Options(
          headers: {
            'X-Reference-Id': externalId,
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return TransactionResponse(
          financialTransactionId: response.data['transaction']?['id'],
          externalId: externalId,
          amount: amount.toStringAsFixed(2),
          currency: 'ZMW',
          status: response.data['status']?['code'] == '200' ? 'SUCCESSFUL' : 'PENDING',
        );
      } else {
        throw Exception('Payment request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Airtel request payment failed: $e');
      rethrow;
    }
  }

  /// Check transaction status
  Future<TransactionResponse> checkTransactionStatus(String transactionId) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    try {
      final response = await _dio.get('/standard/v1/payments/$transactionId');
      
      return TransactionResponse(
        financialTransactionId: response.data['transaction']?['id'],
        externalId: transactionId,
        amount: response.data['transaction']?['amount'],
        currency: response.data['transaction']?['currency'],
        status: response.data['status']?['code'] == '200' ? 'SUCCESSFUL' : 'PENDING',
      );
    } catch (e) {
      _logger.e('Airtel status check failed: $e');
      rethrow;
    }
  }

  /// Get account balance
  Future<double> getBalance() async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    try {
      final response = await _dio.get('/standard/v1/users/balance');
      
      if (response.statusCode == 200) {
        final balance = response.data['balance'] as String;
        return double.parse(balance);
      } else {
        throw Exception('Failed to get balance');
      }
    } catch (e) {
      _logger.e('Airtel get balance failed: $e');
      rethrow;
    }
  }

  /// Calculate transaction fee
  double calculateFee(double amount) {
    final feeRate = AppConfig.airtelConfig['txFee'] as double;
    return amount * feeRate;
  }

  /// Validate transaction limits
  bool validateAmount(double amount) {
    final minAmount = AppConfig.airtelConfig['minTransactionAmount'] as double;
    final maxAmount = AppConfig.airtelConfig['maxTransactionAmount'] as double;
    
    return amount >= minAmount && amount <= maxAmount;
  }

  /// Ensure authentication is valid
  Future<bool> _ensureAuthenticated() async {
    if (_accessToken == null || !_isTokenValid()) {
      return await authenticate();
    }
    return true;
  }

  /// Get provider information
  MobileMoneyProvider getProviderInfo() {
    return MobileMoneyProvider(
      code: AppConstants.providerAirtel,
      name: 'Airtel Money',
      operatorCode: AppConfig.airtelConfig['operatorCode'] as String,
      countryCode: AppConfig.airtelConfig['countryCode'] as String,
      transactionFee: AppConfig.airtelConfig['txFee'] as double,
      maxAmount: AppConfig.airtelConfig['maxTransactionAmount'] as double,
      minAmount: AppConfig.airtelConfig['minTransactionAmount'] as double,
      supportedServices: [
        'SEND_MONEY',
        'REQUEST_PAYMENT',
        'BILL_PAYMENT',
        'AIRTIME_PURCHASE',
      ],
    );
  }
}
