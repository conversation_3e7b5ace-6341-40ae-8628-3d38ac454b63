import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/constants/app_constants.dart';
import '../models/transaction_model.dart';
import 'mtn_api_service.dart';
import 'airtel_api_service.dart';
import 'zamtel_api_service.dart';
import '../../../offline_sync/data/offline_sync_manager.dart';
import 'offline_storage.dart';

/// Unified mobile money service that manages all providers
/// Implements automatic provider detection and fallback mechanisms
class MobileMoneyService {
  static final MobileMoneyService _instance = MobileMoneyService._internal();
  factory MobileMoneyService() => _instance;
  MobileMoneyService._internal();

  final MTNApiService _mtnService = MTNApiService();
  final AirtelApiService _airtelService = AirtelApiService();
  final ZamtelApiService _zamtelService = ZamtelApiService();
  final OfflineSyncManager _syncManager = OfflineSyncManager();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  bool _isInitialized = false;

  /// Initialize all mobile money providers
  Future<void> initialize({
    required Map<String, String> mtnCredentials,
    required Map<String, String> airtelCredentials,
    required Map<String, String> zamtelCredentials,
  }) async {
    try {
      // Initialize MTN
      _mtnService.initialize(
        apiKey: mtnCredentials['apiKey']!,
        subscriptionKey: mtnCredentials['subscriptionKey']!,
      );

      // Initialize Airtel
      _airtelService.initialize(
        clientId: airtelCredentials['clientId']!,
        clientSecret: airtelCredentials['clientSecret']!,
      );

      // Initialize Zamtel
      _zamtelService.initialize(
        apiKey: zamtelCredentials['apiKey']!,
        merchantId: zamtelCredentials['merchantId']!,
      );

      // Authenticate with all providers
      await Future.wait([
        _mtnService.authenticate(),
        _airtelService.authenticate(),
        _zamtelService.authenticate(),
      ]);

      _isInitialized = true;
      _logger.i('Mobile money service initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize mobile money service: $e');
      rethrow;
    }
  }

  /// Detect mobile money provider from phone number
  String detectProvider(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    String formatted;
    
    if (cleaned.startsWith('260')) {
      formatted = cleaned;
    } else if (cleaned.startsWith('0')) {
      formatted = '260${cleaned.substring(1)}';
    } else if (cleaned.length == 9) {
      formatted = '260$cleaned';
    } else {
      formatted = cleaned;
    }

    // Detect provider based on number prefix
    if (formatted.startsWith('26096')) {
      return AppConstants.providerMTN;
    } else if (formatted.startsWith('26097')) {
      return AppConstants.providerAirtel;
    } else if (formatted.startsWith('26095')) {
      return AppConstants.providerZamtel;
    }

    // Default to MTN if unknown
    return AppConstants.providerMTN;
  }

  /// Send money with automatic provider detection and fallback
  Future<TransactionModel> sendMoney({
    required String userId,
    required String senderPhone,
    required String receiverPhone,
    required double amount,
    String? description,
    bool useOfflineQueue = true,
  }) async {
    if (!_isInitialized) {
      throw Exception('Mobile money service not initialized');
    }

    final transactionId = _uuid.v4();
    final now = DateTime.now();
    final provider = detectProvider(receiverPhone);
    final fee = _calculateFee(provider, amount);
    final totalAmount = amount + fee;

    // Create transaction model
    final transaction = TransactionModel(
      id: transactionId,
      userId: userId,
      transactionType: AppConstants.transactionTypeSend,
      amount: amount,
      fee: fee,
      totalAmount: totalAmount,
      senderPhone: senderPhone,
      receiverPhone: receiverPhone,
      provider: provider,
      status: AppConstants.statusPending,
      description: description,
      createdAt: now,
      updatedAt: now,
    );

    try {
      // Check if online and attempt immediate processing
      if (await _syncManager.isConnected()) {
        final result = await _processTransaction(transaction);
        return result;
      } else if (useOfflineQueue) {
        // Queue for offline processing
        await _syncManager.queueTransaction(
          userId: userId,
          transactionData: transaction.toJson(),
          transactionType: AppConstants.transactionTypeSend,
        );
        
        return transaction.copyWith(status: AppConstants.statusQueued);
      } else {
        throw Exception('No internet connection and offline mode disabled');
      }
    } catch (e) {
      _logger.e('Send money failed: $e');
      
      if (useOfflineQueue) {
        // Queue for retry
        await _syncManager.queueTransaction(
          userId: userId,
          transactionData: transaction.toJson(),
          transactionType: AppConstants.transactionTypeSend,
        );
        
        return transaction.copyWith(
          status: AppConstants.statusQueued,
          errorMessage: e.toString(),
        );
      } else {
        rethrow;
      }
    }
  }

  /// Process transaction with provider fallback
  Future<TransactionModel> _processTransaction(TransactionModel transaction) async {
    final providers = _getProviderFallbackOrder(transaction.provider);
    
    for (final provider in providers) {
      try {
        final result = await _executeWithProvider(provider, transaction);
        return transaction.copyWith(
          status: AppConstants.statusCompleted,
          provider: provider,
          referenceNumber: result.financialTransactionId,
          completedAt: DateTime.now(),
        );
      } catch (e) {
        _logger.w('Provider $provider failed: $e');
        
        // If this is the last provider, throw the error
        if (provider == providers.last) {
          rethrow;
        }
        
        // Continue to next provider
        continue;
      }
    }
    
    throw Exception('All providers failed');
  }

  /// Execute transaction with specific provider
  Future<TransactionResponse> _executeWithProvider(
    String provider,
    TransactionModel transaction,
  ) async {
    switch (provider) {
      case AppConstants.providerMTN:
        return await _mtnService.sendMoney(
          senderPhone: transaction.senderPhone!,
          receiverPhone: transaction.receiverPhone!,
          amount: transaction.amount,
          externalId: transaction.id,
          message: transaction.description,
        );
      
      case AppConstants.providerAirtel:
        return await _airtelService.sendMoney(
          senderPhone: transaction.senderPhone!,
          receiverPhone: transaction.receiverPhone!,
          amount: transaction.amount,
          externalId: transaction.id,
          message: transaction.description,
        );
      
      case AppConstants.providerZamtel:
        return await _zamtelService.sendMoney(
          senderPhone: transaction.senderPhone!,
          receiverPhone: transaction.receiverPhone!,
          amount: transaction.amount,
          externalId: transaction.id,
          message: transaction.description,
        );
      
      default:
        throw Exception('Unsupported provider: $provider');
    }
  }

  /// Get provider fallback order
  List<String> _getProviderFallbackOrder(String primaryProvider) {
    final fallbackOrder = List<String>.from(AppConfig.fallbackProviders);
    
    // Move primary provider to front
    fallbackOrder.remove(primaryProvider);
    fallbackOrder.insert(0, primaryProvider);
    
    return fallbackOrder;
  }

  /// Calculate transaction fee based on provider
  double _calculateFee(String provider, double amount) {
    switch (provider) {
      case AppConstants.providerMTN:
        return _mtnService.calculateFee(amount);
      case AppConstants.providerAirtel:
        return _airtelService.calculateFee(amount);
      case AppConstants.providerZamtel:
        return _zamtelService.calculateFee(amount);
      default:
        return amount * 0.0021; // Default fee rate
    }
  }

  /// Validate transaction amount for provider
  bool validateAmount(String provider, double amount) {
    switch (provider) {
      case AppConstants.providerMTN:
        return _mtnService.validateAmount(amount);
      case AppConstants.providerAirtel:
        return _airtelService.validateAmount(amount);
      case AppConstants.providerZamtel:
        return _zamtelService.validateAmount(amount);
      default:
        return amount >= AppConstants.minTransactionAmount && 
               amount <= AppConstants.maxTransactionAmount;
    }
  }

  /// Get all available providers
  List<MobileMoneyProvider> getAvailableProviders() {
    return [
      _mtnService.getProviderInfo(),
      _airtelService.getProviderInfo(),
      _zamtelService.getProviderInfo(),
    ];
  }

  /// Check transaction status
  Future<TransactionModel> checkTransactionStatus(
    String transactionId,
    String provider,
  ) async {
    try {
      TransactionResponse response;
      
      switch (provider) {
        case AppConstants.providerMTN:
          response = await _mtnService.checkTransactionStatus(transactionId);
          break;
        case AppConstants.providerAirtel:
          response = await _airtelService.checkTransactionStatus(transactionId);
          break;
        case AppConstants.providerZamtel:
          response = await _zamtelService.checkTransactionStatus(transactionId);
          break;
        default:
          throw Exception('Unsupported provider: $provider');
      }

      // Convert response to transaction model
      return TransactionModel(
        id: transactionId,
        userId: '', // Will be filled from database
        transactionType: AppConstants.transactionTypeSend,
        amount: double.parse(response.amount ?? '0'),
        totalAmount: double.parse(response.amount ?? '0'),
        provider: provider,
        status: response.status == 'SUCCESSFUL' 
            ? AppConstants.statusCompleted 
            : AppConstants.statusPending,
        referenceNumber: response.financialTransactionId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      _logger.e('Status check failed: $e');
      rethrow;
    }
  }

  /// Get balance from all providers
  Future<Map<String, double>> getAllBalances() async {
    final balances = <String, double>{};

    try {
      final results = await Future.wait([
        _mtnService.getBalance().catchError((_) => 0.0),
        _airtelService.getBalance().catchError((_) => 0.0),
        _zamtelService.getBalance().catchError((_) => 0.0),
      ]);

      balances[AppConstants.providerMTN] = results[0];
      balances[AppConstants.providerAirtel] = results[1];
      balances[AppConstants.providerZamtel] = results[2];
    } catch (e) {
      _logger.e('Failed to get balances: $e');
    }

    return balances;
  }

  /// Enhanced transaction verification with provider-specific validation
  /// Validates transaction status and ensures data integrity
  Future<bool> verifyTransactionWithProvider(String transactionId, String provider) async {
    try {
      _logger.i('Verifying transaction $transactionId with provider $provider');

      // Get transaction status from provider
      final transaction = await checkTransactionStatus(transactionId, provider);

      // Validate transaction data integrity
      final isValid = _validateTransactionData(transaction);
      if (!isValid) {
        _logger.w('Transaction data validation failed for $transactionId');
        return false;
      }

      // Check if transaction status is valid
      final validStatuses = [
        AppConstants.statusCompleted,
        AppConstants.statusPending,
        AppConstants.statusProcessing,
      ];

      final isStatusValid = validStatuses.contains(transaction.status);
      if (!isStatusValid) {
        _logger.w('Invalid transaction status for $transactionId: ${transaction.status}');
        return false;
      }

      // Additional provider-specific verification
      final providerVerified = await _performProviderSpecificVerification(transaction, provider);

      _logger.i('Transaction verification result for $transactionId: ${isStatusValid && providerVerified}');
      return isStatusValid && providerVerified;

    } catch (e) {
      _logger.e('Transaction verification failed for $transactionId: $e');
      return false;
    }
  }

  /// Validate transaction data integrity
  bool _validateTransactionData(TransactionModel transaction) {
    try {
      // Check required fields
      if (transaction.id.isEmpty ||
          transaction.userId.isEmpty ||
          transaction.amount <= 0 ||
          transaction.provider.isEmpty) {
        return false;
      }

      // Validate amount ranges
      if (transaction.amount < AppConstants.minTransactionAmount ||
          transaction.amount > AppConstants.maxTransactionAmount) {
        return false;
      }

      // Validate phone numbers if present
      if (transaction.senderPhone != null && !_isValidZambianPhone(transaction.senderPhone!)) {
        return false;
      }

      if (transaction.receiverPhone != null && !_isValidZambianPhone(transaction.receiverPhone!)) {
        return false;
      }

      // Validate provider
      final validProviders = [AppConstants.providerMTN, AppConstants.providerAirtel, AppConstants.providerZamtel];
      if (!validProviders.contains(transaction.provider)) {
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('Error validating transaction data: $e');
      return false;
    }
  }

  /// Perform provider-specific verification
  Future<bool> _performProviderSpecificVerification(TransactionModel transaction, String provider) async {
    try {
      switch (provider) {
        case AppConstants.providerMTN:
          return await _verifyMTNTransaction(transaction);
        case AppConstants.providerAirtel:
          return await _verifyAirtelTransaction(transaction);
        case AppConstants.providerZamtel:
          return await _verifyZamtelTransaction(transaction);
        default:
          _logger.w('Unknown provider for verification: $provider');
          return false;
      }
    } catch (e) {
      _logger.e('Provider-specific verification failed: $e');
      return false;
    }
  }

  /// Verify MTN transaction
  Future<bool> _verifyMTNTransaction(TransactionModel transaction) async {
    try {
      // MTN-specific verification logic
      if (transaction.referenceNumber == null || transaction.referenceNumber!.isEmpty) {
        return false;
      }

      // Verify MTN reference number format (example: MTN-specific format)
      final mtnRefPattern = RegExp(r'^MTN\d{10,}$');
      if (!mtnRefPattern.hasMatch(transaction.referenceNumber!)) {
        _logger.w('Invalid MTN reference number format: ${transaction.referenceNumber}');
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('MTN verification error: $e');
      return false;
    }
  }

  /// Verify Airtel transaction
  Future<bool> _verifyAirtelTransaction(TransactionModel transaction) async {
    try {
      // Airtel-specific verification logic
      if (transaction.referenceNumber == null || transaction.referenceNumber!.isEmpty) {
        return false;
      }

      // Verify Airtel reference number format
      final airtelRefPattern = RegExp(r'^AM\d{8,}$');
      if (!airtelRefPattern.hasMatch(transaction.referenceNumber!)) {
        _logger.w('Invalid Airtel reference number format: ${transaction.referenceNumber}');
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('Airtel verification error: $e');
      return false;
    }
  }

  /// Verify Zamtel transaction
  Future<bool> _verifyZamtelTransaction(TransactionModel transaction) async {
    try {
      // Zamtel-specific verification logic
      if (transaction.referenceNumber == null || transaction.referenceNumber!.isEmpty) {
        return false;
      }

      // Verify Zamtel reference number format
      final zamtelRefPattern = RegExp(r'^ZK\d{8,}$');
      if (!zamtelRefPattern.hasMatch(transaction.referenceNumber!)) {
        _logger.w('Invalid Zamtel reference number format: ${transaction.referenceNumber}');
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('Zamtel verification error: $e');
      return false;
    }
  }

  /// Validate Zambian phone number format
  bool _isValidZambianPhone(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Valid Zambian phone number patterns
    final patterns = [
      RegExp(r'^26096\d{7}$'), // MTN
      RegExp(r'^26097\d{7}$'), // Airtel
      RegExp(r'^26095\d{7}$'), // Zamtel
    ];

    return patterns.any((pattern) => pattern.hasMatch(cleaned));
  }

  /// Static method to support streaming functionality
  /// Returns a stream of transactions for real-time updates
  static Stream<List<TransactionModel>> streamTransactions({
    String? userId,
    String? provider,
    int limit = 50,
  }) async* {
    try {
      final instance = MobileMoneyService();

      // Yield cached transactions first for immediate display
      final cachedTransactions = await OfflineStorage.getCachedTransactions(
        userId: userId,
        provider: provider,
        limit: limit,
      );
      yield cachedTransactions;

      // Then yield real-time updates (this would be implemented with actual provider APIs)
      // For now, we'll simulate periodic updates
      while (true) {
        await Future.delayed(const Duration(seconds: 30));

        try {
          // In a real implementation, this would fetch from provider APIs
          final updatedTransactions = await OfflineStorage.getCachedTransactions(
            userId: userId,
            provider: provider,
            limit: limit,
          );
          yield updatedTransactions;
        } catch (e) {
          instance._logger.e('Error in transaction stream: $e');
          // Continue with cached data on error
          yield cachedTransactions;
        }
      }
    } catch (e) {
      MobileMoneyService()._logger.e('Failed to create transaction stream: $e');
      // Yield empty list as fallback
      yield <TransactionModel>[];
    }
  }

  /// Cache transaction for offline access and streaming
  Future<void> cacheTransactionForStreaming(TransactionModel transaction) async {
    try {
      await OfflineStorage().cacheTransaction(transaction);
      _logger.i('Transaction cached for streaming: ${transaction.id}');
    } catch (e) {
      _logger.e('Failed to cache transaction for streaming: $e');
    }
  }
}
