import 'dart:async';
import 'dart:math';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../../../core/constants/app_constants.dart';
import '../models/transaction_model.dart';
import 'mobile_money_service.dart';
import 'offline_storage.dart';
import 'mtn_api_service.dart';
import 'airtel_api_service.dart';
import 'zamtel_api_service.dart';

/// MomoStreamingService for real-time mobile money operations
/// Provides real-time streams for transactions and balances with failsafe mechanisms
class MomoStreamingService {
  static final MomoStreamingService _instance = MomoStreamingService._internal();
  factory MomoStreamingService() => _instance;
  MomoStreamingService._internal();

  final MobileMoneyService _mobileMoneyService = MobileMoneyService();
  final MTNApiService _mtnService = MTNApiService();
  final AirtelApiService _airtelService = AirtelApiService();
  final ZamtelApiService _zamtelService = ZamtelApiService();
  final Connectivity _connectivity = Connectivity();
  final Logger _logger = Logger();

  // Stream controllers for real-time data
  final StreamController<List<TransactionModel>> _transactionController = 
      StreamController<List<TransactionModel>>.broadcast();
  final StreamController<Map<String, double>> _balanceController = 
      StreamController<Map<String, double>>.broadcast();
  
  Timer? _transactionTimer;
  Timer? _balanceTimer;
  bool _isInitialized = false;

  /// Initialize the streaming service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _logger.i('Initializing MomoStreamingService...');
      
      // Start periodic updates
      _startTransactionStream();
      _startBalanceStream();
      
      // Listen to connectivity changes
      _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);
      
      _isInitialized = true;
      _logger.i('MomoStreamingService initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize MomoStreamingService: $e');
      rethrow;
    }
  }

  /// Get real-time transaction stream with error handling and fallbacks
  Stream<List<TransactionModel>> get realtimeTransactions {
    return _transactionController.stream.handleError((error) {
      _logger.e('Transaction stream error: $error');
      // FAILSAFE: Fallback to cached transactions
      return OfflineStorage.getCachedTransactions();
    }).asyncMap((transactions) async {
      // VALIDATION: Verify transactions with providers
      final verifiedTransactions = <TransactionModel>[];
      
      for (final tx in transactions) {
        try {
          final verified = await _verifyWithProvider(tx.id, tx.provider);
          if (verified) {
            verifiedTransactions.add(tx);
          } else {
            _logger.w('Transaction verification failed: ${tx.id}');
          }
        } catch (e) {
          _logger.e('Error verifying transaction ${tx.id}: $e');
          // Include transaction but mark as unverified
          verifiedTransactions.add(tx.copyWith(
            metadata: {...?tx.metadata, 'verified': false}
          ));
        }
      }
      
      return verifiedTransactions;
    });
  }

  /// Get real-time balance stream with error handling
  Stream<Map<String, double>> get realtimeBalances {
    return _balanceController.stream.handleError((error) async {
      _logger.e('Balance stream error: $error');
      // FAILSAFE: Fallback to cached balances
      final cachedBalances = <String, double>{};
      for (final provider in [AppConstants.providerMTN, AppConstants.providerAirtel, AppConstants.providerZamtel]) {
        cachedBalances[provider] = await OfflineStorage.getCachedBalance(provider);
      }
      return cachedBalances;
    });
  }

  /// Start transaction streaming with periodic updates
  void _startTransactionStream() {
    _transactionTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      try {
        // Check connectivity first
        final connectivityResult = await _connectivity.checkConnectivity();
        if (connectivityResult == ConnectivityResult.none) {
          // Use cached data when offline
          final cachedTransactions = await OfflineStorage.getCachedTransactions(limit: 20);
          _transactionController.add(cachedTransactions);
          return;
        }

        // Fetch recent transactions from all providers
        final allTransactions = <TransactionModel>[];
        
        // This would typically call provider APIs to get recent transactions
        // For now, we'll simulate with cached data and add some real-time updates
        final cachedTransactions = await OfflineStorage.getCachedTransactions(limit: 10);
        allTransactions.addAll(cachedTransactions);
        
        // Add to stream
        _transactionController.add(allTransactions);
        
        // Cache the transactions for offline access
        for (final transaction in allTransactions) {
          await OfflineStorage().cacheTransaction(transaction);
        }
        
      } catch (e) {
        _logger.e('Error in transaction stream: $e');
        // Fallback to cached data
        final cachedTransactions = await OfflineStorage.getCachedTransactions();
        _transactionController.add(cachedTransactions);
      }
    });
  }

  /// Start balance streaming with periodic updates
  void _startBalanceStream() {
    _balanceTimer = Timer.periodic(const Duration(minutes: 2), (timer) async {
      try {
        // Check connectivity first
        final connectivityResult = await _connectivity.checkConnectivity();
        if (connectivityResult == ConnectivityResult.none) {
          // Use cached balances when offline
          final cachedBalances = <String, double>{};
          for (final provider in [AppConstants.providerMTN, AppConstants.providerAirtel, AppConstants.providerZamtel]) {
            cachedBalances[provider] = await OfflineStorage.getCachedBalance(provider);
          }
          _balanceController.add(cachedBalances);
          return;
        }

        // Fetch real-time balances
        final balances = await _mobileMoneyService.getAllBalances();
        
        // Cache the balances
        for (final entry in balances.entries) {
          await OfflineStorage().cacheBalance(entry.key, entry.value);
        }
        
        _balanceController.add(balances);
        
      } catch (e) {
        _logger.e('Error in balance stream: $e');
        // Fallback to cached balances
        final cachedBalances = <String, double>{};
        for (final provider in [AppConstants.providerMTN, AppConstants.providerAirtel, AppConstants.providerZamtel]) {
          cachedBalances[provider] = await OfflineStorage.getCachedBalance(provider);
        }
        _balanceController.add(cachedBalances);
      }
    });
  }

  /// Verify transaction with provider
  /// Returns true if transaction is valid and confirmed by provider
  Future<bool> _verifyWithProvider(String transactionId, String provider) async {
    try {
      final transaction = await _mobileMoneyService.checkTransactionStatus(transactionId, provider);
      
      // Consider transaction verified if it has a valid status
      return transaction.status == AppConstants.statusCompleted || 
             transaction.status == AppConstants.statusPending ||
             transaction.status == AppConstants.statusProcessing;
             
    } catch (e) {
      _logger.e('Provider verification failed for $transactionId: $e');
      return false;
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    if (result != ConnectivityResult.none) {
      _logger.i('Connectivity restored, refreshing streams');
      // Trigger immediate refresh when connectivity is restored
      _refreshStreams();
    } else {
      _logger.w('Connectivity lost, switching to cached data');
    }
  }

  /// Refresh all streams immediately
  Future<void> _refreshStreams() async {
    try {
      // Refresh transactions
      final transactions = await OfflineStorage.getCachedTransactions(limit: 20);
      _transactionController.add(transactions);
      
      // Refresh balances
      final balances = await _mobileMoneyService.getAllBalances();
      _balanceController.add(balances);
      
    } catch (e) {
      _logger.e('Error refreshing streams: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _transactionTimer?.cancel();
    _balanceTimer?.cancel();
    _transactionController.close();
    _balanceController.close();
  }
}
