import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';
import 'package:logger/logger.dart';
import 'package:crypto/crypto.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/constants/app_constants.dart';
import '../models/transaction_model.dart';

/// MTN Mobile Money API service for Zambia
/// Implements MTN MoMo API v1.0 with proper Zambian phone number formatting
class MTNApiService {
  static final MTNApiService _instance = MTNApiService._internal();
  factory MTNApiService() => _instance;
  MTNApiService._internal();

  late final Dio _dio;
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  // MTN API credentials (should be stored securely)
  String? _accessToken;
  String? _apiKey;
  String? _subscriptionKey;
  DateTime? _tokenExpiry;

  void initialize({
    required String apiKey,
    required String subscriptionKey,
  }) {
    _apiKey = apiKey;
    _subscriptionKey = subscriptionKey;
    
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.mtnConfig['baseUrl'] as String,
      connectTimeout: Duration(milliseconds: AppConstants.connectionTimeoutMs),
      receiveTimeout: Duration(milliseconds: AppConstants.receiveTimeoutMs),
      sendTimeout: Duration(milliseconds: AppConstants.sendTimeoutMs),
      headers: {
        'Content-Type': 'application/json',
        'X-Target-Environment': AppConfig.isProduction ? 'mtnzambia' : 'sandbox',
        'Ocp-Apim-Subscription-Key': _subscriptionKey,
      },
    ));

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add authorization header if token exists
        if (_accessToken != null && _isTokenValid()) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        
        // Add request ID for tracking
        options.headers['X-Reference-Id'] = _uuid.v4();
        
        _logger.d('MTN API Request: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        _logger.d('MTN API Response: ${response.statusCode} ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) {
        _logger.e('MTN API Error: ${error.response?.statusCode} ${error.message}');
        handler.next(error);
      },
    ));
  }

  /// Authenticate with MTN API and get access token
  Future<bool> authenticate() async {
    try {
      final response = await _dio.post('/token/', data: {
        'grant_type': 'client_credentials',
      }, options: Options(
        headers: {
          'Authorization': 'Basic ${base64Encode(utf8.encode('$_apiKey:'))}',
        },
      ));

      if (response.statusCode == 200) {
        _accessToken = response.data['access_token'];
        final expiresIn = response.data['expires_in'] as int;
        _tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn - 60)); // 1 min buffer
        
        _logger.i('MTN authentication successful');
        return true;
      }
    } catch (e) {
      _logger.e('MTN authentication failed: $e');
    }
    
    return false;
  }

  bool _isTokenValid() {
    return _tokenExpiry != null && DateTime.now().isBefore(_tokenExpiry!);
  }

  /// Format Zambian phone number for MTN API
  String _formatZambianPhone(String phoneNumber) {
    // Remove any non-digit characters
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Handle different formats
    if (cleaned.startsWith('260')) {
      return cleaned; // Already in international format
    } else if (cleaned.startsWith('0')) {
      return '260${cleaned.substring(1)}'; // Remove leading 0 and add country code
    } else if (cleaned.length == 9) {
      return '260$cleaned'; // Add country code
    }
    
    return cleaned;
  }

  /// Validate if phone number is MTN Zambia
  bool _isMTNNumber(String phoneNumber) {
    final formatted = _formatZambianPhone(phoneNumber);
    // MTN Zambia numbers start with 26096
    return formatted.startsWith('26096');
  }

  /// Send money to another MTN user
  Future<TransactionResponse> sendMoney({
    required String senderPhone,
    required String receiverPhone,
    required double amount,
    required String externalId,
    String? message,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    final formattedSender = _formatZambianPhone(senderPhone);
    final formattedReceiver = _formatZambianPhone(receiverPhone);

    if (!_isMTNNumber(formattedReceiver)) {
      throw Exception('Receiver is not an MTN Zambia number');
    }

    final request = TransactionRequest(
      amount: amount.toStringAsFixed(2),
      currency: AppConfig.mtnConfig['currency'] as String,
      externalId: externalId,
      payerPartyId: formattedSender,
      payeePartyId: formattedReceiver,
      payerMessage: message ?? 'Money transfer via Zambia Pay',
      payeeNote: 'You have received money via Zambia Pay',
    );

    try {
      final response = await _dio.post(
        '/transfer',
        data: request.toJson(),
        options: Options(
          headers: {
            'X-Reference-Id': externalId,
          },
        ),
      );

      if (response.statusCode == 202) {
        // Transaction accepted, check status
        return await checkTransactionStatus(externalId);
      } else {
        throw Exception('Transaction failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('MTN send money failed: $e');
      rethrow;
    }
  }

  /// Request payment from MTN user
  Future<TransactionResponse> requestPayment({
    required String payerPhone,
    required String payeePhone,
    required double amount,
    required String externalId,
    String? message,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    final formattedPayer = _formatZambianPhone(payerPhone);
    final formattedPayee = _formatZambianPhone(payeePhone);

    if (!_isMTNNumber(formattedPayer)) {
      throw Exception('Payer is not an MTN Zambia number');
    }

    final request = TransactionRequest(
      amount: amount.toStringAsFixed(2),
      currency: AppConfig.mtnConfig['currency'] as String,
      externalId: externalId,
      payerPartyId: formattedPayer,
      payeePartyId: formattedPayee,
      payerMessage: message ?? 'Payment request via Zambia Pay',
      payeeNote: 'Payment request via Zambia Pay',
    );

    try {
      final response = await _dio.post(
        '/requesttopay',
        data: request.toJson(),
        options: Options(
          headers: {
            'X-Reference-Id': externalId,
          },
        ),
      );

      if (response.statusCode == 202) {
        return await checkTransactionStatus(externalId);
      } else {
        throw Exception('Payment request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('MTN request payment failed: $e');
      rethrow;
    }
  }

  /// Check transaction status
  Future<TransactionResponse> checkTransactionStatus(String externalId) async {
    try {
      final response = await _dio.get(
        '/transfer/$externalId',
        options: Options(
          headers: {
            'X-Target-Environment': AppConfig.isProduction ? 'mtnzambia' : 'sandbox',
          },
        ),
      );

      return TransactionResponse.fromJson(response.data);
    } catch (e) {
      _logger.e('MTN status check failed: $e');
      rethrow;
    }
  }

  /// Get account balance
  Future<double> getBalance() async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    try {
      final response = await _dio.get('/account/balance');
      
      if (response.statusCode == 200) {
        final balance = response.data['availableBalance'] as String;
        return double.parse(balance);
      } else {
        throw Exception('Failed to get balance');
      }
    } catch (e) {
      _logger.e('MTN get balance failed: $e');
      rethrow;
    }
  }

  /// Calculate transaction fee
  double calculateFee(double amount) {
    final feeRate = AppConfig.mtnConfig['txFee'] as double;
    return amount * feeRate;
  }

  /// Validate transaction limits
  bool validateAmount(double amount) {
    final minAmount = AppConfig.mtnConfig['minTransactionAmount'] as double;
    final maxAmount = AppConfig.mtnConfig['maxTransactionAmount'] as double;
    
    return amount >= minAmount && amount <= maxAmount;
  }

  /// Ensure authentication is valid
  Future<bool> _ensureAuthenticated() async {
    if (_accessToken == null || !_isTokenValid()) {
      return await authenticate();
    }
    return true;
  }

  /// Get provider information
  MobileMoneyProvider getProviderInfo() {
    return MobileMoneyProvider(
      code: AppConstants.providerMTN,
      name: 'MTN Mobile Money',
      operatorCode: AppConfig.mtnConfig['operatorCode'] as String,
      countryCode: AppConfig.mtnConfig['countryCode'] as String,
      transactionFee: AppConfig.mtnConfig['txFee'] as double,
      maxAmount: AppConfig.mtnConfig['maxTransactionAmount'] as double,
      minAmount: AppConfig.mtnConfig['minTransactionAmount'] as double,
      supportedServices: [
        'SEND_MONEY',
        'REQUEST_PAYMENT',
        'BILL_PAYMENT',
        'AIRTIME_PURCHASE',
      ],
    );
  }
}
