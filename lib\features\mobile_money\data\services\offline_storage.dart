import 'dart:async';
import 'package:logger/logger.dart';
import 'package:sqflite/sqflite.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../data/database/database_helper.dart';
import '../models/transaction_model.dart';

/// OfflineStorage service for cached transactions
/// Provides fallback functionality when real-time streams fail
/// Integrates with existing DatabaseHelper for data persistence
class OfflineStorage {
  static final OfflineStorage _instance = OfflineStorage._internal();
  factory OfflineStorage() => _instance;
  OfflineStorage._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Logger _logger = Logger();

  /// Get cached transactions for a user
  /// Returns transactions from local database when network is unavailable
  static Future<List<TransactionModel>> getCachedTransactions({
    String? userId,
    int limit = 50,
    String? provider,
  }) async {
    try {
      final instance = OfflineStorage();
      final db = await instance._dbHelper.database;
      
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];
      
      if (userId != null) {
        whereClause += ' AND user_id = ?';
        whereArgs.add(userId);
      }
      
      if (provider != null) {
        whereClause += ' AND provider = ?';
        whereArgs.add(provider);
      }
      
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.transactionsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'created_at DESC',
        limit: limit,
      );
      
      return maps.map((map) => TransactionModel.fromDatabase(map)).toList();
    } catch (e) {
      OfflineStorage()._logger.e('Failed to get cached transactions: $e');
      return [];
    }
  }

  /// Cache transaction for offline access
  /// Stores transaction in local database for later retrieval
  Future<void> cacheTransaction(TransactionModel transaction) async {
    try {
      final db = await _dbHelper.database;
      
      final transactionMap = {
        'id': transaction.id,
        'user_id': transaction.userId,
        'transaction_type': transaction.transactionType,
        'amount': transaction.amount,
        'currency': transaction.currency,
        'fee': transaction.fee,
        'total_amount': transaction.totalAmount,
        'sender_phone': transaction.senderPhone,
        'receiver_phone': transaction.receiverPhone,
        'provider': transaction.provider,
        'status': transaction.status,
        'reference_number': transaction.referenceNumber,
        'description': transaction.description,
        'metadata': transaction.metadata != null 
            ? transaction.metadata.toString() 
            : null,
        'created_at': transaction.createdAt.millisecondsSinceEpoch,
        'updated_at': transaction.updatedAt.millisecondsSinceEpoch,
        'completed_at': transaction.completedAt?.millisecondsSinceEpoch,
        'error_message': transaction.errorMessage,
        'retry_count': transaction.retryCount,
      };
      
      await db.insert(
        AppConstants.transactionsTable,
        transactionMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      _logger.i('Transaction cached: ${transaction.id}');
    } catch (e) {
      _logger.e('Failed to cache transaction: $e');
      rethrow;
    }
  }

  /// Get cached balance for a provider
  /// Returns last known balance from cache when real-time data unavailable
  static Future<double> getCachedBalance(String provider) async {
    try {
      final instance = OfflineStorage();
      final db = await instance._dbHelper.database;
      
      // Check if balance cache table exists, create if not
      await _ensureBalanceCacheTable(db);
      
      final List<Map<String, dynamic>> maps = await db.query(
        'balance_cache',
        where: 'provider = ?',
        whereArgs: [provider],
        orderBy: 'cached_at DESC',
        limit: 1,
      );
      
      if (maps.isNotEmpty) {
        final cachedAt = DateTime.fromMillisecondsSinceEpoch(maps.first['cached_at']);
        final now = DateTime.now();
        
        // Return cached balance if it's less than 1 hour old
        if (now.difference(cachedAt).inHours < 1) {
          return (maps.first['balance'] as num).toDouble();
        }
      }
      
      return 0.0;
    } catch (e) {
      OfflineStorage()._logger.e('Failed to get cached balance: $e');
      return 0.0;
    }
  }

  /// Cache balance for a provider
  /// Stores current balance for offline access
  Future<void> cacheBalance(String provider, double balance) async {
    try {
      final db = await _dbHelper.database;
      
      // Ensure balance cache table exists
      await _ensureBalanceCacheTable(db);
      
      final balanceMap = {
        'provider': provider,
        'balance': balance,
        'cached_at': DateTime.now().millisecondsSinceEpoch,
      };
      
      await db.insert(
        'balance_cache',
        balanceMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      _logger.i('Balance cached for $provider: $balance');
    } catch (e) {
      _logger.e('Failed to cache balance: $e');
    }
  }

  /// Ensure balance cache table exists
  static Future<void> _ensureBalanceCacheTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS balance_cache (
        provider TEXT PRIMARY KEY,
        balance REAL NOT NULL,
        cached_at INTEGER NOT NULL
      )
    ''');
  }

  /// Clear old cached data
  /// Removes cached transactions and balances older than specified days
  Future<void> clearOldCache({int daysOld = 30}) async {
    try {
      final db = await _dbHelper.database;
      final cutoffTime = DateTime.now()
          .subtract(Duration(days: daysOld))
          .millisecondsSinceEpoch;
      
      // Clear old transactions
      await db.delete(
        AppConstants.transactionsTable,
        where: 'created_at < ? AND status IN (?, ?)',
        whereArgs: [cutoffTime, AppConstants.statusCompleted, AppConstants.statusFailed],
      );
      
      // Clear old balance cache
      await db.delete(
        'balance_cache',
        where: 'cached_at < ?',
        whereArgs: [cutoffTime],
      );
      
      _logger.i('Cleared cache older than $daysOld days');
    } catch (e) {
      _logger.e('Failed to clear old cache: $e');
    }
  }
}
