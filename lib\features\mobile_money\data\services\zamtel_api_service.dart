import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';
import 'package:logger/logger.dart';
import 'package:crypto/crypto.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/constants/app_constants.dart';
import '../models/transaction_model.dart';

/// Zamtel Kwacha API service for Zambia
/// Implements Zamtel mobile money API with proper Zambian phone number formatting
class ZamtelApiService {
  static final ZamtelApiService _instance = ZamtelApiService._internal();
  factory ZamtelApiService() => _instance;
  ZamtelApiService._internal();

  late final Dio _dio;
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  // Zamtel API credentials
  String? _accessToken;
  String? _apiKey;
  String? _merchantId;
  DateTime? _tokenExpiry;

  void initialize({
    required String apiKey,
    required String merchantId,
  }) {
    _apiKey = apiKey;
    _merchantId = merchantId;
    
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.zamtelConfig['baseUrl'] as String,
      connectTimeout: Duration(milliseconds: AppConstants.connectionTimeoutMs),
      receiveTimeout: Duration(milliseconds: AppConstants.receiveTimeoutMs),
      sendTimeout: Duration(milliseconds: AppConstants.sendTimeoutMs),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': _apiKey,
        'X-Country': 'ZM',
        'X-Currency': 'ZMW',
      },
    ));

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add authorization header if token exists
        if (_accessToken != null && _isTokenValid()) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        
        // Add unique transaction ID
        options.headers['X-Transaction-Id'] = _uuid.v4();
        
        _logger.d('Zamtel API Request: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        _logger.d('Zamtel API Response: ${response.statusCode} ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) {
        _logger.e('Zamtel API Error: ${error.response?.statusCode} ${error.message}');
        handler.next(error);
      },
    ));
  }

  /// Authenticate with Zamtel API and get access token
  Future<bool> authenticate() async {
    try {
      final response = await _dio.post('/auth/token', data: {
        'api_key': _apiKey,
        'merchant_id': _merchantId,
        'grant_type': 'client_credentials',
      });

      if (response.statusCode == 200) {
        _accessToken = response.data['access_token'];
        final expiresIn = response.data['expires_in'] as int;
        _tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn - 60));
        
        _logger.i('Zamtel authentication successful');
        return true;
      }
    } catch (e) {
      _logger.e('Zamtel authentication failed: $e');
    }
    
    return false;
  }

  bool _isTokenValid() {
    return _tokenExpiry != null && DateTime.now().isBefore(_tokenExpiry!);
  }

  /// Format Zambian phone number for Zamtel API
  String _formatZambianPhone(String phoneNumber) {
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleaned.startsWith('260')) {
      return cleaned;
    } else if (cleaned.startsWith('0')) {
      return '260${cleaned.substring(1)}';
    } else if (cleaned.length == 9) {
      return '260$cleaned';
    }
    
    return cleaned;
  }

  /// Validate if phone number is Zamtel
  bool _isZamtelNumber(String phoneNumber) {
    final formatted = _formatZambianPhone(phoneNumber);
    // Zamtel numbers start with 26095
    return formatted.startsWith('26095');
  }

  /// Send money to another Zamtel user
  Future<TransactionResponse> sendMoney({
    required String senderPhone,
    required String receiverPhone,
    required double amount,
    required String externalId,
    String? message,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    final formattedSender = _formatZambianPhone(senderPhone);
    final formattedReceiver = _formatZambianPhone(receiverPhone);

    if (!_isZamtelNumber(formattedReceiver)) {
      throw Exception('Receiver is not a Zamtel number');
    }

    final requestData = {
      'transaction_id': externalId,
      'sender_msisdn': formattedSender,
      'receiver_msisdn': formattedReceiver,
      'amount': amount.toStringAsFixed(2),
      'currency': 'ZMW',
      'description': message ?? 'Money transfer via Zambia Pay',
      'merchant_id': _merchantId,
    };

    try {
      final response = await _dio.post(
        '/transactions/send',
        data: requestData,
        options: Options(
          headers: {
            'X-Reference-Id': externalId,
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return TransactionResponse(
          financialTransactionId: response.data['transaction_id'],
          externalId: externalId,
          amount: amount.toStringAsFixed(2),
          currency: 'ZMW',
          status: response.data['status'] == 'SUCCESS' ? 'SUCCESSFUL' : 'PENDING',
        );
      } else {
        throw Exception('Transaction failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Zamtel send money failed: $e');
      rethrow;
    }
  }

  /// Request payment from Zamtel user
  Future<TransactionResponse> requestPayment({
    required String payerPhone,
    required String payeePhone,
    required double amount,
    required String externalId,
    String? message,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    final formattedPayer = _formatZambianPhone(payerPhone);

    if (!_isZamtelNumber(formattedPayer)) {
      throw Exception('Payer is not a Zamtel number');
    }

    final requestData = {
      'transaction_id': externalId,
      'payer_msisdn': formattedPayer,
      'payee_msisdn': _formatZambianPhone(payeePhone),
      'amount': amount.toStringAsFixed(2),
      'currency': 'ZMW',
      'description': message ?? 'Payment request via Zambia Pay',
      'merchant_id': _merchantId,
    };

    try {
      final response = await _dio.post(
        '/transactions/request',
        data: requestData,
        options: Options(
          headers: {
            'X-Reference-Id': externalId,
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return TransactionResponse(
          financialTransactionId: response.data['transaction_id'],
          externalId: externalId,
          amount: amount.toStringAsFixed(2),
          currency: 'ZMW',
          status: response.data['status'] == 'SUCCESS' ? 'SUCCESSFUL' : 'PENDING',
        );
      } else {
        throw Exception('Payment request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Zamtel request payment failed: $e');
      rethrow;
    }
  }

  /// Check transaction status
  Future<TransactionResponse> checkTransactionStatus(String transactionId) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    try {
      final response = await _dio.get('/transactions/$transactionId/status');
      
      return TransactionResponse(
        financialTransactionId: response.data['transaction_id'],
        externalId: transactionId,
        amount: response.data['amount'],
        currency: response.data['currency'],
        status: response.data['status'] == 'SUCCESS' ? 'SUCCESSFUL' : 'PENDING',
      );
    } catch (e) {
      _logger.e('Zamtel status check failed: $e');
      rethrow;
    }
  }

  /// Get account balance
  Future<double> getBalance() async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    try {
      final response = await _dio.get('/account/balance');
      
      if (response.statusCode == 200) {
        final balance = response.data['balance'] as String;
        return double.parse(balance);
      } else {
        throw Exception('Failed to get balance');
      }
    } catch (e) {
      _logger.e('Zamtel get balance failed: $e');
      rethrow;
    }
  }

  /// Calculate transaction fee
  double calculateFee(double amount) {
    final feeRate = AppConfig.zamtelConfig['txFee'] as double;
    return amount * feeRate;
  }

  /// Validate transaction limits
  bool validateAmount(double amount) {
    final minAmount = AppConfig.zamtelConfig['minTransactionAmount'] as double;
    final maxAmount = AppConfig.zamtelConfig['maxTransactionAmount'] as double;
    
    return amount >= minAmount && amount <= maxAmount;
  }

  /// Ensure authentication is valid
  Future<bool> _ensureAuthenticated() async {
    if (_accessToken == null || !_isTokenValid()) {
      return await authenticate();
    }
    return true;
  }

  /// Get provider information
  MobileMoneyProvider getProviderInfo() {
    return MobileMoneyProvider(
      code: AppConstants.providerZamtel,
      name: 'Zamtel Kwacha',
      operatorCode: AppConfig.zamtelConfig['operatorCode'] as String,
      countryCode: AppConfig.zamtelConfig['countryCode'] as String,
      transactionFee: AppConfig.zamtelConfig['txFee'] as double,
      maxAmount: AppConfig.zamtelConfig['maxTransactionAmount'] as double,
      minAmount: AppConfig.zamtelConfig['minTransactionAmount'] as double,
      supportedServices: [
        'SEND_MONEY',
        'REQUEST_PAYMENT',
        'BILL_PAYMENT',
        'AIRTIME_PURCHASE',
      ],
    );
  }
}
