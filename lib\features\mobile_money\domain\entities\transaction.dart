import 'package:equatable/equatable.dart';

/// Transaction entity representing a mobile money transaction
class Transaction extends Equatable {
  final String id;
  final String userId;
  final String type;
  final double amount;
  final String currency;
  final String status;
  final String? description;
  final String? recipientPhone;
  final String? senderPhone;
  final DateTime createdAt;
  final DateTime? completedAt;
  final Map<String, dynamic>? metadata;
  final String? referenceNumber;
  final double? fees;
  final String? provider; // MTN, Airtel, Zamtel
  final String? merchantId;
  final bool isOffline;

  const Transaction({
    required this.id,
    required this.userId,
    required this.type,
    required this.amount,
    required this.currency,
    required this.status,
    required this.createdAt,
    this.description,
    this.recipientPhone,
    this.senderPhone,
    this.completedAt,
    this.metadata,
    this.referenceNumber,
    this.fees,
    this.provider,
    this.merchantId,
    this.isOffline = false,
  });

  /// Transaction types
  static const String typeSendMoney = 'SEND_MONEY';
  static const String typeReceiveMoney = 'RECEIVE_MONEY';
  static const String typeBuyAirtime = 'BUY_AIRTIME';
  static const String typePayBill = 'PAY_BILL';
  static const String typeWithdraw = 'WITHDRAW';
  static const String typeDeposit = 'DEPOSIT';
  static const String typeQrPayment = 'QR_PAYMENT';
  static const String typeMerchantPayment = 'MERCHANT_PAYMENT';

  /// Transaction statuses
  static const String statusPending = 'PENDING';
  static const String statusProcessing = 'PROCESSING';
  static const String statusCompleted = 'COMPLETED';
  static const String statusFailed = 'FAILED';
  static const String statusCancelled = 'CANCELLED';
  static const String statusRefunded = 'REFUNDED';

  /// Supported currencies
  static const String currencyZMW = 'ZMW';
  static const String currencyUSD = 'USD';

  /// Mobile money providers
  static const String providerMTN = 'MTN';
  static const String providerAirtel = 'AIRTEL';
  static const String providerZamtel = 'ZAMTEL';

  /// Check if transaction is successful
  bool get isSuccessful => status == statusCompleted;

  /// Check if transaction is pending
  bool get isPending => status == statusPending || status == statusProcessing;

  /// Check if transaction failed
  bool get isFailed => status == statusFailed || status == statusCancelled;

  /// Get total amount including fees
  double get totalAmount => amount + (fees ?? 0.0);

  /// Check if transaction is a payment type
  bool get isPayment => [
    typeSendMoney,
    typePayBill,
    typeQrPayment,
    typeMerchantPayment,
    typeBuyAirtime,
  ].contains(type);

  /// Check if transaction is a receipt type
  bool get isReceipt => [
    typeReceiveMoney,
    typeDeposit,
  ].contains(type);

  /// Get formatted amount with currency
  String get formattedAmount {
    return '$currency ${amount.toStringAsFixed(2)}';
  }

  /// Get formatted total amount with currency
  String get formattedTotalAmount {
    return '$currency ${totalAmount.toStringAsFixed(2)}';
  }

  /// Copy with method for immutable updates
  Transaction copyWith({
    String? id,
    String? userId,
    String? type,
    double? amount,
    String? currency,
    String? status,
    String? description,
    String? recipientPhone,
    String? senderPhone,
    DateTime? createdAt,
    DateTime? completedAt,
    Map<String, dynamic>? metadata,
    String? referenceNumber,
    double? fees,
    String? provider,
    String? merchantId,
    bool? isOffline,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      description: description ?? this.description,
      recipientPhone: recipientPhone ?? this.recipientPhone,
      senderPhone: senderPhone ?? this.senderPhone,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      metadata: metadata ?? this.metadata,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      fees: fees ?? this.fees,
      provider: provider ?? this.provider,
      merchantId: merchantId ?? this.merchantId,
      isOffline: isOffline ?? this.isOffline,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type,
      'amount': amount,
      'currency': currency,
      'status': status,
      'description': description,
      'recipientPhone': recipientPhone,
      'senderPhone': senderPhone,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'metadata': metadata,
      'referenceNumber': referenceNumber,
      'fees': fees,
      'provider': provider,
      'merchantId': merchantId,
      'isOffline': isOffline,
    };
  }

  /// Create from JSON
  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: json['type'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: json['status'] as String,
      description: json['description'] as String?,
      recipientPhone: json['recipientPhone'] as String?,
      senderPhone: json['senderPhone'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
      referenceNumber: json['referenceNumber'] as String?,
      fees: json['fees'] != null ? (json['fees'] as num).toDouble() : null,
      provider: json['provider'] as String?,
      merchantId: json['merchantId'] as String?,
      isOffline: json['isOffline'] as bool? ?? false,
    );
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    type,
    amount,
    currency,
    status,
    description,
    recipientPhone,
    senderPhone,
    createdAt,
    completedAt,
    metadata,
    referenceNumber,
    fees,
    provider,
    merchantId,
    isOffline,
  ];

  @override
  String toString() {
    return 'Transaction(id: $id, type: $type, amount: $formattedAmount, status: $status)';
  }
}
