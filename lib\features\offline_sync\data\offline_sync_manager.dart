import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:workmanager/workmanager.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../../core/config/app_config.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/database/database_helper.dart';
import '../../mobile_money/data/models/transaction_model.dart';

/// VeryPay-style offline sync manager with background processing
/// Handles transaction queuing, retry logic, and network-aware synchronization
class OfflineSyncManager {
  static final OfflineSyncManager _instance = OfflineSyncManager._internal();
  factory OfflineSyncManager() => _instance;
  OfflineSyncManager._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Connectivity _connectivity = Connectivity();
  final InternetConnectionChecker _connectionChecker = InternetConnectionChecker();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  Timer? _syncTimer;
  bool _isSyncing = false;

  // Initialize the sync manager
  Future<void> initialize() async {
    await _setupWorkManager();
    await _startConnectivityListener();
    await _schedulePeriodicSync();
    
    // Attempt initial sync if connected
    if (await isConnected()) {
      await syncPendingTransactions();
    }
  }

  // Setup WorkManager for background processing
  Future<void> _setupWorkManager() async {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: !AppConfig.isProduction,
    );
    
    // Register periodic sync task
    await Workmanager().registerPeriodicTask(
      'sync_task',
      'syncPendingTransactions',
      frequency: Duration(minutes: AppConfig.syncIntervalMinutes),
      constraints: Constraints(
        networkType: NetworkType.connected,
        requiresBatteryNotLow: true,
      ),
    );
  }

  // Start listening to connectivity changes
  Future<void> _startConnectivityListener() async {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (ConnectivityResult result) async {
        if (result != ConnectivityResult.none) {
          // Network available, attempt sync
          await Future.delayed(Duration(seconds: 2)); // Wait for stable connection
          if (await isConnected()) {
            await syncPendingTransactions();
          }
        }
      },
    );
  }

  // Schedule periodic sync attempts
  Future<void> _schedulePeriodicSync() async {
    _syncTimer = Timer.periodic(
      Duration(minutes: AppConfig.syncIntervalMinutes),
      (timer) async {
        if (await isConnected()) {
          await syncPendingTransactions();
        }
      },
    );
  }

  // Check if device has internet connection
  Future<bool> isConnected() async {
    try {
      return await _connectionChecker.hasConnection;
    } catch (e) {
      _logger.e('Error checking connection: $e');
      return false;
    }
  }

  // Queue transaction for offline processing
  Future<String> queueTransaction({
    required String userId,
    required Map<String, dynamic> transactionData,
    required String transactionType,
    int priority = 1,
  }) async {
    final queueId = _uuid.v4();
    final now = DateTime.now().millisecondsSinceEpoch;
    
    // Encrypt sensitive transaction data
    final encryptedData = await _encryptTransactionData(transactionData);
    
    final queueItem = {
      'id': queueId,
      'user_id': userId,
      'transaction_data': encryptedData,
      'transaction_type': transactionType,
      'priority': priority,
      'retry_count': 0,
      'max_retries': AppConfig.maxRetryAttempts,
      'created_at': now,
      'next_retry_at': now,
      'status': AppConstants.statusQueued,
    };

    await _dbHelper.insert(AppConstants.offlineQueueTable, queueItem);
    
    _logger.i('Transaction queued for offline sync: $queueId');
    
    // Attempt immediate sync if connected
    if (await isConnected()) {
      await syncPendingTransactions();
    }
    
    return queueId;
  }

  // Sync all pending transactions
  Future<void> syncPendingTransactions() async {
    if (_isSyncing) return;
    
    _isSyncing = true;
    _logger.i('Starting sync of pending transactions');
    
    try {
      final pendingTransactions = await _getPendingTransactions();
      
      for (final transaction in pendingTransactions) {
        await _processSingleTransaction(transaction);
      }
      
      _logger.i('Sync completed. Processed ${pendingTransactions.length} transactions');
    } catch (e) {
      _logger.e('Error during sync: $e');
    } finally {
      _isSyncing = false;
    }
  }

  // Get pending transactions from queue
  Future<List<Map<String, dynamic>>> _getPendingTransactions() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    return await _dbHelper.query(
      AppConstants.offlineQueueTable,
      where: 'status = ? AND next_retry_at <= ? AND retry_count < max_retries',
      whereArgs: [AppConstants.statusQueued, now],
      orderBy: 'priority DESC, created_at ASC',
      limit: AppConstants.syncBatchSize,
    );
  }

  // Process a single transaction
  Future<void> _processSingleTransaction(Map<String, dynamic> queueItem) async {
    final queueId = queueItem['id'] as String;
    
    try {
      // Decrypt transaction data
      final transactionData = await _decryptTransactionData(
        queueItem['transaction_data'] as String,
      );
      
      // Attempt to process the transaction
      final success = await _executeTransaction(transactionData);
      
      if (success) {
        // Mark as completed and remove from queue
        await _dbHelper.delete(
          AppConstants.offlineQueueTable,
          where: 'id = ?',
          whereArgs: [queueId],
        );
        
        _logger.i('Transaction synced successfully: $queueId');
      } else {
        // Increment retry count and schedule next retry
        await _scheduleRetry(queueItem);
      }
    } catch (e) {
      _logger.e('Error processing transaction $queueId: $e');
      await _scheduleRetry(queueItem, errorMessage: e.toString());
    }
  }

  // Schedule retry for failed transaction
  Future<void> _scheduleRetry(
    Map<String, dynamic> queueItem, {
    String? errorMessage,
  }) async {
    final queueId = queueItem['id'] as String;
    final retryCount = (queueItem['retry_count'] as int) + 1;
    final maxRetries = queueItem['max_retries'] as int;
    
    if (retryCount >= maxRetries) {
      // Max retries reached, mark as failed
      await _dbHelper.update(
        AppConstants.offlineQueueTable,
        {
          'status': AppConstants.statusFailed,
          'error_message': errorMessage ?? 'Max retries exceeded',
        },
        where: 'id = ?',
        whereArgs: [queueId],
      );
      
      _logger.w('Transaction failed after max retries: $queueId');
      return;
    }
    
    // Calculate exponential backoff delay
    final delayMinutes = _calculateRetryDelay(retryCount);
    final nextRetryAt = DateTime.now()
        .add(Duration(minutes: delayMinutes))
        .millisecondsSinceEpoch;
    
    await _dbHelper.update(
      AppConstants.offlineQueueTable,
      {
        'retry_count': retryCount,
        'last_retry_at': DateTime.now().millisecondsSinceEpoch,
        'next_retry_at': nextRetryAt,
        'error_message': errorMessage,
      },
      where: 'id = ?',
      whereArgs: [queueId],
    );
    
    _logger.i('Scheduled retry $retryCount/$maxRetries for transaction $queueId in $delayMinutes minutes');
  }

  // Calculate retry delay with exponential backoff
  int _calculateRetryDelay(int retryCount) {
    // Exponential backoff: 1, 2, 4, 8, 16 minutes (max 15 minutes as per requirement)
    final delay = (1 << (retryCount - 1)).clamp(1, 15);
    return delay;
  }

  // Execute transaction via appropriate API
  Future<bool> _executeTransaction(Map<String, dynamic> transactionData) async {
    // This would integrate with the actual mobile money APIs
    // For now, simulate API call
    await Future.delayed(Duration(seconds: 2));
    
    // Simulate 90% success rate
    return DateTime.now().millisecond % 10 != 0;
  }

  // Encrypt transaction data for secure storage
  Future<String> _encryptTransactionData(Map<String, dynamic> data) async {
    // Implement AES-256 encryption here
    // For now, return base64 encoded JSON
    return base64Encode(utf8.encode(jsonEncode(data)));
  }

  // Decrypt transaction data
  Future<Map<String, dynamic>> _decryptTransactionData(String encryptedData) async {
    // Implement AES-256 decryption here
    // For now, decode base64 JSON
    final jsonString = utf8.decode(base64Decode(encryptedData));
    return jsonDecode(jsonString) as Map<String, dynamic>;
  }

  // Get sync status and statistics
  Future<Map<String, dynamic>> getSyncStatus() async {
    final pending = await _dbHelper.query(
      AppConstants.offlineQueueTable,
      where: 'status = ?',
      whereArgs: [AppConstants.statusQueued],
    );
    
    final failed = await _dbHelper.query(
      AppConstants.offlineQueueTable,
      where: 'status = ?',
      whereArgs: [AppConstants.statusFailed],
    );
    
    return {
      'isConnected': await isConnected(),
      'isSyncing': _isSyncing,
      'pendingCount': pending.length,
      'failedCount': failed.length,
      'lastSyncAttempt': DateTime.now().toIso8601String(),
    };
  }

  // Cleanup old failed transactions
  Future<void> cleanupOldTransactions() async {
    final cutoffTime = DateTime.now()
        .subtract(Duration(days: 7))
        .millisecondsSinceEpoch;
    
    await _dbHelper.delete(
      AppConstants.offlineQueueTable,
      where: 'status = ? AND created_at < ?',
      whereArgs: [AppConstants.statusFailed, cutoffTime],
    );
  }

  // Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
    Workmanager().cancelAll();
  }
}

// WorkManager callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case 'syncPendingTransactions':
        final syncManager = OfflineSyncManager();
        await syncManager.syncPendingTransactions();
        break;
    }
    return Future.value(true);
  });
}
