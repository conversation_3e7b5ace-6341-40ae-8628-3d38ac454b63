import 'dart:convert';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/app_constants.dart';
import '../../core/config/environment_config.dart';
import '../../data/database/database_helper.dart';
import '../rural_resilience/sms_token_service.dart';
import '../offline_sync/data/offline_sync_manager.dart';

/// Rural QR Service for 2G Network Support
/// Optimized QR codes that work on slow networks with SMS fallback
/// Designed for Zambian informal markets and rural merchants
class RuralQRService {
  static final RuralQRService _instance = RuralQRService._internal();
  factory RuralQRService() => _instance;
  RuralQRService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final SMSTokenService _smsTokenService = SMSTokenService();
  final OfflineSyncManager _syncManager = OfflineSyncManager();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  // QR code optimization for 2G networks
  static const Map<String, dynamic> qrOptimization = {
    'max_data_size': 200,           // Maximum 200 bytes for 2G compatibility
    'compression_level': 9,         // Maximum compression
    'error_correction': 'M',        // Medium error correction (15%)
    'sms_fallback_enabled': true,   // SMS fallback for failed scans
    'offline_mode_supported': true, // Works without internet
    'cache_duration_hours': 24,     // Cache QR data for 24 hours
  };

  // Merchant categories for rural areas
  static const Map<String, Map<String, dynamic>> merchantCategories = {
    'MARKET_VENDOR': {
      'name': 'Market Vendor',
      'icon': 'store',
      'typical_amounts': [5.0, 10.0, 20.0, 50.0],
      'common_products': ['vegetables', 'fruits', 'grains', 'meat'],
    },
    'SHOP_KEEPER': {
      'name': 'Shop Keeper',
      'icon': 'shopping_cart',
      'typical_amounts': [10.0, 25.0, 50.0, 100.0],
      'common_products': ['groceries', 'household_items', 'drinks', 'snacks'],
    },
    'TRANSPORT': {
      'name': 'Transport',
      'icon': 'directions_bus',
      'typical_amounts': [15.0, 30.0, 50.0, 100.0],
      'common_products': ['bus_fare', 'taxi_fare', 'minibus_fare'],
    },
    'SERVICES': {
      'name': 'Services',
      'icon': 'build',
      'typical_amounts': [20.0, 50.0, 100.0, 200.0],
      'common_products': ['repairs', 'tailoring', 'barber', 'phone_repair'],
    },
    'AGRICULTURE': {
      'name': 'Agriculture',
      'icon': 'agriculture',
      'typical_amounts': [50.0, 100.0, 200.0, 500.0],
      'common_products': ['seeds', 'fertilizer', 'tools', 'produce'],
    },
  };

  /// Initialize rural QR service
  Future<void> initialize() async {
    await _createQRTables();
    _logger.i('Rural QR service initialized');
  }

  /// Create QR code tracking tables
  Future<void> _createQRTables() async {
    final db = await _dbHelper.database;

    // QR codes table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS qr_codes (
        id TEXT PRIMARY KEY,
        merchant_id TEXT NOT NULL,
        qr_type TEXT NOT NULL,
        qr_data TEXT NOT NULL,
        compressed_data TEXT,
        amount REAL,
        description TEXT,
        category TEXT,
        expires_at INTEGER,
        scan_count INTEGER DEFAULT 0,
        last_scanned_at INTEGER,
        status TEXT DEFAULT 'ACTIVE',
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000)
      )
    ''');

    // QR transactions table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS qr_transactions (
        id TEXT PRIMARY KEY,
        qr_code_id TEXT NOT NULL,
        payer_user_id TEXT NOT NULL,
        merchant_id TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        payment_method TEXT NOT NULL,
        transaction_status TEXT DEFAULT 'PENDING',
        offline_mode INTEGER DEFAULT 0,
        sms_fallback_used INTEGER DEFAULT 0,
        network_type TEXT,
        scan_location TEXT,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000),
        completed_at INTEGER,
        synced_at INTEGER
      )
    ''');

    // Merchant profiles table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS merchant_profiles (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        business_name TEXT NOT NULL,
        category TEXT NOT NULL,
        phone_number TEXT NOT NULL,
        location TEXT,
        region TEXT,
        typical_amounts TEXT,
        qr_enabled INTEGER DEFAULT 1,
        sms_fallback_enabled INTEGER DEFAULT 1,
        offline_payments_enabled INTEGER DEFAULT 1,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000)
      )
    ''');
  }

  /// Register merchant for QR payments
  Future<String> registerMerchant({
    required String userId,
    required String businessName,
    required String category,
    required String phoneNumber,
    String? location,
    List<double>? typicalAmounts,
  }) async {
    try {
      final merchantId = _uuid.v4();
      final currentRegion = EnvironmentConfig.currentRegion;
      
      final merchant = {
        'id': merchantId,
        'user_id': userId,
        'business_name': businessName,
        'category': category,
        'phone_number': phoneNumber,
        'location': location ?? '',
        'region': currentRegion,
        'typical_amounts': jsonEncode(typicalAmounts ?? []),
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('merchant_profiles', merchant);

      // Generate default QR code
      await generateMerchantQR(merchantId: merchantId, qrType: 'DYNAMIC');

      _logger.i('Merchant registered: $businessName ($merchantId)');
      return merchantId;
      
    } catch (e) {
      _logger.e('Merchant registration failed: $e');
      rethrow;
    }
  }

  /// Generate QR code for merchant
  Future<Map<String, dynamic>> generateMerchantQR({
    required String merchantId,
    required String qrType,
    double? amount,
    String? description,
    int? expiryHours,
  }) async {
    try {
      final qrId = _uuid.v4();
      
      // Create compact QR data for 2G networks
      final qrData = _createCompactQRData(
        qrId: qrId,
        merchantId: merchantId,
        amount: amount,
        description: description,
      );

      // Compress data for 2G optimization
      final compressedData = _compressQRData(qrData);
      
      final expiresAt = expiryHours != null
          ? DateTime.now().add(Duration(hours: expiryHours)).millisecondsSinceEpoch
          : null;

      final qrCode = {
        'id': qrId,
        'merchant_id': merchantId,
        'qr_type': qrType,
        'qr_data': qrData,
        'compressed_data': compressedData,
        'amount': amount,
        'description': description,
        'expires_at': expiresAt,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('qr_codes', qrCode);

      _logger.i('QR code generated: $qrId for merchant $merchantId');
      
      return {
        'qr_id': qrId,
        'qr_data': qrData,
        'compressed_data': compressedData,
        'expires_at': expiresAt,
        'sms_fallback_code': qrId.substring(0, 8).toUpperCase(),
      };
      
    } catch (e) {
      _logger.e('QR code generation failed: $e');
      rethrow;
    }
  }

  /// Create compact QR data optimized for 2G networks
  String _createCompactQRData({
    required String qrId,
    required String merchantId,
    double? amount,
    String? description,
  }) {
    // Ultra-compact format for 2G networks
    final data = {
      'v': '1',                                    // Version
      'q': qrId.substring(0, 8),                  // Short QR ID
      'm': merchantId.substring(0, 8),            // Short merchant ID
      'a': amount?.toStringAsFixed(2),            // Amount
      'd': description?.substring(0, 20),         // Short description
      't': DateTime.now().millisecondsSinceEpoch ~/ 1000, // Timestamp
    };

    // Remove null values to minimize size
    data.removeWhere((key, value) => value == null);
    
    return jsonEncode(data);
  }

  /// Compress QR data for 2G optimization
  String _compressQRData(String data) {
    // Simple compression for demo - in production use proper compression
    return base64Encode(utf8.encode(data));
  }

  /// Process QR code scan and payment
  Future<Map<String, dynamic>> processQRPayment({
    required String qrData,
    required String payerUserId,
    double? customAmount,
    String? paymentMethod,
  }) async {
    try {
      // Parse QR data
      final parsedData = _parseQRData(qrData);
      final qrId = parsedData['q'] as String?;
      final merchantId = parsedData['m'] as String?;
      
      if (qrId == null || merchantId == null) {
        throw Exception('Invalid QR code format');
      }

      // Find full QR code record
      final qrCodes = await _dbHelper.query(
        'qr_codes',
        where: 'id LIKE ? AND merchant_id LIKE ?',
        whereArgs: ['$qrId%', '$merchantId%'],
        limit: 1,
      );

      if (qrCodes.isEmpty) {
        throw Exception('QR code not found');
      }

      final qrCode = qrCodes.first;
      final fullQrId = qrCode['id'] as String;
      final fullMerchantId = qrCode['merchant_id'] as String;
      
      // Check expiry
      final expiresAt = qrCode['expires_at'] as int?;
      if (expiresAt != null && expiresAt < DateTime.now().millisecondsSinceEpoch) {
        throw Exception('QR code has expired');
      }

      // Determine payment amount
      final qrAmount = qrCode['amount'] as double?;
      final paymentAmount = customAmount ?? qrAmount;
      
      if (paymentAmount == null || paymentAmount <= 0) {
        throw Exception('Invalid payment amount');
      }

      // Check network connectivity
      final isOnline = await _syncManager.isConnected();
      final networkType = _detectNetworkType();

      // Create transaction
      final transactionId = _uuid.v4();
      final transaction = {
        'id': transactionId,
        'qr_code_id': fullQrId,
        'payer_user_id': payerUserId,
        'merchant_id': fullMerchantId,
        'amount': paymentAmount,
        'description': qrCode['description'] ?? 'QR Payment',
        'payment_method': paymentMethod ?? 'MOBILE_MONEY',
        'offline_mode': isOnline ? 0 : 1,
        'network_type': networkType,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('qr_transactions', transaction);

      // Update QR scan count
      await _updateQRScanCount(fullQrId);

      // Process payment based on connectivity
      if (isOnline && networkType != '2G') {
        // Online payment
        await _processOnlinePayment(transactionId);
      } else {
        // Offline or 2G - use SMS fallback
        await _processOfflinePayment(transactionId);
      }

      _logger.i('QR payment processed: $transactionId for K${paymentAmount.toStringAsFixed(2)}');
      
      return {
        'transaction_id': transactionId,
        'amount': paymentAmount,
        'merchant_id': fullMerchantId,
        'status': isOnline ? 'PROCESSING' : 'QUEUED',
        'offline_mode': !isOnline,
        'network_type': networkType,
      };
      
    } catch (e) {
      _logger.e('QR payment processing failed: $e');
      rethrow;
    }
  }

  /// Parse QR data
  Map<String, dynamic> _parseQRData(String qrData) {
    try {
      // Try to decode as JSON
      return jsonDecode(qrData);
    } catch (e) {
      // Try to decode as base64 compressed
      try {
        final decodedBytes = base64Decode(qrData);
        final decodedString = utf8.decode(decodedBytes);
        return jsonDecode(decodedString);
      } catch (e2) {
        throw Exception('Invalid QR code format');
      }
    }
  }

  /// Detect network type for optimization
  String _detectNetworkType() {
    // In production, use actual network detection
    final region = EnvironmentConfig.currentRegion;
    final regionConfig = EnvironmentConfig.regionConfig;
    final connectivity = regionConfig['connectivity'] as String;
    
    switch (connectivity) {
      case 'HIGH':
        return '4G';
      case 'MEDIUM':
        return '3G';
      case 'LOW':
        return '2G';
      default:
        return '3G';
    }
  }

  /// Process online payment
  Future<void> _processOnlinePayment(String transactionId) async {
    try {
      // Simulate online payment processing
      await Future.delayed(Duration(seconds: 2));
      
      await _dbHelper.update(
        'qr_transactions',
        {
          'transaction_status': 'COMPLETED',
          'completed_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [transactionId],
      );
      
    } catch (e) {
      await _dbHelper.update(
        'qr_transactions',
        {'transaction_status': 'FAILED'},
        where: 'id = ?',
        whereArgs: [transactionId],
      );
      rethrow;
    }
  }

  /// Process offline payment with SMS fallback
  Future<void> _processOfflinePayment(String transactionId) async {
    try {
      // Get transaction details
      final transactions = await _dbHelper.query(
        'qr_transactions',
        where: 'id = ?',
        whereArgs: [transactionId],
        limit: 1,
      );

      if (transactions.isEmpty) return;

      final transaction = transactions.first;
      final merchantId = transaction['merchant_id'] as String;
      final amount = transaction['amount'] as double;

      // Get merchant phone number
      final merchants = await _dbHelper.query(
        'merchant_profiles',
        where: 'id = ?',
        whereArgs: [merchantId],
        limit: 1,
      );

      if (merchants.isNotEmpty) {
        final merchantPhone = merchants.first['phone_number'] as String;
        
        // Generate SMS token for offline payment
        final tokenId = await _smsTokenService.generateSMSToken(
          userId: transaction['payer_user_id'],
          transactionType: 'QR_PAYMENT',
          amount: amount,
          recipientPhone: merchantPhone,
          metadata: {
            'qr_transaction_id': transactionId,
            'merchant_id': merchantId,
            'payment_type': 'QR_OFFLINE',
          },
        );

        // Update transaction with SMS token
        await _dbHelper.update(
          'qr_transactions',
          {
            'transaction_status': 'SMS_SENT',
            'sms_fallback_used': 1,
          },
          where: 'id = ?',
          whereArgs: [transactionId],
        );

        _logger.i('QR payment converted to SMS token: $tokenId');
      }
      
    } catch (e) {
      _logger.e('Offline QR payment processing failed: $e');
    }
  }

  /// Update QR scan count
  Future<void> _updateQRScanCount(String qrId) async {
    await _dbHelper.update(
      'qr_codes',
      {
        'scan_count': 'scan_count + 1',
        'last_scanned_at': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [qrId],
    );
  }

  /// Generate SMS fallback code for QR payment
  Future<String> generateSMSFallbackCode({
    required String merchantId,
    required double amount,
    String? description,
  }) async {
    try {
      // Create short code for SMS
      final shortCode = _uuid.v4().substring(0, 6).toUpperCase();
      
      // Store fallback mapping
      final fallback = {
        'id': _uuid.v4(),
        'short_code': shortCode,
        'merchant_id': merchantId,
        'amount': amount,
        'description': description,
        'expires_at': DateTime.now().add(Duration(hours: 24)).millisecondsSinceEpoch,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('qr_sms_fallbacks', fallback);
      
      return shortCode;
    } catch (e) {
      _logger.e('SMS fallback code generation failed: $e');
      rethrow;
    }
  }

  /// Process SMS fallback payment
  Future<Map<String, dynamic>> processSMSFallbackPayment({
    required String shortCode,
    required String payerUserId,
    required String paymentMethod,
  }) async {
    try {
      // Find fallback record
      final fallbacks = await _dbHelper.query(
        'qr_sms_fallbacks',
        where: 'short_code = ? AND expires_at > ?',
        whereArgs: [shortCode, DateTime.now().millisecondsSinceEpoch],
        limit: 1,
      );

      if (fallbacks.isEmpty) {
        throw Exception('Invalid or expired SMS code');
      }

      final fallback = fallbacks.first;
      
      // Process as regular QR payment
      return await processQRPayment(
        qrData: jsonEncode({
          'q': shortCode,
          'm': fallback['merchant_id'],
          'a': fallback['amount'],
          'd': fallback['description'],
        }),
        payerUserId: payerUserId,
        paymentMethod: paymentMethod,
      );
      
    } catch (e) {
      _logger.e('SMS fallback payment failed: $e');
      rethrow;
    }
  }

  /// Get merchant QR codes
  Future<List<Map<String, dynamic>>> getMerchantQRCodes(String merchantId) async {
    return await _dbHelper.query(
      'qr_codes',
      where: 'merchant_id = ? AND status = ?',
      whereArgs: [merchantId, 'ACTIVE'],
      orderBy: 'created_at DESC',
    );
  }

  /// Get merchant transaction history
  Future<List<Map<String, dynamic>>> getMerchantTransactions(String merchantId) async {
    return await _dbHelper.query(
      'qr_transactions',
      where: 'merchant_id = ?',
      whereArgs: [merchantId],
      orderBy: 'created_at DESC',
      limit: 100,
    );
  }

  /// Get QR payment statistics
  Future<Map<String, dynamic>> getQRStatistics() async {
    try {
      final allTransactions = await _dbHelper.query('qr_transactions');
      final completedTransactions = allTransactions.where((t) => t['transaction_status'] == 'COMPLETED').length;
      final offlineTransactions = allTransactions.where((t) => (t['offline_mode'] as int) == 1).length;
      final smsTransactions = allTransactions.where((t) => (t['sms_fallback_used'] as int) == 1).length;
      
      final totalAmount = allTransactions
          .where((t) => t['transaction_status'] == 'COMPLETED')
          .fold(0.0, (sum, t) => sum + (t['amount'] as num).toDouble());

      return {
        'total_transactions': allTransactions.length,
        'completed_transactions': completedTransactions,
        'offline_transactions': offlineTransactions,
        'sms_fallback_transactions': smsTransactions,
        'total_amount': totalAmount,
        'success_rate': allTransactions.isNotEmpty 
            ? (completedTransactions / allTransactions.length * 100).toStringAsFixed(1)
            : '0.0',
        'offline_usage_rate': allTransactions.isNotEmpty 
            ? (offlineTransactions / allTransactions.length * 100).toStringAsFixed(1)
            : '0.0',
      };
    } catch (e) {
      _logger.e('Failed to get QR statistics: $e');
      return {};
    }
  }
}
