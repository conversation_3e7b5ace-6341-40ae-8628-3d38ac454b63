import 'dart:convert';
import 'dart:math';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/app_constants.dart';
import '../../data/database/database_helper.dart';
import '../offline_sync/data/offline_sync_manager.dart';

/// SMS Token Service for Rural Resilience
/// Converts transactions to SMS tokens during floods/outages
/// Enables financial transactions via basic SMS on any phone
class SMSTokenService {
  static final SMSTokenService _instance = SMSTokenService._internal();
  factory SMSTokenService() => _instance;
  SMSTokenService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final OfflineSyncManager _syncManager = OfflineSyncManager();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();
  final Random _random = Random.secure();

  // SMS short codes for Zambian networks
  static const Map<String, String> smsShortCodes = {
    'MTN': '1234',
    'AIRTEL': '5678', 
    'ZAMTEL': '9012',
  };

  /// Generate SMS token for transaction during outages
  Future<String> generateSMSToken({
    required String userId,
    required String transactionType,
    required double amount,
    required String recipientPhone,
    String? senderPhone,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final tokenId = _uuid.v4().substring(0, 8).toUpperCase();
      final securityCode = _generateSecurityCode();
      
      // Create SMS token record
      final smsToken = {
        'id': tokenId,
        'user_id': userId,
        'transaction_type': transactionType,
        'amount': amount,
        'sender_phone': senderPhone,
        'recipient_phone': recipientPhone,
        'security_code': securityCode,
        'status': 'ACTIVE',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'expires_at': DateTime.now().add(Duration(hours: 24)).millisecondsSinceEpoch,
        'metadata': jsonEncode(metadata ?? {}),
        'network_provider': _detectNetworkProvider(recipientPhone),
      };

      await _dbHelper.insert('sms_tokens', smsToken);

      // Generate SMS message in local language
      final smsMessage = _generateSMSMessage(
        tokenId: tokenId,
        amount: amount,
        transactionType: transactionType,
        securityCode: securityCode,
      );

      // Queue SMS for sending
      await _queueSMSForSending(recipientPhone, smsMessage);

      _logger.i('SMS token generated: $tokenId for amount K${amount.toStringAsFixed(2)}');
      return tokenId;
      
    } catch (e) {
      _logger.e('Failed to generate SMS token: $e');
      rethrow;
    }
  }

  /// Process SMS token redemption
  Future<Map<String, dynamic>> redeemSMSToken({
    required String tokenId,
    required String securityCode,
    required String redeemingPhone,
  }) async {
    try {
      // Find token
      final tokens = await _dbHelper.query(
        'sms_tokens',
        where: 'id = ? AND status = ?',
        whereArgs: [tokenId, 'ACTIVE'],
        limit: 1,
      );

      if (tokens.isEmpty) {
        throw Exception('Invalid or expired SMS token');
      }

      final token = tokens.first;
      final now = DateTime.now().millisecondsSinceEpoch;
      
      // Check expiration
      if ((token['expires_at'] as int) < now) {
        await _updateTokenStatus(tokenId, 'EXPIRED');
        throw Exception('SMS token has expired');
      }

      // Verify security code
      if (token['security_code'] != securityCode) {
        await _logFailedRedemption(tokenId, redeemingPhone, 'INVALID_CODE');
        throw Exception('Invalid security code');
      }

      // Verify recipient phone
      if (token['recipient_phone'] != redeemingPhone) {
        await _logFailedRedemption(tokenId, redeemingPhone, 'WRONG_PHONE');
        throw Exception('Token can only be redeemed by recipient');
      }

      // Process transaction
      final transactionResult = await _processTokenTransaction(token);
      
      // Mark token as redeemed
      await _updateTokenStatus(tokenId, 'REDEEMED');
      
      // Send confirmation SMS
      await _sendConfirmationSMS(token, transactionResult);

      _logger.i('SMS token redeemed: $tokenId');
      return transactionResult;
      
    } catch (e) {
      _logger.e('SMS token redemption failed: $e');
      rethrow;
    }
  }

  /// Generate SMS message in local languages
  String _generateSMSMessage({
    required String tokenId,
    required double amount,
    required String transactionType,
    required String securityCode,
  }) {
    // Default to English, can be localized based on user preference
    final amountStr = 'K${amount.toStringAsFixed(2)}';
    
    switch (transactionType) {
      case 'SEND_MONEY':
        return 'ZambiaPay: You have received $amountStr. '
               'Token: $tokenId, Code: $securityCode. '
               'Reply with TOKEN $tokenId $securityCode to claim. '
               'Valid for 24hrs. Free SMS.';
               
      case 'BILL_PAYMENT':
        return 'ZambiaPay: Bill payment $amountStr ready. '
               'Token: $tokenId, Code: $securityCode. '
               'Reply with BILL $tokenId $securityCode to process. '
               'Valid for 24hrs.';
               
      case 'AIRTIME_ADVANCE':
        return 'ZambiaPay: Emergency airtime $amountStr approved. '
               'Token: $tokenId, Code: $securityCode. '
               'Reply with AIRTIME $tokenId $securityCode to receive. '
               'Valid for 24hrs.';
               
      default:
        return 'ZambiaPay: Transaction $amountStr ready. '
               'Token: $tokenId, Code: $securityCode. '
               'Reply with CLAIM $tokenId $securityCode. '
               'Valid for 24hrs.';
    }
  }

  /// Generate Bemba/Nyanja SMS messages
  String _generateLocalizedSMS({
    required String tokenId,
    required double amount,
    required String transactionType,
    required String securityCode,
    String language = 'bemba',
  }) {
    final amountStr = 'K${amount.toStringAsFixed(2)}';
    
    if (language == 'bemba') {
      switch (transactionType) {
        case 'SEND_MONEY':
          return 'ZambiaPay: Mwalandile indalama $amountStr. '
                 'Token: $tokenId, Code: $securityCode. '
                 'Yankani TOKEN $tokenId $securityCode ukupokelela. '
                 'Ichilangapo masiku 24.';
        default:
          return 'ZambiaPay: Indalama $amountStr ili ready. '
                 'Token: $tokenId, Code: $securityCode. '
                 'Yankani CLAIM $tokenId $securityCode.';
      }
    } else if (language == 'nyanja') {
      switch (transactionType) {
        case 'SEND_MONEY':
          return 'ZambiaPay: Mwalandira ndalama $amountStr. '
                 'Token: $tokenId, Code: $securityCode. '
                 'Tumizani TOKEN $tokenId $securityCode kuti mulandire. '
                 'Masiku 24 okha.';
        default:
          return 'ZambiaPay: Ndalama $amountStr ili ready. '
                 'Token: $tokenId, Code: $securityCode. '
                 'Tumizani CLAIM $tokenId $securityCode.';
      }
    }
    
    return _generateSMSMessage(
      tokenId: tokenId,
      amount: amount,
      transactionType: transactionType,
      securityCode: securityCode,
    );
  }

  /// Detect network provider from phone number
  String _detectNetworkProvider(String phoneNumber) {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanNumber.startsWith('26096') || cleanNumber.startsWith('096')) {
      return 'MTN';
    } else if (cleanNumber.startsWith('26097') || cleanNumber.startsWith('097')) {
      return 'AIRTEL';
    } else if (cleanNumber.startsWith('26095') || cleanNumber.startsWith('095')) {
      return 'ZAMTEL';
    }
    
    return 'MTN'; // Default fallback
  }

  /// Generate 6-digit security code
  String _generateSecurityCode() {
    return (_random.nextInt(900000) + 100000).toString();
  }

  /// Queue SMS for sending via network provider
  Future<void> _queueSMSForSending(String phoneNumber, String message) async {
    try {
      final provider = _detectNetworkProvider(phoneNumber);
      final shortCode = smsShortCodes[provider] ?? smsShortCodes['MTN']!;
      
      final smsQueue = {
        'id': _uuid.v4(),
        'phone_number': phoneNumber,
        'message': message,
        'provider': provider,
        'short_code': shortCode,
        'status': 'QUEUED',
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'retry_count': 0,
        'max_retries': 3,
      };

      await _dbHelper.insert('sms_queue', smsQueue);
      
      // If online, attempt immediate sending
      if (await _syncManager.isConnected()) {
        await _processSMSQueue();
      }
      
    } catch (e) {
      _logger.e('Failed to queue SMS: $e');
    }
  }

  /// Process SMS queue when connection available
  Future<void> _processSMSQueue() async {
    try {
      final queuedSMS = await _dbHelper.query(
        'sms_queue',
        where: 'status = ? AND retry_count < max_retries',
        whereArgs: ['QUEUED'],
        orderBy: 'created_at ASC',
        limit: 10,
      );

      for (final sms in queuedSMS) {
        try {
          await _sendSMSViaProvider(sms);
          
          await _dbHelper.update(
            'sms_queue',
            {'status': 'SENT', 'sent_at': DateTime.now().millisecondsSinceEpoch},
            where: 'id = ?',
            whereArgs: [sms['id']],
          );
          
        } catch (e) {
          final retryCount = (sms['retry_count'] as int) + 1;
          await _dbHelper.update(
            'sms_queue',
            {'retry_count': retryCount},
            where: 'id = ?',
            whereArgs: [sms['id']],
          );
          
          if (retryCount >= (sms['max_retries'] as int)) {
            await _dbHelper.update(
              'sms_queue',
              {'status': 'FAILED'},
              where: 'id = ?',
              whereArgs: [sms['id']],
            );
          }
        }
      }
    } catch (e) {
      _logger.e('SMS queue processing failed: $e');
    }
  }

  /// Send SMS via network provider API
  Future<void> _sendSMSViaProvider(Map<String, dynamic> sms) async {
    // In production, integrate with actual SMS gateway APIs
    // For now, simulate SMS sending
    await Future.delayed(Duration(milliseconds: 500));
    
    _logger.i('SMS sent to ${sms['phone_number']}: ${sms['message']}');
  }

  /// Process transaction from redeemed token
  Future<Map<String, dynamic>> _processTokenTransaction(Map<String, dynamic> token) async {
    final transactionId = _uuid.v4();
    
    // Create transaction record
    final transaction = {
      'id': transactionId,
      'user_id': token['user_id'],
      'transaction_type': token['transaction_type'],
      'amount': token['amount'],
      'sender_phone': token['sender_phone'],
      'receiver_phone': token['recipient_phone'],
      'status': 'COMPLETED',
      'reference_number': 'SMS-${token['id']}',
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'completed_at': DateTime.now().millisecondsSinceEpoch,
      'metadata': token['metadata'],
    };

    await _dbHelper.insert('transactions', transaction);

    return {
      'transaction_id': transactionId,
      'amount': token['amount'],
      'status': 'COMPLETED',
      'reference': 'SMS-${token['id']}',
      'message': 'Transaction completed successfully via SMS token',
    };
  }

  /// Update token status
  Future<void> _updateTokenStatus(String tokenId, String status) async {
    await _dbHelper.update(
      'sms_tokens',
      {
        'status': status,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [tokenId],
    );
  }

  /// Log failed redemption attempt
  Future<void> _logFailedRedemption(String tokenId, String phone, String reason) async {
    final failureLog = {
      'id': _uuid.v4(),
      'token_id': tokenId,
      'phone_number': phone,
      'failure_reason': reason,
      'attempted_at': DateTime.now().millisecondsSinceEpoch,
    };

    await _dbHelper.insert('sms_token_failures', failureLog);
  }

  /// Send confirmation SMS
  Future<void> _sendConfirmationSMS(Map<String, dynamic> token, Map<String, dynamic> result) async {
    final confirmationMessage = 'ZambiaPay: Transaction completed! '
                               'Amount: K${(token['amount'] as num).toStringAsFixed(2)}, '
                               'Ref: ${result['reference']}. '
                               'Thank you for using ZambiaPay.';

    await _queueSMSForSending(token['recipient_phone'], confirmationMessage);
  }

  /// Get SMS token statistics
  Future<Map<String, dynamic>> getTokenStatistics() async {
    try {
      final totalTokens = await _dbHelper.query('sms_tokens');
      final activeTokens = await _dbHelper.query(
        'sms_tokens',
        where: 'status = ?',
        whereArgs: ['ACTIVE'],
      );
      final redeemedTokens = await _dbHelper.query(
        'sms_tokens',
        where: 'status = ?',
        whereArgs: ['REDEEMED'],
      );

      return {
        'total_tokens': totalTokens.length,
        'active_tokens': activeTokens.length,
        'redeemed_tokens': redeemedTokens.length,
        'redemption_rate': totalTokens.isNotEmpty 
            ? (redeemedTokens.length / totalTokens.length * 100).toStringAsFixed(1)
            : '0.0',
      };
    } catch (e) {
      _logger.e('Failed to get token statistics: $e');
      return {};
    }
  }

  /// Emergency mode: Convert all pending transactions to SMS tokens
  Future<List<String>> activateEmergencyMode(String userId) async {
    try {
      final pendingTransactions = await _dbHelper.query(
        'transactions',
        where: 'user_id = ? AND status = ?',
        whereArgs: [userId, 'PENDING'],
      );

      final tokenIds = <String>[];
      
      for (final transaction in pendingTransactions) {
        final tokenId = await generateSMSToken(
          userId: userId,
          transactionType: transaction['transaction_type'],
          amount: transaction['amount'],
          recipientPhone: transaction['receiver_phone'],
          senderPhone: transaction['sender_phone'],
          metadata: jsonDecode(transaction['metadata'] ?? '{}'),
        );
        
        tokenIds.add(tokenId);
      }

      _logger.i('Emergency mode activated: ${tokenIds.length} transactions converted to SMS tokens');
      return tokenIds;
      
    } catch (e) {
      _logger.e('Emergency mode activation failed: $e');
      return [];
    }
  }
}
