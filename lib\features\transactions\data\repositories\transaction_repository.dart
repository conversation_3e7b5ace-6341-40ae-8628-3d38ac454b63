import 'package:logger/logger.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../data/database/database_helper.dart';
import '../../mobile_money/data/models/transaction_model.dart';
import '../../mobile_money/data/services/mobile_money_service.dart';

/// Repository for managing transaction data and operations
/// Handles local storage, API calls, and offline synchronization
class TransactionRepository {
  static final TransactionRepository _instance = TransactionRepository._internal();
  factory TransactionRepository() => _instance;
  TransactionRepository._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final MobileMoneyService _mobileMoneyService = MobileMoneyService();
  final Logger _logger = Logger();

  /// Save transaction to local database
  Future<void> saveTransaction(TransactionModel transaction) async {
    try {
      await _dbHelper.insert(
        AppConstants.transactionsTable,
        transaction.toDatabase(),
      );
      _logger.i('Transaction saved: ${transaction.id}');
    } catch (e) {
      _logger.e('Failed to save transaction: $e');
      rethrow;
    }
  }

  /// Update transaction in database
  Future<void> updateTransaction(TransactionModel transaction) async {
    try {
      await _dbHelper.update(
        AppConstants.transactionsTable,
        transaction.toDatabase(),
        where: 'id = ?',
        whereArgs: [transaction.id],
      );
      _logger.i('Transaction updated: ${transaction.id}');
    } catch (e) {
      _logger.e('Failed to update transaction: $e');
      rethrow;
    }
  }

  /// Get transaction by ID
  Future<TransactionModel?> getTransactionById(String transactionId) async {
    try {
      final results = await _dbHelper.query(
        AppConstants.transactionsTable,
        where: 'id = ?',
        whereArgs: [transactionId],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return TransactionModel.fromDatabase(results.first);
      }
      return null;
    } catch (e) {
      _logger.e('Failed to get transaction: $e');
      return null;
    }
  }

  /// Get transactions for a user
  Future<List<TransactionModel>> getUserTransactions({
    required String userId,
    int? limit,
    int? offset,
    String? status,
    String? transactionType,
  }) async {
    try {
      String whereClause = 'user_id = ?';
      List<dynamic> whereArgs = [userId];

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (transactionType != null) {
        whereClause += ' AND transaction_type = ?';
        whereArgs.add(transactionType);
      }

      final results = await _dbHelper.query(
        AppConstants.transactionsTable,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
        limit: limit,
        offset: offset,
      );

      return results.map((data) => TransactionModel.fromDatabase(data)).toList();
    } catch (e) {
      _logger.e('Failed to get user transactions: $e');
      return [];
    }
  }

  /// Send money transaction
  Future<TransactionModel> sendMoney({
    required String userId,
    required String senderPhone,
    required String receiverPhone,
    required double amount,
    String? description,
  }) async {
    try {
      // Validate amount
      final provider = _mobileMoneyService.detectProvider(receiverPhone);
      if (!_mobileMoneyService.validateAmount(provider, amount)) {
        throw Exception('Invalid transaction amount');
      }

      // Execute transaction
      final transaction = await _mobileMoneyService.sendMoney(
        userId: userId,
        senderPhone: senderPhone,
        receiverPhone: receiverPhone,
        amount: amount,
        description: description,
      );

      // Save to database
      await saveTransaction(transaction);

      return transaction;
    } catch (e) {
      _logger.e('Send money failed: $e');
      rethrow;
    }
  }

  /// Get transaction statistics
  Future<Map<String, dynamic>> getTransactionStats(String userId) async {
    try {
      final allTransactions = await getUserTransactions(userId: userId);
      
      final completed = allTransactions.where(
        (t) => t.status == AppConstants.statusCompleted,
      ).toList();
      
      final pending = allTransactions.where(
        (t) => t.status == AppConstants.statusPending,
      ).toList();
      
      final failed = allTransactions.where(
        (t) => t.status == AppConstants.statusFailed,
      ).toList();

      final totalSent = completed
          .where((t) => t.transactionType == AppConstants.transactionTypeSend)
          .fold(0.0, (sum, t) => sum + t.amount);

      final totalReceived = completed
          .where((t) => t.transactionType == AppConstants.transactionTypeReceive)
          .fold(0.0, (sum, t) => sum + t.amount);

      return {
        'totalTransactions': allTransactions.length,
        'completedTransactions': completed.length,
        'pendingTransactions': pending.length,
        'failedTransactions': failed.length,
        'totalAmountSent': totalSent,
        'totalAmountReceived': totalReceived,
        'totalFeesPaid': completed.fold(0.0, (sum, t) => sum + t.fee),
      };
    } catch (e) {
      _logger.e('Failed to get transaction stats: $e');
      return {};
    }
  }

  /// Get recent transactions
  Future<List<TransactionModel>> getRecentTransactions({
    required String userId,
    int limit = 10,
  }) async {
    return await getUserTransactions(
      userId: userId,
      limit: limit,
    );
  }

  /// Search transactions
  Future<List<TransactionModel>> searchTransactions({
    required String userId,
    String? query,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? provider,
  }) async {
    try {
      String whereClause = 'user_id = ?';
      List<dynamic> whereArgs = [userId];

      if (query != null && query.isNotEmpty) {
        whereClause += ' AND (description LIKE ? OR receiver_phone LIKE ? OR sender_phone LIKE ?)';
        final searchPattern = '%$query%';
        whereArgs.addAll([searchPattern, searchPattern, searchPattern]);
      }

      if (startDate != null) {
        whereClause += ' AND created_at >= ?';
        whereArgs.add(startDate.millisecondsSinceEpoch);
      }

      if (endDate != null) {
        whereClause += ' AND created_at <= ?';
        whereArgs.add(endDate.millisecondsSinceEpoch);
      }

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (provider != null) {
        whereClause += ' AND provider = ?';
        whereArgs.add(provider);
      }

      final results = await _dbHelper.query(
        AppConstants.transactionsTable,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'created_at DESC',
      );

      return results.map((data) => TransactionModel.fromDatabase(data)).toList();
    } catch (e) {
      _logger.e('Failed to search transactions: $e');
      return [];
    }
  }

  /// Delete transaction
  Future<void> deleteTransaction(String transactionId) async {
    try {
      await _dbHelper.delete(
        AppConstants.transactionsTable,
        where: 'id = ?',
        whereArgs: [transactionId],
      );
      _logger.i('Transaction deleted: $transactionId');
    } catch (e) {
      _logger.e('Failed to delete transaction: $e');
      rethrow;
    }
  }

  /// Get transactions by date range
  Future<List<TransactionModel>> getTransactionsByDateRange({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await searchTransactions(
      userId: userId,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Get transactions by provider
  Future<List<TransactionModel>> getTransactionsByProvider({
    required String userId,
    required String provider,
  }) async {
    return await getUserTransactions(
      userId: userId,
      status: null,
      transactionType: null,
    ).then((transactions) => 
      transactions.where((t) => t.provider == provider).toList()
    );
  }

  /// Get pending transactions count
  Future<int> getPendingTransactionsCount(String userId) async {
    final pending = await getUserTransactions(
      userId: userId,
      status: AppConstants.statusPending,
    );
    return pending.length;
  }

  /// Get failed transactions count
  Future<int> getFailedTransactionsCount(String userId) async {
    final failed = await getUserTransactions(
      userId: userId,
      status: AppConstants.statusFailed,
    );
    return failed.length;
  }

  /// Retry failed transaction
  Future<TransactionModel> retryTransaction(String transactionId) async {
    try {
      final transaction = await getTransactionById(transactionId);
      if (transaction == null) {
        throw Exception('Transaction not found');
      }

      if (transaction.status != AppConstants.statusFailed) {
        throw Exception('Only failed transactions can be retried');
      }

      // Create new transaction with same details
      final newTransaction = await sendMoney(
        userId: transaction.userId,
        senderPhone: transaction.senderPhone!,
        receiverPhone: transaction.receiverPhone!,
        amount: transaction.amount,
        description: transaction.description,
      );

      return newTransaction;
    } catch (e) {
      _logger.e('Failed to retry transaction: $e');
      rethrow;
    }
  }
}
