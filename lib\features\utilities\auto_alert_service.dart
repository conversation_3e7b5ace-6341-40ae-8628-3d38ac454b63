import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../core/constants/app_constants.dart';
import '../../data/database/database_helper.dart';
import '../rural_resilience/sms_token_service.dart';
import 'data/services/zesco_api_service.dart';

/// Auto Alert Service for Utility Pain Relief
/// Proactive notifications before ZESCO/NWSC service disconnections
/// SMS alerts for rural users with payment options
class AutoAlertService {
  static final AutoAlertService _instance = AutoAlertService._internal();
  factory AutoAlertService() => _instance;
  AutoAlertService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final SMSTokenService _smsTokenService = SMSTokenService();
  final ZESCOApiService _zescoService = ZESCOApiService();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  // Alert thresholds (days before disconnection)
  static const Map<String, int> alertThresholds = {
    'FIRST_NOTICE': 7,    // 7 days before
    'FINAL_NOTICE': 3,    // 3 days before
    'URGENT_NOTICE': 1,   // 1 day before
    'DISCONNECTION': 0,   // Day of disconnection
  };

  /// Initialize auto alert monitoring
  Future<void> initialize() async {
    await _createAlertTables();
    await _scheduleAlertChecks();
    _logger.i('Auto alert service initialized');
  }

  /// Create alert tracking tables
  Future<void> _createAlertTables() async {
    final db = await _dbHelper.database;

    // Utility alerts table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS utility_alerts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        utility_provider TEXT NOT NULL,
        account_number TEXT NOT NULL,
        alert_type TEXT NOT NULL,
        amount_due REAL NOT NULL,
        due_date INTEGER NOT NULL,
        disconnection_date INTEGER,
        alert_sent_at INTEGER,
        payment_link_token TEXT,
        status TEXT DEFAULT 'PENDING',
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000)
      )
    ''');

    // Alert preferences table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS alert_preferences (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        utility_provider TEXT NOT NULL,
        account_number TEXT NOT NULL,
        sms_alerts_enabled INTEGER DEFAULT 1,
        email_alerts_enabled INTEGER DEFAULT 0,
        push_alerts_enabled INTEGER DEFAULT 1,
        advance_notice_days INTEGER DEFAULT 7,
        auto_payment_enabled INTEGER DEFAULT 0,
        preferred_language TEXT DEFAULT 'en',
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000)
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX IF NOT EXISTS idx_utility_alerts_user_id ON utility_alerts (user_id)');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_utility_alerts_due_date ON utility_alerts (due_date)');
  }

  /// Register utility account for monitoring
  Future<void> registerUtilityAccount({
    required String userId,
    required String utilityProvider,
    required String accountNumber,
    int advanceNoticeDays = 7,
    bool smsAlertsEnabled = true,
    bool autoPaymentEnabled = false,
    String preferredLanguage = 'en',
  }) async {
    try {
      final preference = {
        'id': _uuid.v4(),
        'user_id': userId,
        'utility_provider': utilityProvider,
        'account_number': accountNumber,
        'sms_alerts_enabled': smsAlertsEnabled ? 1 : 0,
        'push_alerts_enabled': 1,
        'advance_notice_days': advanceNoticeDays,
        'auto_payment_enabled': autoPaymentEnabled ? 1 : 0,
        'preferred_language': preferredLanguage,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('alert_preferences', preference);
      
      _logger.i('Utility account registered for alerts: $utilityProvider - $accountNumber');
    } catch (e) {
      _logger.e('Failed to register utility account: $e');
      rethrow;
    }
  }

  /// Check for upcoming bills and send alerts
  Future<void> checkAndSendAlerts() async {
    try {
      final preferences = await _dbHelper.query('alert_preferences');
      
      for (final pref in preferences) {
        await _checkUtilityBills(pref);
      }
      
      _logger.i('Alert check completed for ${preferences.length} accounts');
    } catch (e) {
      _logger.e('Alert check failed: $e');
    }
  }

  /// Check bills for specific utility account
  Future<void> _checkUtilityBills(Map<String, dynamic> preference) async {
    try {
      final utilityProvider = preference['utility_provider'] as String;
      final accountNumber = preference['account_number'] as String;
      final userId = preference['user_id'] as String;
      
      // Get bill information from utility provider
      Map<String, dynamic>? billInfo;
      
      switch (utilityProvider.toUpperCase()) {
        case 'ZESCO':
          billInfo = await _getZESCOBillInfo(accountNumber);
          break;
        case 'NWSC':
        case 'LWSC':
          billInfo = await _getWaterBillInfo(accountNumber, utilityProvider);
          break;
        default:
          _logger.w('Unsupported utility provider: $utilityProvider');
          return;
      }

      if (billInfo != null && billInfo['amount_due'] > 0) {
        await _processUtilityAlert(preference, billInfo);
      }
      
    } catch (e) {
      _logger.e('Failed to check utility bills for ${preference['account_number']}: $e');
    }
  }

  /// Get ZESCO bill information
  Future<Map<String, dynamic>?> _getZESCOBillInfo(String accountNumber) async {
    try {
      final billInquiry = await _zescoService.inquireBill(accountNumber);
      
      return {
        'amount_due': billInquiry.amountDue,
        'due_date': billInquiry.dueDate?.millisecondsSinceEpoch,
        'customer_name': billInquiry.customerName,
        'bill_period': billInquiry.billPeriod,
        'status': billInquiry.status,
      };
    } catch (e) {
      _logger.e('Failed to get ZESCO bill info: $e');
      return null;
    }
  }

  /// Get water bill information (NWSC/LWSC)
  Future<Map<String, dynamic>?> _getWaterBillInfo(String accountNumber, String provider) async {
    // Simulate water bill inquiry
    // In production, integrate with actual water utility APIs
    
    return {
      'amount_due': 150.0 + (accountNumber.hashCode % 500), // Simulated amount
      'due_date': DateTime.now().add(Duration(days: 5)).millisecondsSinceEpoch,
      'customer_name': 'Customer ${accountNumber.substring(0, 4)}',
      'bill_period': 'March 2024',
      'status': 'UNPAID',
    };
  }

  /// Process utility alert based on due date
  Future<void> _processUtilityAlert(
    Map<String, dynamic> preference,
    Map<String, dynamic> billInfo,
  ) async {
    try {
      final userId = preference['user_id'] as String;
      final utilityProvider = preference['utility_provider'] as String;
      final accountNumber = preference['account_number'] as String;
      final amountDue = billInfo['amount_due'] as double;
      final dueDate = billInfo['due_date'] as int?;
      
      if (dueDate == null) return;

      final now = DateTime.now().millisecondsSinceEpoch;
      final daysUntilDue = ((dueDate - now) / (24 * 60 * 60 * 1000)).ceil();
      
      // Determine alert type based on days until due
      String? alertType;
      if (daysUntilDue <= 0) {
        alertType = 'DISCONNECTION';
      } else if (daysUntilDue <= 1) {
        alertType = 'URGENT_NOTICE';
      } else if (daysUntilDue <= 3) {
        alertType = 'FINAL_NOTICE';
      } else if (daysUntilDue <= 7) {
        alertType = 'FIRST_NOTICE';
      }

      if (alertType != null) {
        // Check if alert already sent
        final existingAlerts = await _dbHelper.query(
          'utility_alerts',
          where: 'user_id = ? AND account_number = ? AND alert_type = ? AND due_date = ?',
          whereArgs: [userId, accountNumber, alertType, dueDate],
        );

        if (existingAlerts.isEmpty) {
          await _sendUtilityAlert(preference, billInfo, alertType, daysUntilDue);
        }
      }
      
    } catch (e) {
      _logger.e('Failed to process utility alert: $e');
    }
  }

  /// Send utility alert via SMS/Push
  Future<void> _sendUtilityAlert(
    Map<String, dynamic> preference,
    Map<String, dynamic> billInfo,
    String alertType,
    int daysUntilDue,
  ) async {
    try {
      final userId = preference['user_id'] as String;
      final utilityProvider = preference['utility_provider'] as String;
      final accountNumber = preference['account_number'] as String;
      final amountDue = billInfo['amount_due'] as double;
      final preferredLanguage = preference['preferred_language'] as String;
      final smsEnabled = (preference['sms_alerts_enabled'] as int) == 1;
      
      // Generate payment token for easy payment
      final paymentToken = await _generatePaymentToken(
        userId: userId,
        utilityProvider: utilityProvider,
        accountNumber: accountNumber,
        amount: amountDue,
      );

      // Create alert record
      final alertId = _uuid.v4();
      final alert = {
        'id': alertId,
        'user_id': userId,
        'utility_provider': utilityProvider,
        'account_number': accountNumber,
        'alert_type': alertType,
        'amount_due': amountDue,
        'due_date': billInfo['due_date'],
        'disconnection_date': _calculateDisconnectionDate(billInfo['due_date']),
        'payment_link_token': paymentToken,
        'status': 'SENT',
        'alert_sent_at': DateTime.now().millisecondsSinceEpoch,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      };

      await _dbHelper.insert('utility_alerts', alert);

      // Send SMS alert if enabled
      if (smsEnabled) {
        await _sendSMSAlert(
          userId: userId,
          utilityProvider: utilityProvider,
          accountNumber: accountNumber,
          amountDue: amountDue,
          daysUntilDue: daysUntilDue,
          alertType: alertType,
          paymentToken: paymentToken,
          language: preferredLanguage,
        );
      }

      _logger.i('Utility alert sent: $alertType for $utilityProvider - $accountNumber');
      
    } catch (e) {
      _logger.e('Failed to send utility alert: $e');
    }
  }

  /// Send SMS alert in local language
  Future<void> _sendSMSAlert({
    required String userId,
    required String utilityProvider,
    required String accountNumber,
    required double amountDue,
    required int daysUntilDue,
    required String alertType,
    required String paymentToken,
    required String language,
  }) async {
    try {
      // Get user phone number
      final users = await _dbHelper.query(
        AppConstants.usersTable,
        where: 'id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      if (users.isEmpty) return;

      final userPhone = users.first['phone_number'] as String?;
      if (userPhone == null) return;

      // Generate localized SMS message
      final smsMessage = _generateLocalizedAlertSMS(
        utilityProvider: utilityProvider,
        accountNumber: accountNumber,
        amountDue: amountDue,
        daysUntilDue: daysUntilDue,
        alertType: alertType,
        paymentToken: paymentToken,
        language: language,
      );

      // Send SMS via SMS token service
      await _smsTokenService._queueSMSForSending(userPhone, smsMessage);
      
    } catch (e) {
      _logger.e('Failed to send SMS alert: $e');
    }
  }

  /// Generate localized alert SMS
  String _generateLocalizedAlertSMS({
    required String utilityProvider,
    required String accountNumber,
    required double amountDue,
    required int daysUntilDue,
    required String alertType,
    required String paymentToken,
    required String language,
  }) {
    final amountStr = 'K${amountDue.toStringAsFixed(2)}';
    final shortAccount = accountNumber.length > 6 
        ? '***${accountNumber.substring(accountNumber.length - 4)}'
        : accountNumber;

    if (language == 'bemba') {
      switch (alertType) {
        case 'URGENT_NOTICE':
          return 'ZambiaPay: $utilityProvider bill $amountStr for account $shortAccount '
                 'ichakwabula lelo! Pay now: Reply PAY $paymentToken. '
                 'Ukwabula teti muchinjilwe umeme/amenshi.';
        case 'FINAL_NOTICE':
          return 'ZambiaPay: $utilityProvider bill $amountStr for account $shortAccount '
                 'ichakwabula mu masiku $daysUntilDue. Pay: Reply PAY $paymentToken. '
                 'Lipileni nomba teti muchinjilwe.';
        default:
          return 'ZambiaPay: $utilityProvider bill $amountStr for account $shortAccount '
                 'ichakwabula mu masiku $daysUntilDue. Pay: Reply PAY $paymentToken.';
      }
    } else if (language == 'nyanja') {
      switch (alertType) {
        case 'URGENT_NOTICE':
          return 'ZambiaPay: $utilityProvider bill $amountStr ya account $shortAccount '
                 'ikwabula lero! Lipireni: Reply PAY $paymentToken. '
                 'Osalipira adzakudulani magetsi/madzi.';
        case 'FINAL_NOTICE':
          return 'ZambiaPay: $utilityProvider bill $amountStr ya account $shortAccount '
                 'ikwabula masiku $daysUntilDue. Lipira: Reply PAY $paymentToken. '
                 'Lipireni kapena adzakudulani.';
        default:
          return 'ZambiaPay: $utilityProvider bill $amountStr ya account $shortAccount '
                 'ikwabula masiku $daysUntilDue. Lipira: Reply PAY $paymentToken.';
      }
    } else {
      // English
      switch (alertType) {
        case 'URGENT_NOTICE':
          return 'ZambiaPay: Your $utilityProvider bill of $amountStr for account $shortAccount '
                 'is due TODAY! Pay now: Reply PAY $paymentToken. '
                 'Avoid disconnection - pay immediately.';
        case 'FINAL_NOTICE':
          return 'ZambiaPay: FINAL NOTICE - Your $utilityProvider bill of $amountStr '
                 'for account $shortAccount is due in $daysUntilDue days. '
                 'Pay: Reply PAY $paymentToken. Avoid disconnection.';
        case 'DISCONNECTION':
          return 'ZambiaPay: URGENT - Your $utilityProvider service for account $shortAccount '
                 'may be disconnected today. Outstanding: $amountStr. '
                 'Pay immediately: Reply PAY $paymentToken.';
        default:
          return 'ZambiaPay: Your $utilityProvider bill of $amountStr for account $shortAccount '
                 'is due in $daysUntilDue days. Pay easily: Reply PAY $paymentToken.';
      }
    }
  }

  /// Generate payment token for quick payment
  Future<String> _generatePaymentToken({
    required String userId,
    required String utilityProvider,
    required String accountNumber,
    required double amount,
  }) async {
    try {
      final tokenId = await _smsTokenService.generateSMSToken(
        userId: userId,
        transactionType: 'BILL_PAYMENT',
        amount: amount,
        recipientPhone: accountNumber, // Use account as identifier
        metadata: {
          'utility_provider': utilityProvider,
          'account_number': accountNumber,
          'bill_type': 'UTILITY',
        },
      );

      return tokenId;
    } catch (e) {
      _logger.e('Failed to generate payment token: $e');
      return 'ERROR';
    }
  }

  /// Calculate disconnection date (typically 3-5 days after due date)
  int _calculateDisconnectionDate(int dueDate) {
    return dueDate + (4 * 24 * 60 * 60 * 1000); // 4 days after due date
  }

  /// Schedule periodic alert checks
  Future<void> _scheduleAlertChecks() async {
    // In production, this would use a proper scheduler
    // For now, we'll simulate periodic checks
    _logger.i('Alert checks scheduled - run checkAndSendAlerts() periodically');
  }

  /// Get user's alert history
  Future<List<Map<String, dynamic>>> getUserAlerts(String userId) async {
    return await _dbHelper.query(
      'utility_alerts',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
      limit: 50,
    );
  }

  /// Update alert preferences
  Future<void> updateAlertPreferences({
    required String userId,
    required String utilityProvider,
    required String accountNumber,
    bool? smsAlertsEnabled,
    bool? autoPaymentEnabled,
    int? advanceNoticeDays,
    String? preferredLanguage,
  }) async {
    try {
      final updates = <String, dynamic>{};
      
      if (smsAlertsEnabled != null) {
        updates['sms_alerts_enabled'] = smsAlertsEnabled ? 1 : 0;
      }
      if (autoPaymentEnabled != null) {
        updates['auto_payment_enabled'] = autoPaymentEnabled ? 1 : 0;
      }
      if (advanceNoticeDays != null) {
        updates['advance_notice_days'] = advanceNoticeDays;
      }
      if (preferredLanguage != null) {
        updates['preferred_language'] = preferredLanguage;
      }

      if (updates.isNotEmpty) {
        await _dbHelper.update(
          'alert_preferences',
          updates,
          where: 'user_id = ? AND utility_provider = ? AND account_number = ?',
          whereArgs: [userId, utilityProvider, accountNumber],
        );
      }
      
    } catch (e) {
      _logger.e('Failed to update alert preferences: $e');
      rethrow;
    }
  }

  /// Get alert statistics
  Future<Map<String, dynamic>> getAlertStatistics() async {
    try {
      final allAlerts = await _dbHelper.query('utility_alerts');
      final sentAlerts = allAlerts.where((a) => a['status'] == 'SENT').length;
      
      final alertsByType = <String, int>{};
      for (final alert in allAlerts) {
        final type = alert['alert_type'] as String;
        alertsByType[type] = (alertsByType[type] ?? 0) + 1;
      }

      return {
        'total_alerts': allAlerts.length,
        'sent_alerts': sentAlerts,
        'alerts_by_type': alertsByType,
        'active_accounts': (await _dbHelper.query('alert_preferences')).length,
      };
    } catch (e) {
      _logger.e('Failed to get alert statistics: $e');
      return {};
    }
  }
}
