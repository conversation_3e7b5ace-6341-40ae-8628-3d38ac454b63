import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'utility_bill_model.g.dart';

/// Utility bill model for ZESCO, water, and other utility payments
@JsonSerializable()
class UtilityBillModel extends Equatable {
  final String id;
  final String userId;
  final String provider;
  final String accountNumber;
  final String? customerName;
  final String utilityType;
  final double? amountDue;
  final DateTime? dueDate;
  final String? billPeriod;
  final String status;
  final DateTime lastUpdated;
  final bool autoPayEnabled;
  final Map<String, dynamic>? metadata;

  const UtilityBillModel({
    required this.id,
    required this.userId,
    required this.provider,
    required this.accountNumber,
    this.customerName,
    required this.utilityType,
    this.amountDue,
    this.dueDate,
    this.billPeriod,
    this.status = 'UNPAID',
    required this.lastUpdated,
    this.autoPayEnabled = false,
    this.metadata,
  });

  factory UtilityBillModel.fromJson(Map<String, dynamic> json) =>
      _$UtilityBillModelFromJson(json);

  Map<String, dynamic> toJson() => _$UtilityBillModelToJson(this);

  factory UtilityBillModel.fromDatabase(Map<String, dynamic> map) {
    return UtilityBillModel(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      provider: map['provider'] as String,
      accountNumber: map['account_number'] as String,
      customerName: map['customer_name'] as String?,
      utilityType: map['utility_type'] as String,
      amountDue: (map['amount_due'] as num?)?.toDouble(),
      dueDate: map['due_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['due_date'] as int)
          : null,
      billPeriod: map['bill_period'] as String?,
      status: map['status'] as String? ?? 'UNPAID',
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(map['last_updated'] as int),
      autoPayEnabled: (map['auto_pay_enabled'] as int? ?? 0) == 1,
      metadata: map['metadata'] != null 
          ? Map<String, dynamic>.from(map['metadata'] as Map)
          : null,
    );
  }

  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'user_id': userId,
      'provider': provider,
      'account_number': accountNumber,
      'customer_name': customerName,
      'utility_type': utilityType,
      'amount_due': amountDue,
      'due_date': dueDate?.millisecondsSinceEpoch,
      'bill_period': billPeriod,
      'status': status,
      'last_updated': lastUpdated.millisecondsSinceEpoch,
      'auto_pay_enabled': autoPayEnabled ? 1 : 0,
      'metadata': metadata,
    };
  }

  UtilityBillModel copyWith({
    String? id,
    String? userId,
    String? provider,
    String? accountNumber,
    String? customerName,
    String? utilityType,
    double? amountDue,
    DateTime? dueDate,
    String? billPeriod,
    String? status,
    DateTime? lastUpdated,
    bool? autoPayEnabled,
    Map<String, dynamic>? metadata,
  }) {
    return UtilityBillModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      provider: provider ?? this.provider,
      accountNumber: accountNumber ?? this.accountNumber,
      customerName: customerName ?? this.customerName,
      utilityType: utilityType ?? this.utilityType,
      amountDue: amountDue ?? this.amountDue,
      dueDate: dueDate ?? this.dueDate,
      billPeriod: billPeriod ?? this.billPeriod,
      status: status ?? this.status,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      autoPayEnabled: autoPayEnabled ?? this.autoPayEnabled,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        provider,
        accountNumber,
        customerName,
        utilityType,
        amountDue,
        dueDate,
        billPeriod,
        status,
        lastUpdated,
        autoPayEnabled,
        metadata,
      ];

  @override
  String toString() {
    return 'UtilityBillModel(id: $id, provider: $provider, account: $accountNumber, amount: $amountDue, status: $status)';
  }
}

/// Utility provider model
@JsonSerializable()
class UtilityProvider extends Equatable {
  final String code;
  final String name;
  final String utilityType;
  final String apiEndpoint;
  final List<String> supportedRegions;
  final bool isActive;
  final Map<String, dynamic>? configuration;

  const UtilityProvider({
    required this.code,
    required this.name,
    required this.utilityType,
    required this.apiEndpoint,
    this.supportedRegions = const [],
    this.isActive = true,
    this.configuration,
  });

  factory UtilityProvider.fromJson(Map<String, dynamic> json) =>
      _$UtilityProviderFromJson(json);

  Map<String, dynamic> toJson() => _$UtilityProviderToJson(this);

  @override
  List<Object?> get props => [
        code,
        name,
        utilityType,
        apiEndpoint,
        supportedRegions,
        isActive,
        configuration,
      ];
}

/// Bill payment request model
@JsonSerializable()
class BillPaymentRequest extends Equatable {
  final String billId;
  final String accountNumber;
  final double amount;
  final String paymentMethod;
  final String? customerPhone;
  final String? reference;
  final Map<String, dynamic>? additionalData;

  const BillPaymentRequest({
    required this.billId,
    required this.accountNumber,
    required this.amount,
    required this.paymentMethod,
    this.customerPhone,
    this.reference,
    this.additionalData,
  });

  factory BillPaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$BillPaymentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BillPaymentRequestToJson(this);

  @override
  List<Object?> get props => [
        billId,
        accountNumber,
        amount,
        paymentMethod,
        customerPhone,
        reference,
        additionalData,
      ];
}

/// Bill payment response model
@JsonSerializable()
class BillPaymentResponse extends Equatable {
  final String transactionId;
  final String status;
  final String? receiptNumber;
  final double amountPaid;
  final double? balance;
  final DateTime paymentDate;
  final String? message;

  const BillPaymentResponse({
    required this.transactionId,
    required this.status,
    this.receiptNumber,
    required this.amountPaid,
    this.balance,
    required this.paymentDate,
    this.message,
  });

  factory BillPaymentResponse.fromJson(Map<String, dynamic> json) =>
      _$BillPaymentResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BillPaymentResponseToJson(this);

  @override
  List<Object?> get props => [
        transactionId,
        status,
        receiptNumber,
        amountPaid,
        balance,
        paymentDate,
        message,
      ];
}

/// Bill inquiry request model
@JsonSerializable()
class BillInquiryRequest extends Equatable {
  final String provider;
  final String accountNumber;
  final String? customerPhone;
  final String? billPeriod;

  const BillInquiryRequest({
    required this.provider,
    required this.accountNumber,
    this.customerPhone,
    this.billPeriod,
  });

  factory BillInquiryRequest.fromJson(Map<String, dynamic> json) =>
      _$BillInquiryRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BillInquiryRequestToJson(this);

  @override
  List<Object?> get props => [
        provider,
        accountNumber,
        customerPhone,
        billPeriod,
      ];
}

/// Bill inquiry response model
@JsonSerializable()
class BillInquiryResponse extends Equatable {
  final String accountNumber;
  final String customerName;
  final double amountDue;
  final DateTime? dueDate;
  final String billPeriod;
  final String status;
  final Map<String, dynamic>? additionalInfo;

  const BillInquiryResponse({
    required this.accountNumber,
    required this.customerName,
    required this.amountDue,
    this.dueDate,
    required this.billPeriod,
    required this.status,
    this.additionalInfo,
  });

  factory BillInquiryResponse.fromJson(Map<String, dynamic> json) =>
      _$BillInquiryResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BillInquiryResponseToJson(this);

  @override
  List<Object?> get props => [
        accountNumber,
        customerName,
        amountDue,
        dueDate,
        billPeriod,
        status,
        additionalInfo,
      ];
}
