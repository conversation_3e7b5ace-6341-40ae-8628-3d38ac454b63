// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'utility_bill_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UtilityBillModel _$UtilityBillModelFromJson(Map<String, dynamic> json) =>
    UtilityBillModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      provider: json['provider'] as String,
      accountNumber: json['accountNumber'] as String,
      customerName: json['customerName'] as String?,
      utilityType: json['utilityType'] as String,
      amountDue: (json['amountDue'] as num?)?.toDouble(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      billPeriod: json['billPeriod'] as String?,
      status: json['status'] as String? ?? 'UNPAID',
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      autoPayEnabled: json['autoPayEnabled'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UtilityBillModelToJson(UtilityBillModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'provider': instance.provider,
      'accountNumber': instance.accountNumber,
      'customerName': instance.customerName,
      'utilityType': instance.utilityType,
      'amountDue': instance.amountDue,
      'dueDate': instance.dueDate?.toIso8601String(),
      'billPeriod': instance.billPeriod,
      'status': instance.status,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'autoPayEnabled': instance.autoPayEnabled,
      'metadata': instance.metadata,
    };

UtilityProvider _$UtilityProviderFromJson(Map<String, dynamic> json) =>
    UtilityProvider(
      code: json['code'] as String,
      name: json['name'] as String,
      utilityType: json['utilityType'] as String,
      apiEndpoint: json['apiEndpoint'] as String,
      supportedRegions: (json['supportedRegions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isActive: json['isActive'] as bool? ?? true,
      configuration: json['configuration'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UtilityProviderToJson(UtilityProvider instance) =>
    <String, dynamic>{
      'code': instance.code,
      'name': instance.name,
      'utilityType': instance.utilityType,
      'apiEndpoint': instance.apiEndpoint,
      'supportedRegions': instance.supportedRegions,
      'isActive': instance.isActive,
      'configuration': instance.configuration,
    };

BillPaymentRequest _$BillPaymentRequestFromJson(Map<String, dynamic> json) =>
    BillPaymentRequest(
      billId: json['billId'] as String,
      accountNumber: json['accountNumber'] as String,
      amount: (json['amount'] as num).toDouble(),
      paymentMethod: json['paymentMethod'] as String,
      customerPhone: json['customerPhone'] as String?,
      reference: json['reference'] as String?,
      additionalData: json['additionalData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$BillPaymentRequestToJson(BillPaymentRequest instance) =>
    <String, dynamic>{
      'billId': instance.billId,
      'accountNumber': instance.accountNumber,
      'amount': instance.amount,
      'paymentMethod': instance.paymentMethod,
      'customerPhone': instance.customerPhone,
      'reference': instance.reference,
      'additionalData': instance.additionalData,
    };

BillPaymentResponse _$BillPaymentResponseFromJson(Map<String, dynamic> json) =>
    BillPaymentResponse(
      transactionId: json['transactionId'] as String,
      status: json['status'] as String,
      receiptNumber: json['receiptNumber'] as String?,
      amountPaid: (json['amountPaid'] as num).toDouble(),
      balance: (json['balance'] as num?)?.toDouble(),
      paymentDate: DateTime.parse(json['paymentDate'] as String),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$BillPaymentResponseToJson(
        BillPaymentResponse instance) =>
    <String, dynamic>{
      'transactionId': instance.transactionId,
      'status': instance.status,
      'receiptNumber': instance.receiptNumber,
      'amountPaid': instance.amountPaid,
      'balance': instance.balance,
      'paymentDate': instance.paymentDate.toIso8601String(),
      'message': instance.message,
    };

BillInquiryRequest _$BillInquiryRequestFromJson(Map<String, dynamic> json) =>
    BillInquiryRequest(
      provider: json['provider'] as String,
      accountNumber: json['accountNumber'] as String,
      customerPhone: json['customerPhone'] as String?,
      billPeriod: json['billPeriod'] as String?,
    );

Map<String, dynamic> _$BillInquiryRequestToJson(BillInquiryRequest instance) =>
    <String, dynamic>{
      'provider': instance.provider,
      'accountNumber': instance.accountNumber,
      'customerPhone': instance.customerPhone,
      'billPeriod': instance.billPeriod,
    };

BillInquiryResponse _$BillInquiryResponseFromJson(Map<String, dynamic> json) =>
    BillInquiryResponse(
      accountNumber: json['accountNumber'] as String,
      customerName: json['customerName'] as String,
      amountDue: (json['amountDue'] as num).toDouble(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      billPeriod: json['billPeriod'] as String,
      status: json['status'] as String,
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$BillInquiryResponseToJson(
        BillInquiryResponse instance) =>
    <String, dynamic>{
      'accountNumber': instance.accountNumber,
      'customerName': instance.customerName,
      'amountDue': instance.amountDue,
      'dueDate': instance.dueDate?.toIso8601String(),
      'billPeriod': instance.billPeriod,
      'status': instance.status,
      'additionalInfo': instance.additionalInfo,
    };
