import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/constants/app_constants.dart';
import '../../offline_sync/data/offline_sync_manager.dart';
import '../models/utility_bill_model.dart';
import 'zesco_api_service.dart';

/// Lupiya API service - Unified utility payment gateway
/// Provides offline-first bill payment with automatic retry and caching
class LupiyaAPI {
  static final LupiyaAPI _instance = LupiyaAPI._internal();
  factory LupiyaAPI() => _instance;
  LupiyaAPI._internal();

  static final ZESCOApiService _zescoService = ZESCOApiService();
  static final OfflineSyncManager _syncManager = OfflineSyncManager();
  static final Logger _logger = Logger();
  static final Uuid _uuid = Uuid();

  /// Pay utility bill with offline-first approach
  /// This is the main entry point that matches your original function signature
  static Future<BillPaymentResponse> payBill(
    String billerCode,
    String account,
    double amount, {
    String paymentMethod = 'MOBILE_MONEY',
    String? customerPhone,
    String? reference,
    String? userId,
  }) async {
    try {
      _logger.i('Processing bill payment: $billerCode, Account: $account, Amount: K${amount.toStringAsFixed(2)}');

      // Route to appropriate service based on biller code
      switch (billerCode.toUpperCase()) {
        case 'ZESCO-001':
        case 'ZESCO':
          return await _payZESCOBill(
            account: account,
            amount: amount,
            paymentMethod: paymentMethod,
            customerPhone: customerPhone,
            reference: reference,
            userId: userId,
          );
        
        case 'LWSC-001':
        case 'LWSC':
          return await _payWaterBill(
            provider: 'LWSC',
            account: account,
            amount: amount,
            paymentMethod: paymentMethod,
            customerPhone: customerPhone,
            reference: reference,
            userId: userId,
          );
        
        case 'NWASCO-001':
        case 'NWASCO':
          return await _payWaterBill(
            provider: 'NWASCO',
            account: account,
            amount: amount,
            paymentMethod: paymentMethod,
            customerPhone: customerPhone,
            reference: reference,
            userId: userId,
          );
        
        default:
          throw Exception('Unsupported biller code: $billerCode');
      }
    } catch (e) {
      _logger.e('Bill payment failed for $billerCode: $e');
      
      // If online payment fails, cache for offline processing
      return await _cacheOfflinePayment(
        billerCode: billerCode,
        account: account,
        amount: amount,
        paymentMethod: paymentMethod,
        customerPhone: customerPhone,
        reference: reference,
        userId: userId,
        error: e.toString(),
      );
    }
  }

  /// Pay ZESCO electricity bill
  static Future<BillPaymentResponse> _payZESCOBill({
    required String account,
    required double amount,
    required String paymentMethod,
    String? customerPhone,
    String? reference,
    String? userId,
  }) async {
    try {
      // Check if we're online
      if (await _syncManager.isConnected()) {
        // Attempt online payment
        final response = await _zescoService.payZESCO(
          account,
          amount,
          paymentMethod: paymentMethod,
          customerPhone: customerPhone,
          reference: reference,
        );

        // Log successful payment for audit
        await _logPaymentAudit(
          billerCode: 'ZESCO-001',
          account: account,
          amount: amount,
          transactionId: response.transactionId,
          status: response.status,
          userId: userId,
        );

        return response;
      } else {
        // Offline mode - queue payment
        throw Exception('No internet connection - queuing for offline processing');
      }
    } catch (e) {
      _logger.e('ZESCO payment failed: $e');
      rethrow;
    }
  }

  /// Pay water bill (LWSC/NWASCO)
  static Future<BillPaymentResponse> _payWaterBill({
    required String provider,
    required String account,
    required double amount,
    required String paymentMethod,
    String? customerPhone,
    String? reference,
    String? userId,
  }) async {
    // For now, simulate water bill payment
    // In production, this would integrate with actual water utility APIs
    
    if (!await _syncManager.isConnected()) {
      throw Exception('No internet connection - queuing for offline processing');
    }

    // Simulate API call delay
    await Future.delayed(Duration(seconds: 2));

    final transactionId = _uuid.v4();
    final response = BillPaymentResponse(
      transactionId: transactionId,
      status: 'SUCCESSFUL',
      receiptNumber: 'WTR-${transactionId.substring(0, 8)}',
      amountPaid: amount,
      balance: null,
      paymentDate: DateTime.now(),
      message: '$provider bill payment successful',
    );

    await _logPaymentAudit(
      billerCode: provider,
      account: account,
      amount: amount,
      transactionId: response.transactionId,
      status: response.status,
      userId: userId,
    );

    return response;
  }

  /// Cache payment for offline processing
  static Future<BillPaymentResponse> _cacheOfflinePayment({
    required String billerCode,
    required String account,
    required double amount,
    required String paymentMethod,
    String? customerPhone,
    String? reference,
    String? userId,
    required String error,
  }) async {
    final transactionId = _uuid.v4();
    
    try {
      // Create offline payment data
      final paymentData = {
        'transaction_id': transactionId,
        'biller_code': billerCode,
        'account_number': account,
        'amount': amount,
        'payment_method': paymentMethod,
        'customer_phone': customerPhone,
        'reference': reference ?? transactionId,
        'user_id': userId,
        'created_at': DateTime.now().toIso8601String(),
        'error_reason': error,
        'retry_count': 0,
      };

      // Queue for offline processing
      await _syncManager.queueTransaction(
        userId: userId ?? 'anonymous',
        transactionData: paymentData,
        transactionType: AppConstants.transactionTypeBillPayment,
        priority: 1,
      );

      _logger.i('Payment cached for offline processing: $transactionId');

      // Return offline receipt
      return BillPaymentResponse(
        transactionId: transactionId,
        status: 'QUEUED',
        receiptNumber: 'OFFLINE-${transactionId.substring(0, 8)}',
        amountPaid: amount,
        balance: null,
        paymentDate: DateTime.now(),
        message: 'Payment queued for processing when connection is restored. You will receive confirmation once processed.',
      );
    } catch (e) {
      _logger.e('Failed to cache offline payment: $e');
      
      // Return error receipt
      return BillPaymentResponse(
        transactionId: transactionId,
        status: 'FAILED',
        receiptNumber: 'ERROR-${transactionId.substring(0, 8)}',
        amountPaid: amount,
        balance: null,
        paymentDate: DateTime.now(),
        message: 'Payment failed: $error. Please try again later.',
      );
    }
  }

  /// Log payment audit for compliance
  static Future<void> _logPaymentAudit({
    required String billerCode,
    required String account,
    required double amount,
    required String transactionId,
    required String status,
    String? userId,
  }) async {
    try {
      // This would integrate with ComplianceService
      final auditData = {
        'event_type': 'UTILITY_BILL_PAYMENT',
        'biller_code': billerCode,
        'account_number': account,
        'amount': amount,
        'transaction_id': transactionId,
        'status': status,
        'timestamp': DateTime.now().toIso8601String(),
      };

      _logger.i('Payment audit logged: $transactionId');
    } catch (e) {
      _logger.e('Failed to log payment audit: $e');
    }
  }

  /// Get payment status
  static Future<BillPaymentResponse?> getPaymentStatus(String transactionId) async {
    try {
      // Check if it's a ZESCO transaction
      if (transactionId.startsWith('ZESCO') || transactionId.contains('ZESCO')) {
        return await _zescoService.checkPaymentStatus(transactionId);
      }
      
      // For other providers, implement similar status checks
      return null;
    } catch (e) {
      _logger.e('Failed to get payment status: $e');
      return null;
    }
  }

  /// Get cached receipt for offline access
  static Future<BillPaymentResponse?> getCachedReceipt(String transactionId) async {
    try {
      // Check ZESCO cache first
      final zescoReceipt = await _zescoService.getCachedReceipt(transactionId);
      if (zescoReceipt != null) {
        return zescoReceipt;
      }
      
      // Check other provider caches
      return null;
    } catch (e) {
      _logger.e('Failed to get cached receipt: $e');
      return null;
    }
  }

  /// Retry all offline payments
  static Future<List<BillPaymentResponse>> retryOfflinePayments() async {
    final results = <BillPaymentResponse>[];
    
    try {
      if (!await _syncManager.isConnected()) {
        _logger.w('Cannot retry offline payments - no internet connection');
        return results;
      }

      // Trigger sync of pending transactions
      await _syncManager.syncPendingTransactions();
      
      // Get ZESCO retry results
      final zescoResults = await _zescoService.retryOfflinePayments();
      results.addAll(zescoResults);
      
      _logger.i('Retried ${results.length} offline payments');
      return results;
    } catch (e) {
      _logger.e('Failed to retry offline payments: $e');
      return results;
    }
  }

  /// Get supported billers
  static List<Map<String, dynamic>> getSupportedBillers() {
    return [
      {
        'code': 'ZESCO-001',
        'name': 'ZESCO Limited',
        'type': 'ELECTRICITY',
        'description': 'Zambia Electricity Supply Corporation',
        'regions': ['lusaka', 'copperbelt', 'southern', 'eastern', 'western', 'northern', 'central', 'muchinga', 'luapula', 'northwestern'],
        'offlineSupport': true,
      },
      {
        'code': 'LWSC-001',
        'name': 'Lusaka Water and Sewerage Company',
        'type': 'WATER',
        'description': 'Water and sewerage services for Lusaka',
        'regions': ['lusaka'],
        'offlineSupport': true,
      },
      {
        'code': 'NWASCO-001',
        'name': 'National Water Supply and Sanitation Council',
        'type': 'WATER',
        'description': 'National water supply regulation',
        'regions': ['all'],
        'offlineSupport': true,
      },
    ];
  }
}
