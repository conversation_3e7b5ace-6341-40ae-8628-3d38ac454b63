import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../data/database/database_helper.dart';
import '../models/utility_bill_model.dart';
import 'zesco_api_service.dart';
import '../../../offline_sync/data/offline_sync_manager.dart';
import '../../../mobile_money/data/services/mobile_money_service.dart';

/// Unified utility service for managing all utility providers
/// Handles ZESCO, water bills, and other utility payments with offline support
class UtilityService {
  static final UtilityService _instance = UtilityService._internal();
  factory UtilityService() => _instance;
  UtilityService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final ZESCOApiService _zescoService = ZESCOApiService();
  final OfflineSyncManager _syncManager = OfflineSyncManager();
  final MobileMoneyService _mobileMoneyService = MobileMoneyService();
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  bool _isInitialized = false;

  /// Initialize utility service with provider credentials
  Future<void> initialize({
    required Map<String, String> zescoCredentials,
    // Add other provider credentials as needed
  }) async {
    try {
      // Initialize ZESCO
      _zescoService.initialize(
        apiKey: zescoCredentials['apiKey']!,
        merchantId: zescoCredentials['merchantId']!,
      );

      // Authenticate with providers
      await _zescoService.authenticate();

      _isInitialized = true;
      _logger.i('Utility service initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize utility service: $e');
      rethrow;
    }
  }

  /// Save utility bill to local database
  Future<void> saveBill(UtilityBillModel bill) async {
    try {
      await _dbHelper.insert(
        AppConstants.utilityBillsTable,
        bill.toDatabase(),
      );
      _logger.i('Utility bill saved: ${bill.id}');
    } catch (e) {
      _logger.e('Failed to save utility bill: $e');
      rethrow;
    }
  }

  /// Update utility bill in database
  Future<void> updateBill(UtilityBillModel bill) async {
    try {
      await _dbHelper.update(
        AppConstants.utilityBillsTable,
        bill.toDatabase(),
        where: 'id = ?',
        whereArgs: [bill.id],
      );
      _logger.i('Utility bill updated: ${bill.id}');
    } catch (e) {
      _logger.e('Failed to update utility bill: $e');
      rethrow;
    }
  }

  /// Get user's utility bills
  Future<List<UtilityBillModel>> getUserBills({
    required String userId,
    String? provider,
    String? utilityType,
    String? status,
  }) async {
    try {
      String whereClause = 'user_id = ?';
      List<dynamic> whereArgs = [userId];

      if (provider != null) {
        whereClause += ' AND provider = ?';
        whereArgs.add(provider);
      }

      if (utilityType != null) {
        whereClause += ' AND utility_type = ?';
        whereArgs.add(utilityType);
      }

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      final results = await _dbHelper.query(
        AppConstants.utilityBillsTable,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'last_updated DESC',
      );

      return results.map((data) => UtilityBillModel.fromDatabase(data)).toList();
    } catch (e) {
      _logger.e('Failed to get user bills: $e');
      return [];
    }
  }

  /// Inquire about a utility bill
  Future<UtilityBillModel> inquireBill({
    required String userId,
    required String provider,
    required String accountNumber,
    String? customerPhone,
    String? billPeriod,
  }) async {
    if (!_isInitialized) {
      throw Exception('Utility service not initialized');
    }

    try {
      BillInquiryResponse response;

      switch (provider.toUpperCase()) {
        case 'ZESCO':
          response = await _zescoService.inquireBill(
            accountNumber: accountNumber,
            customerPhone: customerPhone,
            billPeriod: billPeriod,
          );
          break;
        
        // Add other providers here
        default:
          throw Exception('Unsupported utility provider: $provider');
      }

      // Create or update bill record
      final billId = _uuid.v4();
      final bill = UtilityBillModel(
        id: billId,
        userId: userId,
        provider: provider.toUpperCase(),
        accountNumber: accountNumber,
        customerName: response.customerName,
        utilityType: _getUtilityType(provider),
        amountDue: response.amountDue,
        dueDate: response.dueDate,
        billPeriod: response.billPeriod,
        status: response.status,
        lastUpdated: DateTime.now(),
        metadata: response.additionalInfo,
      );

      // Check if bill already exists
      final existingBills = await getUserBills(
        userId: userId,
        provider: provider.toUpperCase(),
      );

      final existingBill = existingBills.where(
        (b) => b.accountNumber == accountNumber && b.billPeriod == response.billPeriod,
      ).firstOrNull;

      if (existingBill != null) {
        // Update existing bill
        final updatedBill = existingBill.copyWith(
          customerName: response.customerName,
          amountDue: response.amountDue,
          dueDate: response.dueDate,
          status: response.status,
          lastUpdated: DateTime.now(),
          metadata: response.additionalInfo,
        );
        await updateBill(updatedBill);
        return updatedBill;
      } else {
        // Save new bill
        await saveBill(bill);
        return bill;
      }
    } catch (e) {
      _logger.e('Bill inquiry failed: $e');
      rethrow;
    }
  }

  /// Pay utility bill
  Future<BillPaymentResponse> payBill({
    required String userId,
    required String billId,
    required double amount,
    required String paymentMethod,
    String? customerPhone,
    bool useOfflineQueue = true,
  }) async {
    if (!_isInitialized) {
      throw Exception('Utility service not initialized');
    }

    try {
      // Get bill details
      final bills = await getUserBills(userId: userId);
      final bill = bills.where((b) => b.id == billId).firstOrNull;
      
      if (bill == null) {
        throw Exception('Bill not found');
      }

      // Check if online and attempt immediate payment
      if (await _syncManager.isConnected()) {
        final response = await _processPayment(bill, amount, paymentMethod, customerPhone);
        
        // Update bill status
        await updateBill(bill.copyWith(
          status: 'PAID',
          lastUpdated: DateTime.now(),
        ));
        
        return response;
      } else if (useOfflineQueue) {
        // Queue payment for offline processing
        await _syncManager.queueTransaction(
          userId: userId,
          transactionData: {
            'type': 'BILL_PAYMENT',
            'billId': billId,
            'amount': amount,
            'paymentMethod': paymentMethod,
            'customerPhone': customerPhone,
            'provider': bill.provider,
            'accountNumber': bill.accountNumber,
          },
          transactionType: AppConstants.transactionTypeBillPayment,
        );
        
        // Return pending response
        return BillPaymentResponse(
          transactionId: _uuid.v4(),
          status: 'QUEUED',
          amountPaid: amount,
          paymentDate: DateTime.now(),
          message: 'Payment queued for processing when online',
        );
      } else {
        throw Exception('No internet connection and offline mode disabled');
      }
    } catch (e) {
      _logger.e('Bill payment failed: $e');
      rethrow;
    }
  }

  /// Process payment with specific provider
  Future<BillPaymentResponse> _processPayment(
    UtilityBillModel bill,
    double amount,
    String paymentMethod,
    String? customerPhone,
  ) async {
    switch (bill.provider) {
      case 'ZESCO':
        return await _zescoService.payBill(
          accountNumber: bill.accountNumber,
          amount: amount,
          paymentMethod: paymentMethod,
          customerPhone: customerPhone,
        );
      
      // Add other providers here
      default:
        throw Exception('Unsupported utility provider: ${bill.provider}');
    }
  }

  /// Get utility type for provider
  String _getUtilityType(String provider) {
    switch (provider.toUpperCase()) {
      case 'ZESCO':
        return AppConstants.utilityElectricity;
      case 'LWSC':
      case 'NWASCO':
        return AppConstants.utilityWater;
      default:
        return 'OTHER';
    }
  }

  /// Get available utility providers
  List<UtilityProvider> getAvailableProviders() {
    final providers = <UtilityProvider>[];
    
    // Add ZESCO
    providers.add(_zescoService.getProviderInfo());
    
    // Add other providers as they are implemented
    
    return providers;
  }

  /// Get bills due soon
  Future<List<UtilityBillModel>> getBillsDueSoon({
    required String userId,
    int daysAhead = 7,
  }) async {
    final bills = await getUserBills(userId: userId, status: 'UNPAID');
    final cutoffDate = DateTime.now().add(Duration(days: daysAhead));
    
    return bills.where((bill) => 
      bill.dueDate != null && 
      bill.dueDate!.isBefore(cutoffDate) &&
      bill.dueDate!.isAfter(DateTime.now())
    ).toList();
  }

  /// Get overdue bills
  Future<List<UtilityBillModel>> getOverdueBills({
    required String userId,
  }) async {
    final bills = await getUserBills(userId: userId, status: 'UNPAID');
    final now = DateTime.now();
    
    return bills.where((bill) => 
      bill.dueDate != null && bill.dueDate!.isBefore(now)
    ).toList();
  }

  /// Enable auto-pay for a bill
  Future<void> enableAutoPay({
    required String billId,
    required String paymentMethod,
  }) async {
    try {
      final bills = await _dbHelper.query(
        AppConstants.utilityBillsTable,
        where: 'id = ?',
        whereArgs: [billId],
      );

      if (bills.isNotEmpty) {
        final bill = UtilityBillModel.fromDatabase(bills.first);
        await updateBill(bill.copyWith(
          autoPayEnabled: true,
          metadata: {
            ...?bill.metadata,
            'autoPayMethod': paymentMethod,
          },
        ));
      }
    } catch (e) {
      _logger.e('Failed to enable auto-pay: $e');
      rethrow;
    }
  }

  /// Disable auto-pay for a bill
  Future<void> disableAutoPay(String billId) async {
    try {
      final bills = await _dbHelper.query(
        AppConstants.utilityBillsTable,
        where: 'id = ?',
        whereArgs: [billId],
      );

      if (bills.isNotEmpty) {
        final bill = UtilityBillModel.fromDatabase(bills.first);
        final updatedMetadata = Map<String, dynamic>.from(bill.metadata ?? {});
        updatedMetadata.remove('autoPayMethod');
        
        await updateBill(bill.copyWith(
          autoPayEnabled: false,
          metadata: updatedMetadata,
        ));
      }
    } catch (e) {
      _logger.e('Failed to disable auto-pay: $e');
      rethrow;
    }
  }

  /// Process auto-payments for due bills
  Future<void> processAutoPay(String userId) async {
    try {
      final bills = await getUserBills(userId: userId);
      final autoPayBills = bills.where((bill) => 
        bill.autoPayEnabled && 
        bill.status == 'UNPAID' &&
        bill.dueDate != null &&
        bill.dueDate!.isBefore(DateTime.now().add(Duration(days: 1)))
      ).toList();

      for (final bill in autoPayBills) {
        final paymentMethod = bill.metadata?['autoPayMethod'] as String?;
        if (paymentMethod != null && bill.amountDue != null) {
          try {
            await payBill(
              userId: userId,
              billId: bill.id,
              amount: bill.amountDue!,
              paymentMethod: paymentMethod,
            );
            _logger.i('Auto-payment processed for bill: ${bill.id}');
          } catch (e) {
            _logger.w('Auto-payment failed for bill ${bill.id}: $e');
          }
        }
      }
    } catch (e) {
      _logger.e('Auto-payment processing failed: $e');
    }
  }
}
