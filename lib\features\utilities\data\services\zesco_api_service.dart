import 'dart:convert';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/constants/app_constants.dart';
import '../models/utility_bill_model.dart';

/// ZESCO (Zambia Electricity Supply Corporation) API service
/// Handles electricity bill inquiries and payments
class ZESCOApiService {
  static final ZESCOApiService _instance = ZESCOApiService._internal();
  factory ZESCOApiService() => _instance;
  ZESCOApiService._internal();

  late final Dio _dio;
  final Logger _logger = Logger();
  final Uuid _uuid = Uuid();

  String? _accessToken;
  String? _apiKey;
  String? _merchantId;
  DateTime? _tokenExpiry;

  void initialize({
    required String apiKey,
    required String merchantId,
  }) {
    _apiKey = apiKey;
    _merchantId = merchantId;

    final zescoConfig = AppConfig.utilityProviders['ZESCO'] as Map<String, dynamic>;
    
    _dio = Dio(BaseOptions(
      baseUrl: zescoConfig['apiEndpoint'] as String,
      connectTimeout: Duration(milliseconds: AppConstants.connectionTimeoutMs),
      receiveTimeout: Duration(milliseconds: AppConstants.receiveTimeoutMs),
      sendTimeout: Duration(milliseconds: AppConstants.sendTimeoutMs),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': _apiKey,
        'X-Merchant-ID': _merchantId,
      },
    ));

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        if (_accessToken != null && _isTokenValid()) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        
        options.headers['X-Request-ID'] = _uuid.v4();
        
        _logger.d('ZESCO API Request: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        _logger.d('ZESCO API Response: ${response.statusCode} ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) {
        _logger.e('ZESCO API Error: ${error.response?.statusCode} ${error.message}');
        handler.next(error);
      },
    ));
  }

  /// Authenticate with ZESCO API
  Future<bool> authenticate() async {
    try {
      final response = await _dio.post('/auth/token', data: {
        'api_key': _apiKey,
        'merchant_id': _merchantId,
        'grant_type': 'client_credentials',
      });

      if (response.statusCode == 200) {
        _accessToken = response.data['access_token'];
        final expiresIn = response.data['expires_in'] as int;
        _tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn - 60));
        
        _logger.i('ZESCO authentication successful');
        return true;
      }
    } catch (e) {
      _logger.e('ZESCO authentication failed: $e');
    }
    
    return false;
  }

  bool _isTokenValid() {
    return _tokenExpiry != null && DateTime.now().isBefore(_tokenExpiry!);
  }

  /// Validate ZESCO account number format
  bool _isValidAccountNumber(String accountNumber) {
    // ZESCO account numbers are typically 10-12 digits
    final cleaned = accountNumber.replaceAll(RegExp(r'[^\d]'), '');
    return cleaned.length >= 10 && cleaned.length <= 12;
  }

  /// Inquire about electricity bill
  Future<BillInquiryResponse> inquireBill({
    required String accountNumber,
    String? customerPhone,
    String? billPeriod,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    if (!_isValidAccountNumber(accountNumber)) {
      throw Exception('Invalid ZESCO account number format');
    }

    final requestData = {
      'account_number': accountNumber,
      'customer_phone': customerPhone,
      'bill_period': billPeriod ?? _getCurrentBillPeriod(),
      'request_id': _uuid.v4(),
    };

    try {
      final response = await _dio.post('/bills/inquiry', data: requestData);

      if (response.statusCode == 200) {
        final data = response.data;
        
        return BillInquiryResponse(
          accountNumber: data['account_number'],
          customerName: data['customer_name'],
          amountDue: (data['amount_due'] as num).toDouble(),
          dueDate: data['due_date'] != null 
              ? DateTime.parse(data['due_date'])
              : null,
          billPeriod: data['bill_period'],
          status: data['status'] ?? 'ACTIVE',
          additionalInfo: {
            'meter_number': data['meter_number'],
            'tariff_category': data['tariff_category'],
            'units_consumed': data['units_consumed'],
            'previous_reading': data['previous_reading'],
            'current_reading': data['current_reading'],
            'region': data['region'],
          },
        );
      } else {
        throw Exception('Bill inquiry failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('ZESCO bill inquiry failed: $e');
      rethrow;
    }
  }

  /// Pay electricity bill with offline-first approach
  Future<BillPaymentResponse> payBill({
    required String accountNumber,
    required double amount,
    required String paymentMethod,
    String? customerPhone,
    String? reference,
  }) async {
    if (!_isValidAccountNumber(accountNumber)) {
      throw Exception('Invalid ZESCO account number format');
    }

    if (amount <= 0) {
      throw Exception('Payment amount must be greater than zero');
    }

    final transactionId = _uuid.v4();
    const billerCode = "ZESCO-001";

    // Try online payment first
    try {
      return await _processOnlinePayment(
        accountNumber: accountNumber,
        amount: amount,
        paymentMethod: paymentMethod,
        customerPhone: customerPhone,
        reference: reference,
        transactionId: transactionId,
        billerCode: billerCode,
      );
    } catch (e) {
      _logger.w('Online payment failed, caching for offline processing: $e');

      // Cache payment for offline processing
      return await _cacheOfflinePayment(
        accountNumber: accountNumber,
        amount: amount,
        paymentMethod: paymentMethod,
        customerPhone: customerPhone,
        reference: reference,
        transactionId: transactionId,
        billerCode: billerCode,
        error: e.toString(),
      );
    }
  }

  /// Process online ZESCO payment
  Future<BillPaymentResponse> _processOnlinePayment({
    required String accountNumber,
    required double amount,
    required String paymentMethod,
    String? customerPhone,
    String? reference,
    required String transactionId,
    required String billerCode,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    final requestData = {
      'transaction_id': transactionId,
      'biller_code': billerCode,
      'account_number': accountNumber,
      'amount': amount.toStringAsFixed(2),
      'payment_method': paymentMethod,
      'customer_phone': customerPhone,
      'reference': reference ?? transactionId,
      'merchant_id': _merchantId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    final response = await _dio.post('/bills/payment', data: requestData);

    if (response.statusCode == 200 || response.statusCode == 201) {
      final data = response.data;

      final paymentResponse = BillPaymentResponse(
        transactionId: data['transaction_id'],
        status: data['status'],
        receiptNumber: data['receipt_number'],
        amountPaid: (data['amount_paid'] as num).toDouble(),
        balance: data['balance'] != null
            ? (data['balance'] as num).toDouble()
            : null,
        paymentDate: DateTime.parse(data['payment_date']),
        message: data['message'],
      );

      // Cache successful receipt for offline access
      await _cacheSuccessfulReceipt(paymentResponse, requestData);

      return paymentResponse;
    } else {
      throw Exception('Bill payment failed with status: ${response.statusCode}');
    }
  }

  /// Cache payment for offline processing
  Future<BillPaymentResponse> _cacheOfflinePayment({
    required String accountNumber,
    required double amount,
    required String paymentMethod,
    String? customerPhone,
    String? reference,
    required String transactionId,
    required String billerCode,
    required String error,
  }) async {
    final offlinePayment = {
      'transaction_id': transactionId,
      'biller_code': billerCode,
      'account_number': accountNumber,
      'amount': amount,
      'payment_method': paymentMethod,
      'customer_phone': customerPhone,
      'reference': reference ?? transactionId,
      'merchant_id': _merchantId,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'status': 'CACHED_OFFLINE',
      'error_reason': error,
      'retry_count': 0,
      'next_retry_at': DateTime.now().add(Duration(minutes: 15)).millisecondsSinceEpoch,
    };

    // Store in offline queue (this would integrate with OfflineSyncManager)
    await _storeOfflinePayment(offlinePayment);

    // Generate offline receipt
    return BillPaymentResponse(
      transactionId: transactionId,
      status: 'QUEUED',
      receiptNumber: 'OFFLINE-${transactionId.substring(0, 8)}',
      amountPaid: amount,
      balance: null,
      paymentDate: DateTime.now(),
      message: 'Payment queued for processing when connection is restored',
    );
  }

  /// Check payment status
  Future<BillPaymentResponse> checkPaymentStatus(String transactionId) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    try {
      final response = await _dio.get('/bills/payment/$transactionId/status');
      
      if (response.statusCode == 200) {
        final data = response.data;
        
        return BillPaymentResponse(
          transactionId: data['transaction_id'],
          status: data['status'],
          receiptNumber: data['receipt_number'],
          amountPaid: (data['amount_paid'] as num).toDouble(),
          balance: data['balance'] != null 
              ? (data['balance'] as num).toDouble()
              : null,
          paymentDate: DateTime.parse(data['payment_date']),
          message: data['message'],
        );
      } else {
        throw Exception('Status check failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('ZESCO payment status check failed: $e');
      rethrow;
    }
  }

  /// Get customer account details
  Future<Map<String, dynamic>> getAccountDetails(String accountNumber) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    if (!_isValidAccountNumber(accountNumber)) {
      throw Exception('Invalid ZESCO account number format');
    }

    try {
      final response = await _dio.get('/accounts/$accountNumber');
      
      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('Account details fetch failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('ZESCO account details fetch failed: $e');
      rethrow;
    }
  }

  /// Get payment history for account
  Future<List<Map<String, dynamic>>> getPaymentHistory({
    required String accountNumber,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    if (!await _ensureAuthenticated()) {
      throw Exception('Authentication failed');
    }

    final queryParams = <String, dynamic>{
      'account_number': accountNumber,
    };

    if (startDate != null) {
      queryParams['start_date'] = startDate.toIso8601String();
    }

    if (endDate != null) {
      queryParams['end_date'] = endDate.toIso8601String();
    }

    if (limit != null) {
      queryParams['limit'] = limit;
    }

    try {
      final response = await _dio.get('/bills/payment/history', queryParameters: queryParams);
      
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['payments']);
      } else {
        throw Exception('Payment history fetch failed with status: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('ZESCO payment history fetch failed: $e');
      return [];
    }
  }

  /// Get current bill period
  String _getCurrentBillPeriod() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}';
  }

  /// Ensure authentication is valid
  Future<bool> _ensureAuthenticated() async {
    if (_accessToken == null || !_isTokenValid()) {
      return await authenticate();
    }
    return true;
  }

  /// Get supported regions
  List<String> getSupportedRegions() {
    final zescoConfig = AppConfig.utilityProviders['ZESCO'] as Map<String, dynamic>;
    return List<String>.from(zescoConfig['supportedRegions']);
  }

  /// Validate account number for specific region
  bool validateAccountForRegion(String accountNumber, String region) {
    // Different regions might have different account number formats
    // This is a simplified validation
    return _isValidAccountNumber(accountNumber) && 
           getSupportedRegions().contains(region.toLowerCase());
  }

  /// Cache successful receipt for offline access
  Future<void> _cacheSuccessfulReceipt(
    BillPaymentResponse response,
    Map<String, dynamic> requestData,
  ) async {
    try {
      final receiptData = {
        'transaction_id': response.transactionId,
        'receipt_number': response.receiptNumber,
        'account_number': requestData['account_number'],
        'amount_paid': response.amountPaid,
        'payment_date': response.paymentDate.toIso8601String(),
        'status': response.status,
        'message': response.message,
        'biller_code': requestData['biller_code'],
        'cached_at': DateTime.now().millisecondsSinceEpoch,
      };

      // Store receipt in secure local storage for offline access
      await _storeReceiptCache(receiptData);

      _logger.i('Receipt cached for offline access: ${response.transactionId}');
    } catch (e) {
      _logger.e('Failed to cache receipt: $e');
    }
  }

  /// Store offline payment in queue
  Future<void> _storeOfflinePayment(Map<String, dynamic> paymentData) async {
    try {
      // This would integrate with DatabaseHelper and OfflineSyncManager
      // For now, we'll simulate the storage

      final encryptedData = await _encryptPaymentData(paymentData);

      // Store in offline_queue table
      final queueItem = {
        'id': paymentData['transaction_id'],
        'user_id': 'current_user_id', // Would be passed from service layer
        'transaction_data': encryptedData,
        'transaction_type': 'ZESCO_BILL_PAYMENT',
        'priority': 1,
        'retry_count': 0,
        'max_retries': 3,
        'created_at': paymentData['created_at'],
        'next_retry_at': paymentData['next_retry_at'],
        'status': 'QUEUED',
      };

      // This would use DatabaseHelper.insert()
      _logger.i('Offline payment stored in queue: ${paymentData['transaction_id']}');
    } catch (e) {
      _logger.e('Failed to store offline payment: $e');
      rethrow;
    }
  }

  /// Store receipt in cache
  Future<void> _storeReceiptCache(Map<String, dynamic> receiptData) async {
    try {
      // Store in local cache table for offline receipt access
      final cacheItem = {
        'id': receiptData['transaction_id'],
        'receipt_data': await _encryptPaymentData(receiptData),
        'cached_at': receiptData['cached_at'],
        'expires_at': DateTime.now().add(Duration(days: 30)).millisecondsSinceEpoch,
      };

      // This would use DatabaseHelper to store in receipt_cache table
      _logger.i('Receipt cached: ${receiptData['transaction_id']}');
    } catch (e) {
      _logger.e('Failed to cache receipt: $e');
    }
  }

  /// Encrypt payment data for secure storage
  Future<String> _encryptPaymentData(Map<String, dynamic> data) async {
    try {
      // This would use EncryptionService
      final jsonString = jsonEncode(data);
      // return await EncryptionService().encryptData(jsonString);

      // For now, return base64 encoded (in production, use proper encryption)
      return base64Encode(utf8.encode(jsonString));
    } catch (e) {
      _logger.e('Failed to encrypt payment data: $e');
      rethrow;
    }
  }

  /// Retrieve cached receipt for offline access
  Future<BillPaymentResponse?> getCachedReceipt(String transactionId) async {
    try {
      // This would query the receipt_cache table
      // For now, simulate cache lookup

      _logger.i('Looking up cached receipt: $transactionId');

      // In real implementation:
      // 1. Query receipt_cache table
      // 2. Decrypt receipt data
      // 3. Return BillPaymentResponse

      return null; // Placeholder
    } catch (e) {
      _logger.e('Failed to retrieve cached receipt: $e');
      return null;
    }
  }

  /// Retry offline payments
  Future<List<BillPaymentResponse>> retryOfflinePayments() async {
    final results = <BillPaymentResponse>[];

    try {
      // This would integrate with OfflineSyncManager
      // 1. Get pending ZESCO payments from offline queue
      // 2. Attempt to process each payment
      // 3. Update queue status based on results

      _logger.i('Retrying offline ZESCO payments');

      // Placeholder for retry logic
      return results;
    } catch (e) {
      _logger.e('Failed to retry offline payments: $e');
      return results;
    }
  }

  /// Enhanced payZESCO method with full offline-first support
  Future<BillPaymentResponse> payZESCO(String account, double amount, {
    String paymentMethod = 'MOBILE_MONEY',
    String? customerPhone,
    String? reference,
  }) async {
    const billerCode = "ZESCO-001";

    try {
      // Validate inputs
      if (!_isValidAccountNumber(account)) {
        throw Exception('Invalid ZESCO account number: $account');
      }

      if (amount <= 0 || amount > 50000) {
        throw Exception('Invalid payment amount: K${amount.toStringAsFixed(2)}');
      }

      // Attempt payment with full offline-first support
      final response = await payBill(
        accountNumber: account,
        amount: amount,
        paymentMethod: paymentMethod,
        customerPhone: customerPhone,
        reference: reference,
      );

      _logger.i('ZESCO payment processed: ${response.transactionId} - ${response.status}');
      return response;

    } catch (e) {
      _logger.e('ZESCO payment failed for account $account: $e');
      rethrow;
    }
  }

  /// Get provider information
  UtilityProvider getProviderInfo() {
    final zescoConfig = AppConfig.utilityProviders['ZESCO'] as Map<String, dynamic>;

    return UtilityProvider(
      code: 'ZESCO',
      name: zescoConfig['name'] as String,
      utilityType: zescoConfig['type'] as String,
      apiEndpoint: zescoConfig['apiEndpoint'] as String,
      supportedRegions: List<String>.from(zescoConfig['supportedRegions']),
      isActive: true,
      configuration: {
        'accountNumberLength': '10-12',
        'paymentMethods': ['MOBILE_MONEY', 'BANK_TRANSFER'],
        'billPeriods': ['monthly'],
        'currency': 'ZMW',
        'billerCode': 'ZESCO-001',
        'offlineSupport': true,
        'receiptCaching': true,
      },
    );
  }
}
