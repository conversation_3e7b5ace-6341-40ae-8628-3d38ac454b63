import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/accessibility/voice_player.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../widgets/payment_button.dart';
import '../../data/services/lupiya_api_service.dart';
import '../../data/models/utility_bill_model.dart';

/// Rural-optimized payment screen with voice guidance and icon-first navigation
/// Demonstrates your original concept: VoicePlayer.play("bemba", "tapili_water.wav")
/// and PaymentButton(icon: Icons.water_drop, label: "NWSC")
class RuralPaymentScreen extends StatefulWidget {
  const RuralPaymentScreen({super.key});

  @override
  State<RuralPaymentScreen> createState() => _RuralPaymentScreenState();
}

class _RuralPaymentScreenState extends State<RuralPaymentScreen> {
  String _selectedLanguage = 'bemba';
  bool _isVoiceEnabled = true;

  @override
  void initState() {
    super.initState();
    _initializeVoice();
  }

  Future<void> _initializeVoice() async {
    await VoicePlayer.initialize();
    setState(() {
      _selectedLanguage = VoicePlayer.currentLanguage;
      _isVoiceEnabled = VoicePlayer.isVoiceEnabled;
    });
    
    // Play welcome message in selected language
    await VoicePlayer.playWelcome(language: _selectedLanguage);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'Zambia Pay',
          style: GoogleFonts.roboto(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2E7D32),
          ),
        ),
        actions: [
          // Language selector
          PopupMenuButton<String>(
            icon: const Icon(Icons.language),
            onSelected: (language) async {
              await VoicePlayer.setLanguage(language);
              setState(() => _selectedLanguage = language);
              await VoicePlayer.playWelcome(language: language);
            },
            itemBuilder: (context) => VoicePlayer.languages.entries
                .map((entry) => PopupMenuItem(
                      value: entry.key,
                      child: Text(entry.value),
                    ))
                .toList(),
          ),
          
          // Voice toggle
          IconButton(
            icon: Icon(_isVoiceEnabled ? Icons.volume_up : Icons.volume_off),
            onPressed: () async {
              await VoicePlayer.setVoiceEnabled(!_isVoiceEnabled);
              setState(() => _isVoiceEnabled = !_isVoiceEnabled);
            },
          ),
        ],
      ),
      
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome message with voice
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
                ),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.white,
                    size: 48,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _getWelcomeText(_selectedLanguage),
                    style: GoogleFonts.roboto(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.offline_bolt, color: Colors.white70, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Works offline',
                        style: GoogleFonts.roboto(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Payment Services Title
            Text(
              _getServicesText(_selectedLanguage),
              style: GoogleFonts.roboto(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Icon-first payment buttons as per your example
            PaymentButtonGrid(
              buttons: [
                // Water payment - exactly as in your example
                PaymentButton(
                  icon: Icons.water_drop,
                  label: "NWSC",
                  subtitle: _getWaterText(_selectedLanguage),
                  iconColor: Colors.blue,
                  iconSize: 56,
                  padding: const EdgeInsets.all(24),
                  onPressed: () {
                    // Voice-guided UI (Bemba/Nyanja) - your exact example
                    VoicePlayer.play("bemba", "tapili_water.wav");
                    _handleWaterPayment();
                  },
                ),
                
                // ZESCO electricity
                PaymentButton(
                  icon: Icons.electrical_services,
                  label: "ZESCO",
                  subtitle: _getElectricityText(_selectedLanguage),
                  iconColor: Colors.orange,
                  iconSize: 56,
                  padding: const EdgeInsets.all(24),
                  onPressed: () {
                    VoicePlayer.playElectricityBill(language: _selectedLanguage);
                    _handleZESCOPayment();
                  },
                ),
                
                // Send money
                PaymentButton(
                  icon: Icons.send,
                  label: _getSendMoneyText(_selectedLanguage),
                  subtitle: "MTN, Airtel, Zamtel",
                  iconColor: Colors.green,
                  iconSize: 56,
                  padding: const EdgeInsets.all(24),
                  onPressed: () {
                    VoicePlayer.playSendMoney(language: _selectedLanguage);
                    _handleSendMoney();
                  },
                ),
                
                // Buy airtime
                PaymentButton(
                  icon: Icons.phone_android,
                  label: _getAirtimeText(_selectedLanguage),
                  subtitle: _getAllNetworksText(_selectedLanguage),
                  iconColor: Colors.purple,
                  iconSize: 56,
                  padding: const EdgeInsets.all(24),
                  onPressed: () {
                    VoicePlayer.playPrompt('buy_airtime', language: _selectedLanguage);
                    _handleAirtime();
                  },
                ),
              ],
              crossAxisCount: 2,
              spacing: 20,
              childAspectRatio: 1.0,
            ),
            
            const SizedBox(height: 32),
            
            // Language indicator
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.language, color: Colors.grey),
                  const SizedBox(width: 12),
                  Text(
                    'Language: ${VoicePlayer.languages[_selectedLanguage]}',
                    style: GoogleFonts.roboto(fontSize: 16),
                  ),
                  const Spacer(),
                  if (_isVoiceEnabled)
                    const Icon(Icons.volume_up, color: Colors.green)
                  else
                    const Icon(Icons.volume_off, color: Colors.grey),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Handle water payment - demonstrates your NWSC example
  void _handleWaterPayment() async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Title with voice button
              Row(
                children: [
                  const Icon(Icons.water_drop, color: Colors.blue, size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'NWSC ${_getWaterText(_selectedLanguage)}',
                      style: GoogleFonts.roboto(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.volume_up),
                    onPressed: () => VoicePlayer.play("bemba", "tapili_water.wav"),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Demo payment form
              Expanded(
                child: Column(
                  children: [
                    TextField(
                      decoration: InputDecoration(
                        labelText: _getAccountNumberText(_selectedLanguage),
                        prefixIcon: const Icon(Icons.account_balance),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextField(
                      decoration: InputDecoration(
                        labelText: _getAmountText(_selectedLanguage),
                        prefixIcon: const Icon(Icons.money),
                        prefixText: 'K ',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    
                    const SizedBox(height: 24),
                    
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () async {
                          Navigator.pop(context);
                          await _processPayment('NWSC-001', '**********', 150.0);
                        },
                        child: Text(
                          _getPayText(_selectedLanguage),
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Handle ZESCO payment
  void _handleZESCOPayment() {
    // Similar implementation for ZESCO
    _showComingSoon('ZESCO Payment');
  }

  // Handle send money
  void _handleSendMoney() {
    _showComingSoon('Send Money');
  }

  // Handle airtime
  void _handleAirtime() {
    _showComingSoon('Buy Airtime');
  }

  // Process payment using LupiyaAPI
  Future<void> _processPayment(String billerCode, String account, double amount) async {
    try {
      final response = await LupiyaAPI.payBill(
        billerCode,
        account,
        amount,
        userId: 'demo_user',
      );
      
      // Play success or failure sound
      if (response.status == 'SUCCESSFUL' || response.status == 'QUEUED') {
        await VoicePlayer.playPaymentSuccess(language: _selectedLanguage);
      } else {
        await VoicePlayer.playPaymentFailed(language: _selectedLanguage);
      }
      
      _showPaymentResult(response);
    } catch (e) {
      await VoicePlayer.playPaymentFailed(language: _selectedLanguage);
      _showError(e.toString());
    }
  }

  void _showPaymentResult(BillPaymentResponse response) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          response.status == 'SUCCESSFUL' ? Icons.check_circle : Icons.info,
          color: response.status == 'SUCCESSFUL' ? Colors.green : Colors.orange,
          size: 48,
        ),
        title: Text(response.status == 'SUCCESSFUL' ? 'Payment Successful' : 'Payment Queued'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Receipt: ${response.receiptNumber}'),
            Text('Amount: K${response.amountPaid.toStringAsFixed(2)}'),
            if (response.message != null) Text(response.message!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showError(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.error, color: Colors.red, size: 48),
        title: const Text('Payment Failed'),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.construction, color: Colors.orange, size: 48),
        title: const Text('Coming Soon'),
        content: Text('$feature feature is under development.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Localized text helpers
  String _getWelcomeText(String language) {
    switch (language) {
      case 'bemba': return 'Mwaiseni ku Zambia Pay';
      case 'nyanja': return 'Takulandirani ku Zambia Pay';
      case 'tonga': return 'Twalumba ku Zambia Pay';
      case 'lozi': return 'Re a le amohela ho Zambia Pay';
      default: return 'Welcome to Zambia Pay';
    }
  }

  String _getServicesText(String language) {
    switch (language) {
      case 'bemba': return 'Imilimo';
      case 'nyanja': return 'Ntchito';
      case 'tonga': return 'Milimo';
      case 'lozi': return 'Mesebetsi';
      default: return 'Services';
    }
  }

  String _getWaterText(String language) {
    switch (language) {
      case 'bemba': return 'Tapili';
      case 'nyanja': return 'Madzi';
      case 'tonga': return 'Meenda';
      case 'lozi': return 'Mezi';
      default: return 'Water';
    }
  }

  String _getElectricityText(String language) {
    switch (language) {
      case 'bemba': return 'Umeme';
      case 'nyanja': return 'Magetsi';
      case 'tonga': return 'Getsi';
      case 'lozi': return 'Motlakase';
      default: return 'Electricity';
    }
  }

  String _getSendMoneyText(String language) {
    switch (language) {
      case 'bemba': return 'Tuma indalama';
      case 'nyanja': return 'Tumiza ndalama';
      case 'tonga': return 'Tuma mali';
      case 'lozi': return 'Roma mali';
      default: return 'Send Money';
    }
  }

  String _getAirtimeText(String language) {
    switch (language) {
      case 'bemba': return 'Gula airtime';
      case 'nyanja': return 'Gula airtime';
      case 'tonga': return 'Tenga airtime';
      case 'lozi': return 'Reka airtime';
      default: return 'Buy Airtime';
    }
  }

  String _getAllNetworksText(String language) {
    switch (language) {
      case 'bemba': return 'Yonse network';
      case 'nyanja': return 'Ma network onse';
      case 'tonga': return 'Ma network onse';
      case 'lozi': return 'Li-network tsohle';
      default: return 'All Networks';
    }
  }

  String _getAccountNumberText(String language) {
    switch (language) {
      case 'bemba': return 'Account number';
      case 'nyanja': return 'Account number';
      case 'tonga': return 'Account number';
      case 'lozi': return 'Account number';
      default: return 'Account Number';
    }
  }

  String _getAmountText(String language) {
    switch (language) {
      case 'bemba': return 'Amount';
      case 'nyanja': return 'Amount';
      case 'tonga': return 'Amount';
      case 'lozi': return 'Amount';
      default: return 'Amount';
    }
  }

  String _getPayText(String language) {
    switch (language) {
      case 'bemba': return 'Lipila';
      case 'nyanja': return 'Lipira';
      case 'tonga': return 'Bhadhala';
      case 'lozi': return 'Lefa';
      default: return 'Pay';
    }
  }
}
