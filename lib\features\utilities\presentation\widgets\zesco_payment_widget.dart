import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/constants/app_constants.dart';
import '../../data/services/lupiya_api_service.dart';
import '../../data/models/utility_bill_model.dart';

/// ZESCO payment widget with offline-first support
class ZESCOPaymentWidget extends StatefulWidget {
  final String? initialAccount;
  final double? initialAmount;
  final Function(BillPaymentResponse)? onPaymentComplete;

  const ZESCOPaymentWidget({
    super.key,
    this.initialAccount,
    this.initialAmount,
    this.onPaymentComplete,
  });

  @override
  State<ZESCOPaymentWidget> createState() => _ZESCOPaymentWidgetState();
}

class _ZESCOPaymentWidgetState extends State<ZESCOPaymentWidget> {
  final _formKey = GlobalKey<FormState>();
  final _accountController = TextEditingController();
  final _amountController = TextEditingController();
  final _phoneController = TextEditingController();
  
  bool _isLoading = false;
  String? _errorMessage;
  BillPaymentResponse? _lastPayment;

  @override
  void initState() {
    super.initState();
    if (widget.initialAccount != null) {
      _accountController.text = widget.initialAccount!;
    }
    if (widget.initialAmount != null) {
      _amountController.text = widget.initialAmount!.toStringAsFixed(2);
    }
  }

  @override
  void dispose() {
    _accountController.dispose();
    _amountController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  /// Enhanced payZESCO method with offline-first support
  Future<void> payZESCO(String account, double amount) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Offline-first: Cache receipt if network fails
      const billerCode = "ZESCO-001";
      
      final response = await LupiyaAPI.payBill(
        billerCode,
        account,
        amount,
        customerPhone: _phoneController.text.isNotEmpty ? _phoneController.text : null,
        userId: 'current_user_id', // Would come from auth service
      );

      setState(() {
        _lastPayment = response;
        _isLoading = false;
      });

      // Show success/queued message
      if (mounted) {
        _showPaymentResult(response);
      }

      // Notify parent widget
      widget.onPaymentComplete?.call(response);

    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });

      if (mounted) {
        _showErrorDialog(e.toString());
      }
    }
  }

  void _showPaymentResult(BillPaymentResponse response) {
    final isOffline = response.status == 'QUEUED';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          isOffline ? Icons.offline_bolt : Icons.check_circle,
          color: isOffline ? Colors.orange : Colors.green,
          size: 48,
        ),
        title: Text(
          isOffline ? 'Payment Queued' : 'Payment Successful',
          style: GoogleFonts.roboto(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Receipt: ${response.receiptNumber}'),
            Text('Amount: K${response.amountPaid.toStringAsFixed(2)}'),
            Text('Date: ${response.paymentDate.toString().substring(0, 16)}'),
            const SizedBox(height: 8),
            Text(
              response.message ?? (isOffline 
                ? 'Your payment will be processed when connection is restored.'
                : 'Payment completed successfully.'),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.error, color: Colors.red, size: 48),
        title: Text(
          'Payment Failed',
          style: GoogleFonts.roboto(fontWeight: FontWeight.bold),
        ),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2E7D32).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.electrical_services,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'ZESCO Electricity',
                        style: GoogleFonts.roboto(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Row(
                        children: [
                          Icon(
                            Icons.offline_bolt,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Offline payments supported',
                            style: GoogleFonts.roboto(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Account Number Field
              TextFormField(
                controller: _accountController,
                decoration: const InputDecoration(
                  labelText: 'ZESCO Account Number',
                  hintText: 'Enter 10-12 digit account number',
                  prefixIcon: Icon(Icons.account_balance),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter account number';
                  }
                  if (value.length < 10 || value.length > 12) {
                    return 'Account number must be 10-12 digits';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Amount Field
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'Amount (ZMW)',
                  hintText: 'Enter amount to pay',
                  prefixIcon: Icon(Icons.money),
                  prefixText: 'K ',
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter valid amount';
                  }
                  if (amount > 50000) {
                    return 'Maximum amount is K50,000';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Phone Number Field (Optional)
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number (Optional)',
                  hintText: 'For payment confirmation SMS',
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
              ),
              
              const SizedBox(height: 24),
              
              // Error Message
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: Colors.red[600], size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: Colors.red[600], fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              
              // Pay Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : () async {
                    if (_formKey.currentState!.validate()) {
                      final account = _accountController.text;
                      final amount = double.parse(_amountController.text);
                      await payZESCO(account, amount);
                    }
                  },
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(
                          'Pay ZESCO Bill',
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
              
              // Last Payment Info
              if (_lastPayment != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Last Payment',
                        style: GoogleFonts.roboto(
                          fontWeight: FontWeight.w600,
                          color: Colors.green[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Receipt: ${_lastPayment!.receiptNumber}',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        'Status: ${_lastPayment!.status}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
