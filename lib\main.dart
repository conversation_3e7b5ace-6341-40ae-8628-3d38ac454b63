import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:workmanager/workmanager.dart';

import 'core/config/app_config.dart';
import 'core/constants/app_constants.dart';
import 'core/security/encryption_service.dart';
import 'core/security/biometric_service.dart';
import 'core/security/compliance_service.dart';
import 'features/offline_sync/data/offline_sync_manager.dart';
import 'features/mobile_money/data/services/mobile_money_service.dart';
import 'features/utilities/data/services/utility_service.dart';
import 'wallet/zambia_wallets.dart';
import 'refresh/momo_refresh.dart';
import 'notifications/momo_alerts.dart';
import 'presentation/splash/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize core services
  await _initializeServices();

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(const ZambiaPayApp());
}

/// Initialize all core services
Future<void> _initializeServices() async {
  try {
    // 🇿🇲 INITIALIZE FEATURE LOCK SYSTEM FOR MOBILE MONEY MVP
    await Features.initialize();

    // 📱 SETUP WALLET-ONLY FLOW FOR ZAMBIAN MOBILE MONEY
    await ZambiaWallets.setupWalletOnlyFlow();

    // 🔄 INITIALIZE MOBILE MONEY REFRESH SYSTEM
    final momoRefresh = MomoRefreshController();
    await momoRefresh.initialize();
    enableBalanceRefresh(); // Enable 2G-optimized balance refresh

    // 🔔 INITIALIZE MOBILE MONEY NOTIFICATIONS
    final momoNotifications = MomoNotificationService();
    await momoNotifications.initialize();
    configureMomoNotifications(); // Configure with Zambian delivery guarantee

    // Initialize encryption service first
    await EncryptionService().initialize();

    // Initialize compliance and audit service
    await ComplianceService().initialize();

    // Initialize offline sync manager
    await OfflineSyncManager().initialize();

    // Initialize mobile money service with demo credentials
    await MobileMoneyService().initialize(
      mtnCredentials: {
        'apiKey': 'demo_mtn_api_key',
        'subscriptionKey': 'demo_mtn_subscription_key',
      },
      airtelCredentials: {
        'clientId': 'demo_airtel_client_id',
        'clientSecret': 'demo_airtel_client_secret',
      },
      zamtelCredentials: {
        'apiKey': 'demo_zamtel_api_key',
        'merchantId': 'demo_zamtel_merchant_id',
      },
    );

    // Initialize utility service
    await UtilityService().initialize(
      zescoCredentials: {
        'apiKey': 'demo_zesco_api_key',
        'merchantId': 'demo_zesco_merchant_id',
      },
    );

    print('✅ All services initialized successfully');
  } catch (e) {
    print('❌ Service initialization failed: $e');
  }
}

class ZambiaPayApp extends StatelessWidget {
  const ZambiaPayApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConfig.appName,
      debugShowCheckedModeBanner: false,

      // Localization
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppConfig.supportedLanguages.map((lang) => Locale(lang)).toList(),

      // Theme
      theme: _buildTheme(),

      // Home
      home: const SplashScreen(),
    );
  }

  ThemeData _buildTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF2E7D32), // Zambian green
        brightness: Brightness.light,
      ),
      textTheme: GoogleFonts.robotoTextTheme(),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.black87,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.defaultPadding,
        ),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
    );
  }
}


