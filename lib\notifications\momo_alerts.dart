/// Mobile Money Alert System for Pay Mule Zambia MVP
/// Configures notifications for mobile money transactions with Zambian delivery guarantees
/// Integrates with feature lock system and wallet-only flow
/// 
/// CORE MANDATE: Mobile money-only release • No banking features • Zero breakage

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../core/config/app_config.dart';
import '../features/feature_lock.dart';
import '../wallet/zambia_wallets.dart';
import '../offline/zambia_retry_policy.dart';
import '../features/auth/data/services/zambia_sms_service.dart';
import '../notifications/zambia_alert.dart';

/// Mobile money notification types for MVP
enum NotificationType {
  MONEY_RECEIVED('money_received', 'Money Received', Icons.attach_money),
  MONEY_SENT('money_sent', 'Money Sent', Icons.send),
  CHILIMBA_REQUEST('chilimba_request', 'Chilimba Request', Icons.group),
  CHILIMBA_CONTRIBUTION('chilimba_contribution', 'Chilimba Contribution', Icons.savings),
  UTILITY_CONFIRMATION('utility_confirmation', 'Utility Payment', Icons.receipt),
  UTILITY_REMINDER('utility_reminder', 'Bill Reminder', Icons.notification_important),
  BALANCE_LOW('balance_low', 'Low Balance', Icons.warning),
  TRANSACTION_FAILED('transaction_failed', 'Transaction Failed', Icons.error),
  AGENT_NEARBY('agent_nearby', 'Agent Nearby', Icons.location_on),
  AIRTIME_PURCHASED('airtime_purchased', 'Airtime Purchased', Icons.phone);

  const NotificationType(this.code, this.displayName, this.icon);
  
  final String code;
  final String displayName;
  final IconData icon;
}

/// Mobile Money Notification Service
/// Handles all mobile money-related notifications with Zambian delivery guarantees
class MomoNotificationService {
  static final MomoNotificationService _instance = MomoNotificationService._internal();
  factory MomoNotificationService() => _instance;
  MomoNotificationService._internal();

  final Logger _logger = Logger();
  final ZambiaAlertService _alertService = ZambiaAlertService();
  final ZambiaSMSService _smsService = ZambiaSMSService();

  // Configuration
  bool _isInitialized = false;
  final Set<NotificationType> _enabledTypes = {};
  DeliveryGuarantee? _deliveryGuarantee;

  /// Initialize mobile money notification system
  Future<void> initialize() async {
    if (_isInitialized) return;

    _logger.i('🔔 Initializing Mobile Money Notification System');
    
    // Ensure feature lock system is initialized
    if (!Features.isMobileMoneyEnabled()) {
      await Features.initialize();
    }

    // Initialize underlying alert service
    await _alertService.initialize();

    _isInitialized = true;
    _logger.i('✅ Mobile Money Notification System initialized');
  }

  /// Configure mobile money notifications (as per your specification)
  void configureMomoNotifications() {
    _logger.i('⚙️ Configuring mobile money notifications for MVP');
    
    // Enable core mobile money notification types
    enableTypes([
      NotificationType.MONEY_RECEIVED,
      NotificationType.CHILIMBA_REQUEST,
      NotificationType.UTILITY_CONFIRMATION,
    ]);
    
    // ZAMBIAN DELIVERY GUARANTEE
    enableDeliveryGuarantee(
      retryPolicy: ZambiaRetryPolicy(
        maxRetries: 3,
        baseDelay: const Duration(seconds: 5),
        maxDelay: const Duration(minutes: 5),
        backoffMultiplier: 2.0,
      ),
      fallback: SMSNotification(),
    );

    _logger.i('✅ Mobile money notifications configured successfully');
  }

  /// Enable specific notification types
  void enableTypes(List<NotificationType> types) {
    _logger.i('📱 Enabling notification types: ${types.map((t) => t.displayName).join(', ')}');
    
    for (final type in types) {
      _enabledTypes.add(type);
      _logger.d('  ✅ Enabled: ${type.displayName}');
    }
  }

  /// Disable specific notification types
  void disableTypes(List<NotificationType> types) {
    _logger.i('🔕 Disabling notification types: ${types.map((t) => t.displayName).join(', ')}');
    
    for (final type in types) {
      _enabledTypes.remove(type);
      _logger.d('  ❌ Disabled: ${type.displayName}');
    }
  }

  /// Enable delivery guarantee with retry policy and SMS fallback
  void enableDeliveryGuarantee({
    required ZambiaRetryPolicy retryPolicy,
    required SMSNotification fallback,
  }) {
    _logger.i('🛡️ Enabling Zambian delivery guarantee');
    
    _deliveryGuarantee = DeliveryGuarantee(
      retryPolicy: retryPolicy,
      fallback: fallback,
    );

    _logger.i('✅ Delivery guarantee enabled with SMS fallback');
  }

  /// Send mobile money notification
  Future<void> sendNotification({
    required String userId,
    required String phoneNumber,
    required NotificationType type,
    required Map<String, dynamic> data,
    String? transactionId,
  }) async {
    if (!_isInitialized) {
      throw Exception('Mobile money notification service not initialized');
    }

    if (!_enabledTypes.contains(type)) {
      _logger.d('Notification type ${type.displayName} is disabled, skipping');
      return;
    }

    // Only send notifications if mobile money features are enabled
    if (!Features.isEnabled(Features.MOBILE_MONEY)) {
      _logger.w('Mobile money features disabled, skipping notification');
      return;
    }

    _logger.i('📨 Sending ${type.displayName} notification to $phoneNumber');

    try {
      // Create notification message
      final message = _createNotificationMessage(type, data);
      
      // Send with delivery guarantee
      await _sendWithDeliveryGuarantee(
        userId: userId,
        phoneNumber: phoneNumber,
        type: type,
        message: message,
        data: data,
        transactionId: transactionId,
      );

      _logger.i('✅ ${type.displayName} notification sent successfully');

    } catch (e) {
      _logger.e('❌ Failed to send ${type.displayName} notification: $e');
      rethrow;
    }
  }

  /// Send notification with delivery guarantee
  Future<void> _sendWithDeliveryGuarantee({
    required String userId,
    required String phoneNumber,
    required NotificationType type,
    required String message,
    required Map<String, dynamic> data,
    String? transactionId,
  }) async {
    if (_deliveryGuarantee == null) {
      // Send without delivery guarantee
      await _sendDirectNotification(userId, phoneNumber, type, message, data, transactionId);
      return;
    }

    // Attempt to send with retry policy
    await _deliveryGuarantee!.retryPolicy.execute(() async {
      await _sendDirectNotification(userId, phoneNumber, type, message, data, transactionId);
    }, fallback: () async {
      // Use SMS fallback
      await _deliveryGuarantee!.fallback.send(phoneNumber, message);
    });
  }

  /// Send notification directly
  Future<void> _sendDirectNotification(
    String userId,
    String phoneNumber,
    NotificationType type,
    String message,
    Map<String, dynamic> data,
    String? transactionId,
  ) async {
    switch (type) {
      case NotificationType.MONEY_RECEIVED:
      case NotificationType.MONEY_SENT:
        await _sendTransactionNotification(userId, phoneNumber, type, message, data, transactionId);
        break;
      case NotificationType.CHILIMBA_REQUEST:
      case NotificationType.CHILIMBA_CONTRIBUTION:
        await _sendChilimbaNotification(userId, phoneNumber, type, message, data);
        break;
      case NotificationType.UTILITY_CONFIRMATION:
      case NotificationType.UTILITY_REMINDER:
        await _sendUtilityNotification(userId, phoneNumber, type, message, data);
        break;
      default:
        await _sendGeneralNotification(userId, phoneNumber, type, message, data);
        break;
    }
  }

  /// Send transaction notification
  Future<void> _sendTransactionNotification(
    String userId,
    String phoneNumber,
    NotificationType type,
    String message,
    Map<String, dynamic> data,
    String? transactionId,
  ) async {
    final amount = data['amount'] as double? ?? 0.0;
    
    // Use existing alert service for transaction notifications
    await _alertService.sendTransactionAlert(
      userId: userId,
      transactionId: transactionId ?? 'unknown',
      amount: amount,
      transactionType: _mapToTransactionType(type),
      phoneNumber: phoneNumber,
      additionalData: data,
    );
  }

  /// Send Chilimba notification
  Future<void> _sendChilimbaNotification(
    String userId,
    String phoneNumber,
    NotificationType type,
    String message,
    Map<String, dynamic> data,
  ) async {
    // Send push notification for Chilimba activities
    await _sendPushNotification(
      userId: userId,
      title: type.displayName,
      body: message,
      data: data,
      icon: type.icon,
    );

    // Also send SMS for important Chilimba requests
    if (type == NotificationType.CHILIMBA_REQUEST) {
      await _smsService.sendSMS(phoneNumber, message);
    }
  }

  /// Send utility notification
  Future<void> _sendUtilityNotification(
    String userId,
    String phoneNumber,
    NotificationType type,
    String message,
    Map<String, dynamic> data,
  ) async {
    // Send push notification for utility payments
    await _sendPushNotification(
      userId: userId,
      title: type.displayName,
      body: message,
      data: data,
      icon: type.icon,
    );

    // Send SMS confirmation for utility payments
    if (type == NotificationType.UTILITY_CONFIRMATION) {
      await _smsService.sendSMS(phoneNumber, message);
    }
  }

  /// Send general notification
  Future<void> _sendGeneralNotification(
    String userId,
    String phoneNumber,
    NotificationType type,
    String message,
    Map<String, dynamic> data,
  ) async {
    await _sendPushNotification(
      userId: userId,
      title: type.displayName,
      body: message,
      data: data,
      icon: type.icon,
    );
  }

  /// Send push notification
  Future<void> _sendPushNotification({
    required String userId,
    required String title,
    required String body,
    required Map<String, dynamic> data,
    required IconData icon,
  }) async {
    // Implementation would send push notification
    _logger.d('📱 Push notification: $title - $body');
  }

  /// Create notification message based on type and data
  String _createNotificationMessage(NotificationType type, Map<String, dynamic> data) {
    switch (type) {
      case NotificationType.MONEY_RECEIVED:
        final amount = data['amount'] ?? '0';
        final sender = data['sender'] ?? 'Unknown';
        return 'You received K$amount ZMW from $sender';
        
      case NotificationType.MONEY_SENT:
        final amount = data['amount'] ?? '0';
        final recipient = data['recipient'] ?? 'Unknown';
        return 'You sent K$amount ZMW to $recipient';
        
      case NotificationType.CHILIMBA_REQUEST:
        final groupName = data['group_name'] ?? 'Unknown Group';
        final amount = data['amount'] ?? '0';
        return 'Chilimba request: K$amount ZMW for $groupName';
        
      case NotificationType.UTILITY_CONFIRMATION:
        final provider = data['provider'] ?? 'Utility';
        final amount = data['amount'] ?? '0';
        return '$provider payment of K$amount ZMW confirmed';
        
      default:
        return data['message'] ?? 'Mobile money notification';
    }
  }

  /// Map notification type to transaction type
  TransactionType _mapToTransactionType(NotificationType type) {
    switch (type) {
      case NotificationType.MONEY_RECEIVED:
        return TransactionType.receive;
      case NotificationType.MONEY_SENT:
        return TransactionType.send;
      default:
        return TransactionType.other;
    }
  }

  // Getters
  bool get isInitialized => _isInitialized;
  Set<NotificationType> get enabledTypes => Set.from(_enabledTypes);
  bool get hasDeliveryGuarantee => _deliveryGuarantee != null;
}

/// Delivery guarantee configuration
class DeliveryGuarantee {
  final ZambiaRetryPolicy retryPolicy;
  final SMSNotification fallback;

  const DeliveryGuarantee({
    required this.retryPolicy,
    required this.fallback,
  });
}

/// SMS notification fallback
class SMSNotification {
  final Logger _logger = Logger();
  final ZambiaSMSService _smsService = ZambiaSMSService();

  /// Send SMS notification as fallback
  Future<void> send(String phoneNumber, String message) async {
    _logger.i('📨 Sending SMS fallback notification to $phoneNumber');
    
    try {
      await _smsService.sendSMS(phoneNumber, message);
      _logger.i('✅ SMS fallback sent successfully');
    } catch (e) {
      _logger.e('❌ SMS fallback failed: $e');
      rethrow;
    }
  }
}

/// Global function to configure mobile money notifications (as per your specification)
void configureMomoNotifications() {
  final service = MomoNotificationService();
  service.configureMomoNotifications();
}
