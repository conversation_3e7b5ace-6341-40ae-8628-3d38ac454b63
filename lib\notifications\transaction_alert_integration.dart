/// 🇿🇲 PAY MULE ZAMBIA - TRANSACTION ALERT INTEGRATION SERVICE
/// 
/// Integrates the Zambian alert system with transaction processing
/// Automatically triggers appropriate notifications based on transaction events
/// Handles fraud detection and security alerts
/// 
/// INTEGRATION POINTS:
/// - Mobile money transactions
/// - Utility payments
/// - Failed transactions
/// - Suspicious activity detection
/// - Account security events

import 'dart:async';
import 'package:logger/logger.dart';

import 'zambia_alert.dart';
import '../core/config/app_config.dart';
import '../core/security/encryption_service.dart';
import '../features/mobile_money/domain/entities/transaction.dart';

enum TransactionStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  suspicious
}

enum SecurityEventType {
  multipleFailedAttempts,
  unusualLocation,
  largeTransaction,
  offHoursActivity,
  deviceChange,
  suspiciousPattern
}

class TransactionAlertIntegration {
  static final TransactionAlertIntegration _instance = TransactionAlertIntegration._internal();
  factory TransactionAlertIntegration() => _instance;
  TransactionAlertIntegration._internal();

  static final Logger _logger = Logger();
  final ZambiaAlertService _alertService = ZambiaAlertService();
  final EncryptionService _encryptionService = EncryptionService();

  bool _isInitialized = false;
  StreamController<Map<String, dynamic>>? _transactionStreamController;
  StreamController<Map<String, dynamic>>? _securityEventStreamController;

  /// Initialize the transaction alert integration
  Future<void> initialize() async {
    try {
      _logger.i('🔗 Initializing transaction alert integration');

      // Initialize the Zambian alert service
      await _alertService.initialize();

      // Setup transaction alerts configuration
      _alertService.setupTransactionAlerts();

      // Initialize stream controllers for real-time processing
      _initializeStreamControllers();

      // Setup transaction event listeners
      _setupTransactionEventListeners();

      // Setup security event listeners
      _setupSecurityEventListeners();

      _isInitialized = true;
      _logger.i('✅ Transaction alert integration initialized successfully');

    } catch (e) {
      _logger.e('❌ Failed to initialize transaction alert integration: $e');
      rethrow;
    }
  }

  /// Process transaction completion and send appropriate alerts
  Future<void> processTransactionAlert({
    required String userId,
    required String transactionId,
    required double amount,
    required TransactionType transactionType,
    required String phoneNumber,
    required TransactionStatus status,
    String? merchantName,
    String? accountNumber,
    Map<String, dynamic>? metadata,
  }) async {
    if (!_isInitialized) {
      throw Exception('Transaction alert integration not initialized');
    }

    _logger.i('🔔 Processing transaction alert for transaction $transactionId');

    try {
      // Prepare additional data for alert
      final additionalData = {
        'merchant_name': merchantName,
        'account_number': accountNumber,
        'status': status.toString(),
        'timestamp': DateTime.now().toIso8601String(),
        'metadata': metadata,
      };

      // Send transaction alert based on status
      switch (status) {
        case TransactionStatus.completed:
          await _handleSuccessfulTransaction(
            userId: userId,
            transactionId: transactionId,
            amount: amount,
            transactionType: transactionType,
            phoneNumber: phoneNumber,
            additionalData: additionalData,
          );
          break;

        case TransactionStatus.failed:
          await _handleFailedTransaction(
            userId: userId,
            transactionId: transactionId,
            amount: amount,
            transactionType: transactionType,
            phoneNumber: phoneNumber,
            additionalData: additionalData,
          );
          break;

        case TransactionStatus.suspicious:
          await _handleSuspiciousTransaction(
            userId: userId,
            transactionId: transactionId,
            amount: amount,
            transactionType: transactionType,
            phoneNumber: phoneNumber,
            additionalData: additionalData,
          );
          break;

        default:
          _logger.i('No alert required for transaction status: $status');
      }

      // Check for fraud patterns
      await _checkForFraudPatterns(userId, amount, transactionType);

      _logger.i('✅ Transaction alert processed successfully');

    } catch (e) {
      _logger.e('❌ Failed to process transaction alert: $e');
      rethrow;
    }
  }

  /// Handle successful transaction alerts
  Future<void> _handleSuccessfulTransaction({
    required String userId,
    required String transactionId,
    required double amount,
    required TransactionType transactionType,
    required String phoneNumber,
    required Map<String, dynamic> additionalData,
  }) async {
    _logger.i('✅ Handling successful transaction alert');

    // Send tiered notification based on amount
    await _alertService.sendTransactionAlert(
      userId: userId,
      transactionId: transactionId,
      amount: amount,
      transactionType: transactionType,
      phoneNumber: phoneNumber,
      additionalData: additionalData,
    );

    // Log successful transaction for analytics
    await _logTransactionEvent(
      userId: userId,
      transactionId: transactionId,
      eventType: 'transaction_completed',
      amount: amount,
      additionalData: additionalData,
    );
  }

  /// Handle failed transaction alerts
  Future<void> _handleFailedTransaction({
    required String userId,
    required String transactionId,
    required double amount,
    required TransactionType transactionType,
    required String phoneNumber,
    required Map<String, dynamic> additionalData,
  }) async {
    _logger.i('❌ Handling failed transaction alert');

    // Create failed transaction message
    final failureReason = additionalData['failure_reason'] ?? 'Unknown error';
    final failedTransactionData = {
      ...additionalData,
      'alert_type': 'transaction_failed',
      'failure_reason': failureReason,
    };

    // Send alert for failed transaction (always send push notification)
    await _alertService.sendTransactionAlert(
      userId: userId,
      transactionId: transactionId,
      amount: amount,
      transactionType: TransactionType.failed,
      phoneNumber: phoneNumber,
      additionalData: failedTransactionData,
    );

    // Check for multiple failed attempts (potential fraud)
    await _checkMultipleFailedAttempts(userId);
  }

  /// Handle suspicious transaction alerts
  Future<void> _handleSuspiciousTransaction({
    required String userId,
    required String transactionId,
    required double amount,
    required TransactionType transactionType,
    required String phoneNumber,
    required Map<String, dynamic> additionalData,
  }) async {
    _logger.w('⚠️ Handling suspicious transaction alert');

    // Create suspicious transaction data
    final suspiciousData = {
      ...additionalData,
      'alert_type': 'suspicious_transaction',
      'risk_score': additionalData['risk_score'] ?? 'high',
      'fraud_indicators': additionalData['fraud_indicators'] ?? [],
    };

    // Always send immediate alert for suspicious transactions (all channels)
    await _alertService.sendTransactionAlert(
      userId: userId,
      transactionId: transactionId,
      amount: amount,
      transactionType: TransactionType.suspicious,
      phoneNumber: phoneNumber,
      additionalData: suspiciousData,
    );

    // Trigger security event
    await _triggerSecurityEvent(
      userId: userId,
      eventType: SecurityEventType.suspiciousPattern,
      severity: 'high',
      details: suspiciousData,
    );
  }

  /// Process security events and send alerts
  Future<void> processSecurityAlert({
    required String userId,
    required SecurityEventType eventType,
    required String phoneNumber,
    String severity = 'medium',
    Map<String, dynamic>? eventDetails,
  }) async {
    if (!_isInitialized) {
      throw Exception('Transaction alert integration not initialized');
    }

    _logger.w('🚨 Processing security alert for user $userId');

    try {
      // Create security alert message
      final alertMessage = _createSecurityAlertMessage(eventType, severity, eventDetails);

      // Determine alert priority based on severity
      final priority = _getSecurityAlertPriority(severity);

      // Send security alert (always use all available channels for security events)
      await _sendSecurityAlert(
        userId: userId,
        phoneNumber: phoneNumber,
        message: alertMessage,
        priority: priority,
        eventType: eventType,
        eventDetails: eventDetails,
      );

      _logger.i('✅ Security alert processed successfully');

    } catch (e) {
      _logger.e('❌ Failed to process security alert: $e');
      rethrow;
    }
  }

  /// Send security alert using all available channels
  Future<void> _sendSecurityAlert({
    required String userId,
    required String phoneNumber,
    required String message,
    required AlertPriority priority,
    required SecurityEventType eventType,
    Map<String, dynamic>? eventDetails,
  }) async {
    final securityTransactionId = 'SEC_${DateTime.now().millisecondsSinceEpoch}';

    // For security events, always use the highest tier (voice + SMS + push)
    await _alertService.sendTransactionAlert(
      userId: userId,
      transactionId: securityTransactionId,
      amount: 1000.0, // Use high amount to trigger all notification channels
      transactionType: TransactionType.suspicious,
      phoneNumber: phoneNumber,
      additionalData: {
        'alert_type': 'security_event',
        'event_type': eventType.toString(),
        'severity': priority.toString(),
        'message': message,
        'event_details': eventDetails,
      },
    );
  }

  /// Create security alert message
  String _createSecurityAlertMessage(
    SecurityEventType eventType,
    String severity,
    Map<String, dynamic>? eventDetails,
  ) {
    final timestamp = DateTime.now();
    
    String baseMessage;
    switch (eventType) {
      case SecurityEventType.multipleFailedAttempts:
        baseMessage = 'SECURITY ALERT: Multiple failed login attempts detected on your Pay Mule account';
        break;
      case SecurityEventType.unusualLocation:
        baseMessage = 'SECURITY ALERT: Unusual location access detected on your Pay Mule account';
        break;
      case SecurityEventType.largeTransaction:
        baseMessage = 'SECURITY ALERT: Large transaction attempted on your Pay Mule account';
        break;
      case SecurityEventType.offHoursActivity:
        baseMessage = 'SECURITY ALERT: Off-hours activity detected on your Pay Mule account';
        break;
      case SecurityEventType.deviceChange:
        baseMessage = 'SECURITY ALERT: New device access detected on your Pay Mule account';
        break;
      case SecurityEventType.suspiciousPattern:
        baseMessage = 'SECURITY ALERT: Suspicious activity pattern detected on your Pay Mule account';
        break;
    }

    return '$baseMessage at ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}. If this was not you, contact support immediately on +260-XXX-XXXX.';
  }

  /// Get security alert priority
  AlertPriority _getSecurityAlertPriority(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return AlertPriority.critical;
      case 'high':
        return AlertPriority.high;
      case 'medium':
        return AlertPriority.medium;
      default:
        return AlertPriority.low;
    }
  }

  /// Check for fraud patterns
  Future<void> _checkForFraudPatterns(
    String userId,
    double amount,
    TransactionType transactionType,
  ) async {
    // Implementation would check for various fraud patterns
    // This is a simplified version
    
    if (amount > 10000.0) { // K10,000+ transactions
      await _triggerSecurityEvent(
        userId: userId,
        eventType: SecurityEventType.largeTransaction,
        severity: 'high',
        details: {
          'amount': amount,
          'transaction_type': transactionType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    }

    // Check for off-hours activity
    final currentHour = DateTime.now().hour;
    if (currentHour < 6 || currentHour > 22) {
      await _triggerSecurityEvent(
        userId: userId,
        eventType: SecurityEventType.offHoursActivity,
        severity: 'medium',
        details: {
          'hour': currentHour,
          'amount': amount,
          'transaction_type': transactionType.toString(),
        },
      );
    }
  }

  /// Check for multiple failed attempts
  Future<void> _checkMultipleFailedAttempts(String userId) async {
    // Implementation would track failed attempts and trigger alerts
    // This is a simplified version
    
    // Simulate checking failed attempts count
    final failedAttempts = 3; // This would come from actual tracking
    
    if (failedAttempts >= 3) {
      await _triggerSecurityEvent(
        userId: userId,
        eventType: SecurityEventType.multipleFailedAttempts,
        severity: 'high',
        details: {
          'failed_attempts': failedAttempts,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    }
  }

  /// Trigger security event
  Future<void> _triggerSecurityEvent({
    required String userId,
    required SecurityEventType eventType,
    required String severity,
    required Map<String, dynamic> details,
  }) async {
    final securityEvent = {
      'user_id': userId,
      'event_type': eventType.toString(),
      'severity': severity,
      'details': details,
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Add to security event stream for real-time processing
    _securityEventStreamController?.add(securityEvent);

    // Log security event
    await _logSecurityEvent(securityEvent);
  }

  /// Initialize stream controllers
  void _initializeStreamControllers() {
    _transactionStreamController = StreamController<Map<String, dynamic>>.broadcast();
    _securityEventStreamController = StreamController<Map<String, dynamic>>.broadcast();
  }

  /// Setup transaction event listeners
  void _setupTransactionEventListeners() {
    _transactionStreamController?.stream.listen((transactionEvent) {
      _logger.i('📊 Processing transaction event: ${transactionEvent['event_type']}');
      // Process transaction events in real-time
    });
  }

  /// Setup security event listeners
  void _setupSecurityEventListeners() {
    _securityEventStreamController?.stream.listen((securityEvent) {
      _logger.w('🚨 Processing security event: ${securityEvent['event_type']}');
      // Process security events in real-time
    });
  }

  /// Log transaction event
  Future<void> _logTransactionEvent({
    required String userId,
    required String transactionId,
    required String eventType,
    required double amount,
    required Map<String, dynamic> additionalData,
  }) async {
    final logEntry = {
      'timestamp': DateTime.now().toIso8601String(),
      'user_id': userId,
      'transaction_id': transactionId,
      'event_type': eventType,
      'amount': amount,
      'additional_data': additionalData,
    };

    // Store encrypted log entry
    final encryptedLog = await _encryptionService.encryptData(logEntry.toString());
    // Implementation would store to secure logging system
  }

  /// Log security event
  Future<void> _logSecurityEvent(Map<String, dynamic> securityEvent) async {
    // Store encrypted security event log
    final encryptedLog = await _encryptionService.encryptData(securityEvent.toString());
    // Implementation would store to secure security logging system
  }

  /// Dispose resources
  void dispose() {
    _transactionStreamController?.close();
    _securityEventStreamController?.close();
  }

  /// Get transaction stream
  Stream<Map<String, dynamic>>? get transactionStream => _transactionStreamController?.stream;

  /// Get security event stream
  Stream<Map<String, dynamic>>? get securityEventStream => _securityEventStreamController?.stream;

  /// Check if integration is initialized
  bool get isInitialized => _isInitialized;
}
