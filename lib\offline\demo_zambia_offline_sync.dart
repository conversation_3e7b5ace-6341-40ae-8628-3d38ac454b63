#!/usr/bin/env dart

/// 🇿🇲 PAY MULE ZAMBIA - OFFLINE SYNC DEMONSTRATION
/// 
/// Demonstrates the Zambian offline synchronization system
/// Shows optimization for local connectivity challenges including:
/// - Rainy season network disruptions
/// - 2G network optimization  
/// - USSD/SMS fallback mechanisms
/// - Rural area connectivity issues
/// 
/// DEMONSTRATION SCENARIOS:
/// 1. Stable connectivity (dry season, 4G)
/// 2. Poor connectivity (rainy season, 2G)
/// 3. Offline mode with SMS fallback
/// 4. Emergency transaction via USSD
/// 5. Data conservation optimization

import 'dart:io';
import 'dart:async';

import 'zambia_sync.dart';
import 'zambia_retry_policy.dart';
import 'zambia_data_saver.dart';
import 'zambia_sms_gateway.dart';

class ZambiaOfflineSyncDemo {
  static const String version = '1.0.0';
  static const String demoId = 'ZAMBIA_OFFLINE_SYNC_DEMO_2025_08_01';

  /// Main demonstration function
  static Future<void> main(List<String> args) async {
    print('🇿🇲 PAY MULE ZAMBIA - OFFLINE SYNC DEMONSTRATION');
    print('=' * 70);
    print('Version: $version');
    print('Demo ID: $demoId');
    print('Timestamp: ${DateTime.now().toIso8601String()}');
    print('');
    print('This demo shows optimization for Zambian connectivity:');
    print('• Rainy season adjusted retry policies');
    print('• 2G network data conservation');
    print('• USSD/SMS emergency fallback');
    print('• Rural connectivity optimization');
    print('');

    final demo = ZambiaOfflineSyncDemo();

    try {
      await demo.runOfflineSyncDemo();
      print('');
      print('🎉 ZAMBIAN OFFLINE SYNC DEMONSTRATION COMPLETED');
      print('🔄 All connectivity scenarios have been demonstrated successfully');
      exit(0);
    } catch (e) {
      print('');
      print('💥 OFFLINE SYNC DEMONSTRATION FAILED: $e');
      exit(1);
    }
  }

  /// Run the complete offline sync demonstration
  Future<void> runOfflineSyncDemo() async {
    print('🚀 STARTING ZAMBIAN OFFLINE SYNC DEMONSTRATION');
    print('');

    // Initialize the offline sync system
    await _initializeOfflineSyncSystem();

    // Configure for Zambian connectivity
    await _configureForZambianConnectivity();

    // Demonstrate different connectivity scenarios
    await _demonstrateConnectivityScenarios();

    // Show emergency fallback capabilities
    await _demonstrateEmergencyFallback();

    // Display system statistics
    await _displaySystemStatistics();
  }

  /// Initialize the offline sync system
  Future<void> _initializeOfflineSyncSystem() async {
    print('🔧 INITIALIZING ZAMBIAN OFFLINE SYNC SYSTEM');
    print('─' * 50);

    print('• Initializing Zambian sync engine...');
    final syncEngine = ZambiaSyncEngine();
    await syncEngine.initialize();

    print('• Initializing SMS/USSD gateway...');
    final smsGateway = ZambiaSMSGateway();
    await smsGateway.initialize();

    print('✅ Offline sync system initialization completed');
    print('');
  }

  /// Configure for Zambian connectivity
  Future<void> _configureForZambianConnectivity() async {
    print('⚙️ CONFIGURING FOR ZAMBIAN CONNECTIVITY');
    print('─' * 50);

    print('• Setting up rainy season retry policy...');
    final retryPolicy = ZambiaRetryPolicy(
      connectivityLevel: ConnectivityLevel.stable2G,
      season: ZambianSeason.rainySeason,
      priority: SyncPriority.high,
    );

    print('• Configuring 2G data conservation...');
    final dataSaver = ZambiaDataSaver(
      connectivityLevel: ConnectivityLevel.stable2G,
    );

    print('• Setting up SMS/USSD emergency fallback...');
    final smsGateway = ZambiaSMSGateway();

    print('• Applying Zambian optimization configuration...');
    final syncEngine = ZambiaSyncEngine();
    syncEngine.optimizeForZMConnectivity();

    // Configure the sync engine
    SyncEngine.configure(
      retryPolicy: retryPolicy,
      dataConservation: dataSaver,
      emergencyFallback: smsGateway,
    );

    print('✅ Zambian connectivity configuration completed');
    print('');
  }

  /// Demonstrate different connectivity scenarios
  Future<void> _demonstrateConnectivityScenarios() async {
    print('📶 DEMONSTRATING CONNECTIVITY SCENARIOS');
    print('─' * 50);

    // Scenario 1: Stable connectivity (dry season, 4G)
    await _demonstrateStableConnectivity();

    // Scenario 2: Poor connectivity (rainy season, 2G)
    await _demonstratePoorConnectivity();

    // Scenario 3: Offline mode
    await _demonstrateOfflineMode();

    print('✅ Connectivity scenarios demonstration completed');
    print('');
  }

  /// Demonstrate stable connectivity scenario
  Future<void> _demonstrateStableConnectivity() async {
    print('📶 SCENARIO 1: STABLE CONNECTIVITY (Dry Season, 4G)');
    print('   Conditions: Good weather, urban area, 4G network');
    print('   Expected behavior: Fast sync, minimal compression, standard retry');
    print('');

    try {
      // Create retry policy for stable conditions
      final retryPolicy = ZambiaRetryPolicy(
        connectivityLevel: ConnectivityLevel.fourG,
        season: ZambianSeason.drySeasonStable,
        priority: SyncPriority.medium,
      );

      // Create data saver for good connectivity
      final dataSaver = ZambiaDataSaver(
        connectivityLevel: ConnectivityLevel.fourG,
      );

      // Simulate transaction sync
      await _simulateTransactionSync(
        scenario: 'Stable Connectivity',
        retryPolicy: retryPolicy,
        dataSaver: dataSaver,
        transactionAmount: 150.0,
      );

      print('   ✅ Stable connectivity scenario completed successfully');

    } catch (e) {
      print('   ❌ Stable connectivity scenario failed: $e');
    }
    print('');
  }

  /// Demonstrate poor connectivity scenario
  Future<void> _demonstratePoorConnectivity() async {
    print('📶 SCENARIO 2: POOR CONNECTIVITY (Rainy Season, 2G)');
    print('   Conditions: Heavy rains, rural area, 2G network');
    print('   Expected behavior: Aggressive compression, extended retry, data conservation');
    print('');

    try {
      // Create retry policy for poor conditions
      final retryPolicy = ZambiaRetryPolicy(
        connectivityLevel: ConnectivityLevel.poor2G,
        season: ZambianSeason.rainySeason,
        priority: SyncPriority.high,
      );

      // Create data saver for poor connectivity
      final dataSaver = ZambiaDataSaver(
        connectivityLevel: ConnectivityLevel.poor2G,
      );

      // Simulate transaction sync with poor connectivity
      await _simulateTransactionSync(
        scenario: 'Poor Connectivity',
        retryPolicy: retryPolicy,
        dataSaver: dataSaver,
        transactionAmount: 75.0,
        simulateFailures: true,
      );

      print('   ✅ Poor connectivity scenario completed successfully');

    } catch (e) {
      print('   ❌ Poor connectivity scenario failed: $e');
    }
    print('');
  }

  /// Demonstrate offline mode scenario
  Future<void> _demonstrateOfflineMode() async {
    print('📶 SCENARIO 3: OFFLINE MODE');
    print('   Conditions: No internet connectivity, power outage');
    print('   Expected behavior: Queue transactions, prepare for SMS fallback');
    print('');

    try {
      final syncEngine = ZambiaSyncEngine();

      // Queue offline transactions
      await syncEngine.queueOfflineTransaction(
        transactionId: 'TXN_OFFLINE_001',
        transactionData: {
          'amount': 50.0,
          'type': 'utility_payment',
          'merchant': 'ZESCO',
          'account': '********',
        },
        priority: SyncPriority.high,
        userId: 'user_offline_001',
      );

      print('   📥 Transaction queued for offline sync');
      print('   🔄 Will sync when connectivity is restored');
      print('   ✅ Offline mode scenario completed successfully');

    } catch (e) {
      print('   ❌ Offline mode scenario failed: $e');
    }
    print('');
  }

  /// Demonstrate emergency fallback capabilities
  Future<void> _demonstrateEmergencyFallback() async {
    print('🚨 DEMONSTRATING EMERGENCY FALLBACK CAPABILITIES');
    print('─' * 50);

    // SMS transaction fallback
    await _demonstrateSMSFallback();

    // USSD balance inquiry
    await _demonstrateUSSDFallback();

    // Emergency security alert
    await _demonstrateEmergencyAlert();

    print('✅ Emergency fallback demonstration completed');
    print('');
  }

  /// Demonstrate SMS transaction fallback
  Future<void> _demonstrateSMSFallback() async {
    print('📨 SMS TRANSACTION FALLBACK');
    print('   Use case: Critical transaction when internet is unavailable');
    print('');

    try {
      final smsGateway = ZambiaSMSGateway();

      final success = await smsGateway.sendTransactionViaSMS(
        transactionId: 'TXN_SMS_001',
        transactionData: {
          'amount': 500.0,
          'type': 'emergency_transfer',
          'recipient': '+************',
        },
        priority: SyncPriority.critical,
        phoneNumber: '+************',
      );

      if (success) {
        print('   ✅ Emergency transaction sent via SMS successfully');
      } else {
        print('   ⚠️ SMS transaction queued for retry');
      }

    } catch (e) {
      print('   ❌ SMS fallback failed: $e');
    }
    print('');
  }

  /// Demonstrate USSD balance inquiry
  Future<void> _demonstrateUSSDFallback() async {
    print('📞 USSD BALANCE INQUIRY');
    print('   Use case: Check balance when app is offline');
    print('');

    try {
      final smsGateway = ZambiaSMSGateway();

      final response = await smsGateway.sendUSSDBalanceInquiry(
        userId: 'user_ussd_001',
        phoneNumber: '+************',
      );

      if (response != null) {
        print('   📊 USSD Response: $response');
        print('   ✅ Balance inquiry completed via USSD');
      } else {
        print('   ⚠️ USSD inquiry failed');
      }

    } catch (e) {
      print('   ❌ USSD fallback failed: $e');
    }
    print('');
  }

  /// Demonstrate emergency security alert
  Future<void> _demonstrateEmergencyAlert() async {
    print('🚨 EMERGENCY SECURITY ALERT');
    print('   Use case: Send security alert when internet is down');
    print('');

    try {
      final smsGateway = ZambiaSMSGateway();

      final success = await smsGateway.sendEmergencySecurityAlert(
        userId: 'user_security_001',
        alertMessage: 'Suspicious login attempt detected on your Pay Mule account',
        phoneNumber: '+************',
        eventType: 'suspicious_login',
      );

      if (success) {
        print('   ✅ Emergency security alert sent via SMS');
      } else {
        print('   ⚠️ Security alert queued for retry');
      }

    } catch (e) {
      print('   ❌ Emergency alert failed: $e');
    }
    print('');
  }

  /// Simulate transaction sync with different conditions
  Future<void> _simulateTransactionSync({
    required String scenario,
    required ZambiaRetryPolicy retryPolicy,
    required ZambiaDataSaver dataSaver,
    required double transactionAmount,
    bool simulateFailures = false,
  }) async {
    print('   🔄 Simulating transaction sync...');

    // Optimize payload for transmission
    final originalPayload = {
      'transaction_id': 'TXN_${DateTime.now().millisecondsSinceEpoch}',
      'amount': transactionAmount,
      'type': 'mobile_money_transfer',
      'user_id': 'user_demo_001',
      'timestamp': DateTime.now().toIso8601String(),
      'metadata': {
        'device_info': 'Android 12',
        'app_version': '1.0.0',
        'location': 'Lusaka, Zambia',
      },
    };

    final optimizedPayload = await dataSaver.optimizePayload(
      originalPayload: originalPayload,
      payloadType: PayloadType.transaction,
      priority: SyncPriority.high,
    );

    print('   📊 Payload optimization:');
    if (optimizedPayload.containsKey('_optimization')) {
      final opt = optimizedPayload['_optimization'];
      print('      Original size: ${opt['original_size']} bytes');
      print('      Optimized size: ${opt['optimized_size']} bytes');
      print('      Savings: ${opt['savings_percent']}%');
    }

    // Simulate retry logic
    if (simulateFailures) {
      print('   🔄 Simulating network failures and retries...');
      
      for (int attempt = 1; attempt <= 3; attempt++) {
        final shouldRetry = retryPolicy.shouldRetry(attempt, RetryReason.networkTimeout);
        final retryInterval = retryPolicy.calculateRetryInterval(attempt, RetryReason.networkTimeout);
        
        print('      Attempt $attempt: ${shouldRetry ? "Retry" : "Give up"} '
              '(Next retry in ${retryInterval.inSeconds}s)');
        
        if (!shouldRetry) break;
        
        // Simulate retry delay (shortened for demo)
        await Future.delayed(Duration(milliseconds: 100));
      }
    }

    print('   ✅ Transaction sync simulation completed');
  }

  /// Display system statistics
  Future<void> _displaySystemStatistics() async {
    print('📊 SYSTEM STATISTICS');
    print('─' * 50);

    try {
      final syncEngine = ZambiaSyncEngine();
      final smsGateway = ZambiaSMSGateway();

      // Sync engine statistics
      final syncStats = syncEngine.getSyncStatistics();
      print('🔄 SYNC ENGINE:');
      print('   • Current connectivity: ${syncStats['current_connectivity']}');
      print('   • Current season: ${syncStats['current_season']}');
      print('   • Queue size: ${syncStats['total_items']} items');
      print('   • Pending items: ${syncStats['pending_items']}');
      print('');

      // SMS gateway statistics
      final smsStats = smsGateway.getGatewayStatistics();
      print('📱 SMS GATEWAY:');
      print('   • Queue size: ${smsStats['total_queue_items']} items');
      print('   • Pending: ${smsStats['pending_items']}');
      print('   • Completed: ${smsStats['completed_items']}');
      print('   • Failed: ${smsStats['failed_items']}');
      print('   • Networks supported: ${smsStats['networks_supported']}');
      print('');

      // Data conservation statistics
      if (SyncEngine.dataConservation != null) {
        final dataStats = SyncEngine.dataConservation!.getDataUsageStats();
        print('💾 DATA CONSERVATION:');
        print('   • Current usage: ${dataStats['current_usage_bytes']} bytes');
        print('   • Daily limit: ${dataStats['daily_limit_bytes']} bytes');
        print('   • Usage percentage: ${dataStats['usage_percentage']}%');
        print('   • Connectivity level: ${dataStats['connectivity_level']}');
        print('');
      }

      print('🇿🇲 ZAMBIAN OPTIMIZATION FEATURES:');
      print('   • Rainy season adjustments: ✅ Active');
      print('   • 2G network optimization: ✅ Enabled');
      print('   • SMS/USSD fallback: ✅ Ready');
      print('   • Rural connectivity support: ✅ Optimized');
      print('   • Data conservation: ✅ Aggressive');
      print('   • Emergency protocols: ✅ Configured');

    } catch (e) {
      print('❌ Failed to display statistics: $e');
    }
  }
}

/// Entry point for the Zambian offline sync demonstration
void main(List<String> args) async {
  await ZambiaOfflineSyncDemo.main(args);
}
