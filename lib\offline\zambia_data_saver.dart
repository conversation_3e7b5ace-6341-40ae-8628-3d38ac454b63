/// 🇿🇲 PAY MULE ZAMBIA - DATA CONSERVATION ENGINE
/// 
/// Optimizes data usage for Zambian network conditions
/// Specifically designed for 2G networks and limited data plans
/// 
/// FEATURES:
/// - Intelligent data compression
/// - Priority-based data transmission
/// - Bandwidth-aware payload optimization
/// - Image and media compression
/// - Delta sync for reduced data usage
/// - Smart caching strategies

import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';
import 'package:logger/logger.dart';
import 'package:archive/archive.dart';

import 'zambia_sync.dart';

enum DataCompressionLevel {
  none,
  light,
  medium,
  aggressive,
  maximum
}

enum PayloadType {
  transaction,
  userProfile,
  transactionHistory,
  balanceUpdate,
  securityEvent,
  analytics,
  media,
  configuration
}

class ZambiaDataSaver {
  static final Logger _logger = Logger();

  // Data limits by connectivity level (in bytes)
  static const Map<ConnectivityLevel, int> _dataLimits = {
    ConnectivityLevel.wifi: 1048576,        // 1MB
    ConnectivityLevel.fourG: 524288,        // 512KB
    ConnectivityLevel.threeG: 262144,       // 256KB
    ConnectivityLevel.stable2G: 131072,     // 128KB
    ConnectivityLevel.edge: 65536,          // 64KB
    ConnectivityLevel.poor2G: 32768,        // 32KB
    ConnectivityLevel.offline: 0,           // No data
  };

  // Compression levels by connectivity
  static const Map<ConnectivityLevel, DataCompressionLevel> _compressionLevels = {
    ConnectivityLevel.wifi: DataCompressionLevel.light,
    ConnectivityLevel.fourG: DataCompressionLevel.light,
    ConnectivityLevel.threeG: DataCompressionLevel.medium,
    ConnectivityLevel.stable2G: DataCompressionLevel.aggressive,
    ConnectivityLevel.edge: DataCompressionLevel.maximum,
    ConnectivityLevel.poor2G: DataCompressionLevel.maximum,
    ConnectivityLevel.offline: DataCompressionLevel.none,
  };

  // Priority weights for data transmission
  static const Map<SyncPriority, double> _priorityWeights = {
    SyncPriority.critical: 1.0,    // Always send
    SyncPriority.high: 0.8,        // Send if under 80% of limit
    SyncPriority.medium: 0.6,      // Send if under 60% of limit
    SyncPriority.low: 0.4,         // Send if under 40% of limit
  };

  final ConnectivityLevel _connectivityLevel;
  final Map<String, dynamic> _compressionCache = {};
  int _currentDataUsage = 0;
  DateTime _usageResetTime = DateTime.now();

  ZambiaDataSaver({required ConnectivityLevel connectivityLevel})
      : _connectivityLevel = connectivityLevel;

  /// Optimize payload for transmission
  Future<Map<String, dynamic>> optimizePayload({
    required Map<String, dynamic> originalPayload,
    required PayloadType payloadType,
    required SyncPriority priority,
  }) async {
    _logger.d('📊 Optimizing payload for ${payloadType.toString()}');

    try {
      // Check if we should send this payload based on data limits
      if (!_shouldSendPayload(priority)) {
        _logger.w('⚠️ Payload skipped due to data limits');
        return {'status': 'deferred', 'reason': 'data_limit_exceeded'};
      }

      // Apply payload-specific optimizations
      var optimizedPayload = await _applyPayloadOptimizations(
        originalPayload, 
        payloadType
      );

      // Apply compression
      optimizedPayload = await _compressPayload(optimizedPayload);

      // Apply field filtering based on connectivity
      optimizedPayload = _filterFields(optimizedPayload, payloadType);

      // Calculate data savings
      final originalSize = _calculatePayloadSize(originalPayload);
      final optimizedSize = _calculatePayloadSize(optimizedPayload);
      final savings = originalSize - optimizedSize;
      final savingsPercent = (savings / originalSize * 100).round();

      _logger.i('💾 Data optimization: ${originalSize} → ${optimizedSize} bytes '
               '(${savingsPercent}% savings)');

      // Update data usage tracking
      _updateDataUsage(optimizedSize);

      // Add optimization metadata
      optimizedPayload['_optimization'] = {
        'original_size': originalSize,
        'optimized_size': optimizedSize,
        'savings_bytes': savings,
        'savings_percent': savingsPercent,
        'compression_level': _compressionLevels[_connectivityLevel].toString(),
        'connectivity': _connectivityLevel.toString(),
        'optimized_at': DateTime.now().toIso8601String(),
      };

      return optimizedPayload;

    } catch (e) {
      _logger.e('❌ Payload optimization failed: $e');
      return originalPayload;
    }
  }

  /// Apply payload-specific optimizations
  Future<Map<String, dynamic>> _applyPayloadOptimizations(
    Map<String, dynamic> payload,
    PayloadType payloadType,
  ) async {
    switch (payloadType) {
      case PayloadType.transaction:
        return _optimizeTransactionPayload(payload);
      case PayloadType.userProfile:
        return _optimizeUserProfilePayload(payload);
      case PayloadType.transactionHistory:
        return _optimizeTransactionHistoryPayload(payload);
      case PayloadType.balanceUpdate:
        return _optimizeBalanceUpdatePayload(payload);
      case PayloadType.securityEvent:
        return _optimizeSecurityEventPayload(payload);
      case PayloadType.analytics:
        return _optimizeAnalyticsPayload(payload);
      case PayloadType.media:
        return _optimizeMediaPayload(payload);
      case PayloadType.configuration:
        return _optimizeConfigurationPayload(payload);
    }
  }

  /// Optimize transaction payload
  Map<String, dynamic> _optimizeTransactionPayload(Map<String, dynamic> payload) {
    final optimized = Map<String, dynamic>.from(payload);

    // Remove non-essential fields for 2G
    if (_connectivityLevel == ConnectivityLevel.poor2G || 
        _connectivityLevel == ConnectivityLevel.edge) {
      optimized.remove('metadata');
      optimized.remove('debug_info');
      optimized.remove('analytics_data');
    }

    // Compress timestamps
    if (optimized.containsKey('timestamp')) {
      optimized['ts'] = _compressTimestamp(optimized['timestamp']);
      optimized.remove('timestamp');
    }

    // Compress amount to minimal precision
    if (optimized.containsKey('amount')) {
      optimized['amt'] = _compressAmount(optimized['amount']);
      optimized.remove('amount');
    }

    // Use shorter field names
    _shortenFieldNames(optimized, {
      'transaction_id': 'tid',
      'user_id': 'uid',
      'merchant_id': 'mid',
      'reference_number': 'ref',
      'description': 'desc',
    });

    return optimized;
  }

  /// Optimize user profile payload
  Map<String, dynamic> _optimizeUserProfilePayload(Map<String, dynamic> payload) {
    final optimized = Map<String, dynamic>.from(payload);

    // Remove profile picture for poor connectivity
    if (_connectivityLevel == ConnectivityLevel.poor2G || 
        _connectivityLevel == ConnectivityLevel.edge) {
      optimized.remove('profile_picture');
      optimized.remove('avatar_url');
    }

    // Compress phone number
    if (optimized.containsKey('phone_number')) {
      optimized['phone'] = _compressPhoneNumber(optimized['phone_number']);
      optimized.remove('phone_number');
    }

    return optimized;
  }

  /// Optimize transaction history payload
  Map<String, dynamic> _optimizeTransactionHistoryPayload(Map<String, dynamic> payload) {
    final optimized = Map<String, dynamic>.from(payload);

    // Limit history items based on connectivity
    if (optimized.containsKey('transactions')) {
      final transactions = optimized['transactions'] as List;
      final maxItems = _getMaxHistoryItems();
      
      if (transactions.length > maxItems) {
        optimized['transactions'] = transactions.take(maxItems).toList();
        optimized['truncated'] = true;
        optimized['total_count'] = transactions.length;
      }
    }

    return optimized;
  }

  /// Optimize balance update payload
  Map<String, dynamic> _optimizeBalanceUpdatePayload(Map<String, dynamic> payload) {
    final optimized = Map<String, dynamic>.from(payload);

    // Keep only essential balance information
    final essentialFields = ['balance', 'currency', 'last_updated'];
    optimized.removeWhere((key, value) => !essentialFields.contains(key));

    return optimized;
  }

  /// Optimize security event payload
  Map<String, dynamic> _optimizeSecurityEventPayload(Map<String, dynamic> payload) {
    // Security events are always sent with minimal optimization
    return payload;
  }

  /// Optimize analytics payload
  Map<String, dynamic> _optimizeAnalyticsPayload(Map<String, dynamic> payload) {
    // Analytics can be heavily optimized or skipped for poor connectivity
    if (_connectivityLevel == ConnectivityLevel.poor2G) {
      return {'status': 'deferred', 'reason': 'poor_connectivity'};
    }

    final optimized = Map<String, dynamic>.from(payload);
    
    // Aggregate similar events
    if (optimized.containsKey('events')) {
      optimized['events'] = _aggregateAnalyticsEvents(optimized['events']);
    }

    return optimized;
  }

  /// Optimize media payload
  Map<String, dynamic> _optimizeMediaPayload(Map<String, dynamic> payload) {
    final optimized = Map<String, dynamic>.from(payload);

    // Skip media for poor connectivity
    if (_connectivityLevel == ConnectivityLevel.poor2G || 
        _connectivityLevel == ConnectivityLevel.edge) {
      return {'status': 'deferred', 'reason': 'poor_connectivity'};
    }

    // Compress images
    if (optimized.containsKey('image_data')) {
      optimized['image_data'] = _compressImageData(optimized['image_data']);
    }

    return optimized;
  }

  /// Optimize configuration payload
  Map<String, dynamic> _optimizeConfigurationPayload(Map<String, dynamic> payload) {
    final optimized = Map<String, dynamic>.from(payload);

    // Send only changed configuration values (delta sync)
    if (optimized.containsKey('config')) {
      optimized['config'] = _createConfigDelta(optimized['config']);
    }

    return optimized;
  }

  /// Compress payload using appropriate algorithm
  Future<Map<String, dynamic>> _compressPayload(Map<String, dynamic> payload) async {
    final compressionLevel = _compressionLevels[_connectivityLevel] ?? DataCompressionLevel.none;
    
    if (compressionLevel == DataCompressionLevel.none) {
      return payload;
    }

    try {
      final jsonString = jsonEncode(payload);
      final bytes = utf8.encode(jsonString);
      
      // Apply compression based on level
      Uint8List compressedBytes;
      switch (compressionLevel) {
        case DataCompressionLevel.light:
          compressedBytes = _lightCompression(bytes);
          break;
        case DataCompressionLevel.medium:
          compressedBytes = _mediumCompression(bytes);
          break;
        case DataCompressionLevel.aggressive:
          compressedBytes = _aggressiveCompression(bytes);
          break;
        case DataCompressionLevel.maximum:
          compressedBytes = _maximumCompression(bytes);
          break;
        case DataCompressionLevel.none:
          compressedBytes = Uint8List.fromList(bytes);
          break;
      }

      // Return compressed payload with metadata
      return {
        '_compressed': true,
        '_compression_level': compressionLevel.toString(),
        '_original_size': bytes.length,
        '_compressed_size': compressedBytes.length,
        '_data': base64Encode(compressedBytes),
      };

    } catch (e) {
      _logger.e('Compression failed: $e');
      return payload;
    }
  }

  /// Light compression using basic gzip
  Uint8List _lightCompression(List<int> bytes) {
    final encoder = GZipEncoder();
    return Uint8List.fromList(encoder.encode(bytes));
  }

  /// Medium compression with better compression ratio
  Uint8List _mediumCompression(List<int> bytes) {
    final encoder = GZipEncoder();
    encoder.level = 6; // Medium compression level
    return Uint8List.fromList(encoder.encode(bytes));
  }

  /// Aggressive compression for 2G networks
  Uint8List _aggressiveCompression(List<int> bytes) {
    final encoder = GZipEncoder();
    encoder.level = 9; // Maximum compression level
    return Uint8List.fromList(encoder.encode(bytes));
  }

  /// Maximum compression with additional optimizations
  Uint8List _maximumCompression(List<int> bytes) {
    // Apply multiple compression techniques
    final encoder = GZipEncoder();
    encoder.level = 9;
    
    // First pass: gzip compression
    var compressed = encoder.encode(bytes);
    
    // Second pass: custom dictionary compression for common patterns
    compressed = _applyDictionaryCompression(compressed);
    
    return Uint8List.fromList(compressed);
  }

  /// Apply dictionary compression for common Zambian patterns
  List<int> _applyDictionaryCompression(List<int> bytes) {
    // In a real implementation, this would use a dictionary of common
    // Zambian terms, phone numbers, and transaction patterns
    return bytes; // Simplified for demo
  }

  /// Filter fields based on connectivity and payload type
  Map<String, dynamic> _filterFields(Map<String, dynamic> payload, PayloadType payloadType) {
    if (_connectivityLevel == ConnectivityLevel.wifi || 
        _connectivityLevel == ConnectivityLevel.fourG) {
      return payload; // No filtering for good connectivity
    }

    final filtered = Map<String, dynamic>.from(payload);
    
    // Remove non-essential fields for poor connectivity
    final nonEssentialFields = [
      'debug_info',
      'analytics_metadata',
      'ui_state',
      'cache_data',
      'preview_data',
    ];

    for (final field in nonEssentialFields) {
      filtered.remove(field);
    }

    return filtered;
  }

  /// Helper methods for specific optimizations
  String _compressTimestamp(dynamic timestamp) {
    if (timestamp is String) {
      final dt = DateTime.parse(timestamp);
      return (dt.millisecondsSinceEpoch ~/ 1000).toString(); // Unix timestamp
    }
    return timestamp.toString();
  }

  String _compressAmount(dynamic amount) {
    if (amount is double) {
      return (amount * 100).round().toString(); // Store as cents
    }
    return amount.toString();
  }

  String _compressPhoneNumber(String phoneNumber) {
    // Remove country code and formatting for Zambian numbers
    return phoneNumber.replaceAll(RegExp(r'[^\d]'), '').replaceFirst('260', '');
  }

  void _shortenFieldNames(Map<String, dynamic> payload, Map<String, String> mapping) {
    for (final entry in mapping.entries) {
      if (payload.containsKey(entry.key)) {
        payload[entry.value] = payload[entry.key];
        payload.remove(entry.key);
      }
    }
  }

  int _getMaxHistoryItems() {
    switch (_connectivityLevel) {
      case ConnectivityLevel.wifi:
      case ConnectivityLevel.fourG:
        return 100;
      case ConnectivityLevel.threeG:
        return 50;
      case ConnectivityLevel.stable2G:
        return 20;
      case ConnectivityLevel.edge:
        return 10;
      case ConnectivityLevel.poor2G:
        return 5;
      case ConnectivityLevel.offline:
        return 0;
    }
  }

  List<dynamic> _aggregateAnalyticsEvents(List<dynamic> events) {
    // Group similar events and send aggregated counts
    final aggregated = <String, int>{};
    
    for (final event in events) {
      if (event is Map<String, dynamic> && event.containsKey('type')) {
        final type = event['type'].toString();
        aggregated[type] = (aggregated[type] ?? 0) + 1;
      }
    }

    return aggregated.entries.map((e) => {
      'type': e.key,
      'count': e.value,
    }).toList();
  }

  String _compressImageData(String imageData) {
    // In real implementation, would compress image data
    // For demo, just indicate compression
    return 'compressed_image_data';
  }

  Map<String, dynamic> _createConfigDelta(Map<String, dynamic> config) {
    // In real implementation, would compare with cached config
    // and return only changed values
    return config;
  }

  int _calculatePayloadSize(Map<String, dynamic> payload) {
    return utf8.encode(jsonEncode(payload)).length;
  }

  bool _shouldSendPayload(SyncPriority priority) {
    final limit = _dataLimits[_connectivityLevel] ?? 0;
    final threshold = (limit * (_priorityWeights[priority] ?? 0.5)).round();
    
    return _currentDataUsage < threshold;
  }

  void _updateDataUsage(int bytes) {
    // Reset usage counter daily
    final now = DateTime.now();
    if (now.difference(_usageResetTime).inDays >= 1) {
      _currentDataUsage = 0;
      _usageResetTime = now;
    }
    
    _currentDataUsage += bytes;
  }

  /// Create data saver configuration map
  Map<String, dynamic> toMap() {
    return {
      'connectivity_level': _connectivityLevel.toString(),
      'data_limits': _dataLimits.map(
        (key, value) => MapEntry(key.toString(), value)
      ),
      'compression_levels': _compressionLevels.map(
        (key, value) => MapEntry(key.toString(), value.toString())
      ),
      'priority_weights': _priorityWeights.map(
        (key, value) => MapEntry(key.toString(), value)
      ),
      'current_usage': _currentDataUsage,
      'usage_reset_time': _usageResetTime.toIso8601String(),
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Get data usage statistics
  Map<String, dynamic> getDataUsageStats() {
    final limit = _dataLimits[_connectivityLevel] ?? 0;
    final usagePercent = limit > 0 ? (_currentDataUsage / limit * 100).round() : 0;

    return {
      'current_usage_bytes': _currentDataUsage,
      'daily_limit_bytes': limit,
      'usage_percentage': usagePercent,
      'connectivity_level': _connectivityLevel.toString(),
      'reset_time': _usageResetTime.toIso8601String(),
    };
  }
}
