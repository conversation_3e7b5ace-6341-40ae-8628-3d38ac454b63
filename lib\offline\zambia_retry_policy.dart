/// 🇿🇲 PAY MULE ZAMBIA - RETRY POLICY
/// 
/// Intelligent retry policy optimized for Zambian network conditions
/// Adjusts retry intervals and strategies based on:
/// - Seasonal connectivity patterns (rainy vs dry season)
/// - Network quality and type (2G, 3G, 4G, WiFi)
/// - Time of day and network congestion
/// - Transaction priority and type
/// 
/// FEATURES:
/// - Rainy season adjusted retry intervals
/// - Exponential backoff with jitter
/// - Network-aware retry strategies
/// - Priority-based retry limits
/// - Smart failure detection

import 'dart:math';
import 'package:logger/logger.dart';

import 'zambia_sync.dart';

enum RetryReason {
  networkTimeout,
  serverError,
  connectionLost,
  serverBusy,
  authenticationFailed,
  dataCorruption,
  unknownError
}

class ZambiaRetryPolicy {
  static final Logger _logger = Logger();

  // Base retry intervals (in seconds)
  static const Map<ConnectivityLevel, int> _baseRetryIntervals = {
    ConnectivityLevel.wifi: 5,
    ConnectivityLevel.fourG: 10,
    ConnectivityLevel.threeG: 15,
    ConnectivityLevel.stable2G: 30,
    ConnectivityLevel.edge: 60,
    ConnectivityLevel.poor2G: 120,
    ConnectivityLevel.offline: 300,
  };

  // Maximum retry attempts by priority
  static const Map<SyncPriority, int> _maxRetryAttempts = {
    SyncPriority.critical: 10,
    SyncPriority.high: 7,
    SyncPriority.medium: 5,
    SyncPriority.low: 3,
  };

  // Seasonal multipliers for retry intervals
  static const Map<ZambianSeason, double> _seasonalMultipliers = {
    ZambianSeason.drySeasonStable: 1.0,
    ZambianSeason.transitionPeriod: 1.5,
    ZambianSeason.rainySeason: 2.5, // Longer intervals during rainy season
  };

  final ConnectivityLevel _connectivityLevel;
  final ZambianSeason _season;
  final SyncPriority _priority;

  ZambiaRetryPolicy({
    required ConnectivityLevel connectivityLevel,
    required ZambianSeason season,
    required SyncPriority priority,
  }) : _connectivityLevel = connectivityLevel,
       _season = season,
       _priority = priority;

  /// Calculate retry interval for given attempt number
  Duration calculateRetryInterval(int attemptNumber, RetryReason reason) {
    _logger.d('🔄 Calculating retry interval for attempt $attemptNumber');

    // Get base interval for current connectivity
    final baseInterval = _baseRetryIntervals[_connectivityLevel] ?? 300;

    // Apply seasonal multiplier
    final seasonalMultiplier = _seasonalMultipliers[_season] ?? 1.0;

    // Apply exponential backoff
    final exponentialMultiplier = pow(2, min(attemptNumber - 1, 6)).toDouble();

    // Apply reason-specific multiplier
    final reasonMultiplier = _getReasonMultiplier(reason);

    // Apply priority multiplier (critical items retry faster)
    final priorityMultiplier = _getPriorityMultiplier();

    // Calculate total interval
    var totalInterval = (baseInterval * 
                        seasonalMultiplier * 
                        exponentialMultiplier * 
                        reasonMultiplier * 
                        priorityMultiplier).round();

    // Add jitter to prevent thundering herd
    final jitter = _calculateJitter(totalInterval);
    totalInterval += jitter;

    // Apply maximum interval cap
    totalInterval = min(totalInterval, _getMaxInterval());

    _logger.d('  Base: ${baseInterval}s, Seasonal: ${seasonalMultiplier}x, '
             'Exponential: ${exponentialMultiplier}x, Reason: ${reasonMultiplier}x, '
             'Priority: ${priorityMultiplier}x, Jitter: ${jitter}s, '
             'Total: ${totalInterval}s');

    return Duration(seconds: totalInterval);
  }

  /// Check if retry should be attempted
  bool shouldRetry(int attemptNumber, RetryReason reason) {
    final maxAttempts = _maxRetryAttempts[_priority] ?? 3;

    // Always allow retry if under max attempts
    if (attemptNumber < maxAttempts) {
      return true;
    }

    // Special cases for critical transactions
    if (_priority == SyncPriority.critical) {
      // Allow extra retries for critical transactions during rainy season
      if (_season == ZambianSeason.rainySeason && attemptNumber < maxAttempts + 3) {
        return true;
      }

      // Allow extra retries for authentication failures (might be temporary)
      if (reason == RetryReason.authenticationFailed && attemptNumber < maxAttempts + 2) {
        return true;
      }
    }

    return false;
  }

  /// Get reason-specific multiplier
  double _getReasonMultiplier(RetryReason reason) {
    switch (reason) {
      case RetryReason.networkTimeout:
        return 1.5; // Network issues need longer intervals
      case RetryReason.connectionLost:
        return 2.0; // Connection issues need much longer intervals
      case RetryReason.serverBusy:
        return 1.8; // Server busy needs longer intervals
      case RetryReason.serverError:
        return 1.2; // Server errors might be temporary
      case RetryReason.authenticationFailed:
        return 0.8; // Auth failures can retry faster
      case RetryReason.dataCorruption:
        return 0.5; // Data issues can retry immediately
      case RetryReason.unknownError:
        return 1.0; // Default multiplier
    }
  }

  /// Get priority-specific multiplier
  double _getPriorityMultiplier() {
    switch (_priority) {
      case SyncPriority.critical:
        return 0.5; // Critical items retry faster
      case SyncPriority.high:
        return 0.8; // High priority items retry faster
      case SyncPriority.medium:
        return 1.0; // Normal retry interval
      case SyncPriority.low:
        return 1.5; // Low priority items retry slower
    }
  }

  /// Calculate jitter to prevent thundering herd
  int _calculateJitter(int baseInterval) {
    final random = Random();
    final maxJitter = (baseInterval * 0.1).round(); // 10% jitter
    return random.nextInt(maxJitter * 2) - maxJitter; // ±10% jitter
  }

  /// Get maximum interval based on connectivity and season
  int _getMaxInterval() {
    int baseMax;

    switch (_connectivityLevel) {
      case ConnectivityLevel.wifi:
      case ConnectivityLevel.fourG:
        baseMax = 300; // 5 minutes
        break;
      case ConnectivityLevel.threeG:
        baseMax = 600; // 10 minutes
        break;
      case ConnectivityLevel.stable2G:
        baseMax = 900; // 15 minutes
        break;
      case ConnectivityLevel.edge:
      case ConnectivityLevel.poor2G:
        baseMax = 1800; // 30 minutes
        break;
      case ConnectivityLevel.offline:
        baseMax = 3600; // 1 hour
        break;
    }

    // Adjust for season
    if (_season == ZambianSeason.rainySeason) {
      baseMax = (baseMax * 1.5).round(); // 50% longer during rainy season
    }

    return baseMax;
  }

  /// Get retry strategy recommendation
  RetryStrategy getRetryStrategy(int attemptNumber, RetryReason reason) {
    // For critical transactions during poor connectivity
    if (_priority == SyncPriority.critical && 
        (_connectivityLevel == ConnectivityLevel.poor2G || 
         _connectivityLevel == ConnectivityLevel.offline)) {
      return RetryStrategy.emergencyFallback;
    }

    // For high-priority transactions with repeated failures
    if (_priority == SyncPriority.high && attemptNumber >= 5) {
      return RetryStrategy.alternativeRoute;
    }

    // For authentication failures
    if (reason == RetryReason.authenticationFailed) {
      return RetryStrategy.refreshCredentials;
    }

    // For connection issues during rainy season
    if (reason == RetryReason.connectionLost && 
        _season == ZambianSeason.rainySeason) {
      return RetryStrategy.waitForBetterConnectivity;
    }

    return RetryStrategy.standardRetry;
  }

  /// Create policy configuration map
  Map<String, dynamic> toMap() {
    return {
      'connectivity_level': _connectivityLevel.toString(),
      'season': _season.toString(),
      'priority': _priority.toString(),
      'base_intervals': _baseRetryIntervals.map(
        (key, value) => MapEntry(key.toString(), value)
      ),
      'max_attempts': _maxRetryAttempts.map(
        (key, value) => MapEntry(key.toString(), value)
      ),
      'seasonal_multipliers': _seasonalMultipliers.map(
        (key, value) => MapEntry(key.toString(), value)
      ),
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Create policy from configuration map
  static ZambiaRetryPolicy fromMap(Map<String, dynamic> map) {
    return ZambiaRetryPolicy(
      connectivityLevel: ConnectivityLevel.values.firstWhere(
        (level) => level.toString() == map['connectivity_level'],
        orElse: () => ConnectivityLevel.stable2G,
      ),
      season: ZambianSeason.values.firstWhere(
        (season) => season.toString() == map['season'],
        orElse: () => ZambianSeason.drySeasonStable,
      ),
      priority: SyncPriority.values.firstWhere(
        (priority) => priority.toString() == map['priority'],
        orElse: () => SyncPriority.medium,
      ),
    );
  }

  /// Get human-readable retry policy description
  String getDescription() {
    final connectivity = _connectivityLevel.toString().split('.').last;
    final season = _season.toString().split('.').last;
    final priority = _priority.toString().split('.').last;

    return 'Zambian Retry Policy: $connectivity connectivity, $season, $priority priority';
  }

  /// Get estimated next retry time
  DateTime getNextRetryTime(int attemptNumber, RetryReason reason) {
    final interval = calculateRetryInterval(attemptNumber, reason);
    return DateTime.now().add(interval);
  }

  /// Check if it's a good time to retry (avoid peak hours during poor connectivity)
  bool isGoodTimeToRetry() {
    final now = DateTime.now();
    final hour = now.hour;

    // During poor connectivity, avoid peak hours (7-9 AM, 12-2 PM, 5-8 PM)
    if (_connectivityLevel == ConnectivityLevel.poor2G || 
        _connectivityLevel == ConnectivityLevel.edge) {
      
      if ((hour >= 7 && hour <= 9) ||   // Morning peak
          (hour >= 12 && hour <= 14) || // Lunch peak
          (hour >= 17 && hour <= 20)) { // Evening peak
        return false;
      }
    }

    return true;
  }

  /// Get recommended batch size for retry
  int getRecommendedBatchSize() {
    switch (_connectivityLevel) {
      case ConnectivityLevel.wifi:
      case ConnectivityLevel.fourG:
        return 20;
      case ConnectivityLevel.threeG:
        return 10;
      case ConnectivityLevel.stable2G:
        return 5;
      case ConnectivityLevel.edge:
        return 3;
      case ConnectivityLevel.poor2G:
        return 1;
      case ConnectivityLevel.offline:
        return 0;
    }
  }
}

enum RetryStrategy {
  standardRetry,
  emergencyFallback,
  alternativeRoute,
  refreshCredentials,
  waitForBetterConnectivity,
  skipAndContinue
}
