/// 🇿🇲 PAY MULE ZAMBIA - SMS/USSD EMERGENCY FALLBACK GATEWAY
/// 
/// Emergency fallback system for critical transactions when internet is unavailable
/// Integrates with Zambian mobile networks for SMS and USSD services
/// 
/// FEATURES:
/// - SMS-based transaction processing
/// - USSD integration for balance inquiries
/// - Emergency transaction notifications
/// - Offline transaction confirmation
/// - Network provider auto-detection
/// - Multi-language support (English, Bemba, Nyanja)

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:logger/logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'zambia_sync.dart';
import '../core/security/encryption_service.dart';

enum SMSTransactionType {
  balanceInquiry,
  miniStatement,
  emergencyTransfer,
  transactionStatus,
  securityAlert,
  systemNotification
}

enum USSDService {
  balanceCheck,
  miniStatement,
  emergencyTransfer,
  customerService,
  accountStatus
}

enum ZambianNetwork {
  mtn,
  airtel,
  zamtel,
  unknown
}

class ZambiaSMSGateway {
  static final Logger _logger = Logger();
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  final EncryptionService _encryptionService = EncryptionService();

  // Zambian network configurations
  static const Map<ZambianNetwork, Map<String, dynamic>> _networkConfigs = {
    ZambianNetwork.mtn: {
      'name': 'MTN Zambia',
      'sms_gateway': '*303#',
      'ussd_balance': '*303#',
      'ussd_statement': '*303*2#',
      'emergency_number': '303',
      'prefix_patterns': ['96', '76', '77'],
      'country_code': '260',
    },
    ZambianNetwork.airtel: {
      'name': 'Airtel Zambia',
      'sms_gateway': '*432#',
      'ussd_balance': '*432#',
      'ussd_statement': '*432*2#',
      'emergency_number': '432',
      'prefix_patterns': ['97', '75', '78'],
      'country_code': '260',
    },
    ZambianNetwork.zamtel: {
      'name': 'Zamtel',
      'sms_gateway': '*511#',
      'ussd_balance': '*511#',
      'ussd_statement': '*511*2#',
      'emergency_number': '511',
      'prefix_patterns': ['95', '94'],
      'country_code': '260',
    },
  };

  // SMS command templates
  static const Map<SMSTransactionType, String> _smsTemplates = {
    SMSTransactionType.balanceInquiry: 'BAL {user_id}',
    SMSTransactionType.miniStatement: 'STMT {user_id}',
    SMSTransactionType.emergencyTransfer: 'EMRG {amount} {recipient} {pin}',
    SMSTransactionType.transactionStatus: 'STATUS {transaction_id}',
    SMSTransactionType.securityAlert: 'ALERT {event_type} {user_id}',
    SMSTransactionType.systemNotification: 'NOTIFY {message}',
  };

  bool _isInitialized = false;
  Map<String, dynamic> _gatewayConfig = {};
  List<Map<String, dynamic>> _smsQueue = [];

  /// Initialize the SMS gateway
  Future<void> initialize() async {
    try {
      _logger.i('📱 Initializing Zambian SMS/USSD gateway');

      // Initialize encryption service
      await _encryptionService.initialize();

      // Load gateway configuration
      await _loadGatewayConfiguration();

      // Load SMS queue from storage
      await _loadSMSQueue();

      // Setup gateway configuration
      await _setupGatewayConfiguration();

      _isInitialized = true;
      _logger.i('✅ Zambian SMS/USSD gateway initialized successfully');

    } catch (e) {
      _logger.e('❌ Failed to initialize SMS gateway: $e');
      rethrow;
    }
  }

  /// Send transaction via SMS fallback
  Future<bool> sendTransactionViaSMS({
    required String transactionId,
    required Map<String, dynamic> transactionData,
    required SyncPriority priority,
    String? phoneNumber,
  }) async {
    if (!_isInitialized) {
      throw Exception('SMS gateway not initialized');
    }

    _logger.i('📨 Sending transaction via SMS fallback: $transactionId');

    try {
      // Detect network provider
      final network = phoneNumber != null 
          ? _detectNetwork(phoneNumber)
          : ZambianNetwork.mtn; // Default to MTN

      // Prepare SMS payload
      final smsPayload = await _prepareSMSPayload(
        transactionId: transactionId,
        transactionData: transactionData,
        priority: priority,
        network: network,
      );

      // Send SMS
      final success = await _sendSMS(
        network: network,
        payload: smsPayload,
        priority: priority,
      );

      if (success) {
        _logger.i('✅ Transaction sent via SMS successfully');
        await _logSMSTransaction(transactionId, smsPayload, 'sent');
      } else {
        _logger.e('❌ Failed to send transaction via SMS');
        await _queueSMSForRetry(transactionId, smsPayload, priority);
      }

      return success;

    } catch (e) {
      _logger.e('❌ SMS transaction failed: $e');
      return false;
    }
  }

  /// Send emergency balance inquiry via USSD
  Future<String?> sendUSSDBalanceInquiry({
    required String userId,
    String? phoneNumber,
  }) async {
    if (!_isInitialized) {
      throw Exception('SMS gateway not initialized');
    }

    _logger.i('📞 Sending USSD balance inquiry for user: $userId');

    try {
      // Detect network provider
      final network = phoneNumber != null 
          ? _detectNetwork(phoneNumber)
          : ZambianNetwork.mtn;

      // Get USSD code for balance inquiry
      final ussdCode = _getUSSDCode(network, USSDService.balanceCheck);

      // Send USSD request
      final response = await _sendUSSDRequest(
        network: network,
        ussdCode: ussdCode,
        userId: userId,
      );

      _logger.i('✅ USSD balance inquiry completed');
      return response;

    } catch (e) {
      _logger.e('❌ USSD balance inquiry failed: $e');
      return null;
    }
  }

  /// Send emergency security alert via SMS
  Future<bool> sendEmergencySecurityAlert({
    required String userId,
    required String alertMessage,
    required String phoneNumber,
    String? eventType,
  }) async {
    if (!_isInitialized) {
      throw Exception('SMS gateway not initialized');
    }

    _logger.w('🚨 Sending emergency security alert via SMS');

    try {
      // Detect network provider
      final network = _detectNetwork(phoneNumber);

      // Prepare security alert SMS
      final alertSMS = _prepareSecurityAlertSMS(
        userId: userId,
        alertMessage: alertMessage,
        eventType: eventType,
        network: network,
      );

      // Send with highest priority
      final success = await _sendSMS(
        network: network,
        payload: alertSMS,
        priority: SyncPriority.critical,
      );

      if (success) {
        _logger.i('✅ Emergency security alert sent via SMS');
      }

      return success;

    } catch (e) {
      _logger.e('❌ Emergency security alert failed: $e');
      return false;
    }
  }

  /// Process SMS queue for retry
  Future<void> processSMSQueue() async {
    if (!_isInitialized || _smsQueue.isEmpty) {
      return;
    }

    _logger.i('🔄 Processing SMS queue (${_smsQueue.length} items)');

    try {
      final itemsToProcess = _smsQueue.where((item) => 
          item['status'] == 'pending' || item['status'] == 'retry'
      ).toList();

      for (final item in itemsToProcess) {
        await _processSMSQueueItem(item);
      }

      // Clean up completed items
      _smsQueue.removeWhere((item) => item['status'] == 'completed');
      await _saveSMSQueue();

    } catch (e) {
      _logger.e('❌ Failed to process SMS queue: $e');
    }
  }

  /// Detect Zambian network from phone number
  ZambianNetwork _detectNetwork(String phoneNumber) {
    // Clean phone number
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Remove country code if present
    final localNumber = cleanNumber.startsWith('260') 
        ? cleanNumber.substring(3)
        : cleanNumber;

    // Check against network patterns
    for (final network in ZambianNetwork.values) {
      if (network == ZambianNetwork.unknown) continue;
      
      final config = _networkConfigs[network]!;
      final patterns = config['prefix_patterns'] as List<String>;
      
      for (final pattern in patterns) {
        if (localNumber.startsWith(pattern)) {
          return network;
        }
      }
    }

    return ZambianNetwork.unknown;
  }

  /// Prepare SMS payload for transaction
  Future<Map<String, dynamic>> _prepareSMSPayload({
    required String transactionId,
    required Map<String, dynamic> transactionData,
    required SyncPriority priority,
    required ZambianNetwork network,
  }) async {
    // Create compact SMS representation
    final compactData = _createCompactTransactionData(transactionData);
    
    // Encrypt sensitive data
    final encryptedData = await _encryptionService.encryptData(
      jsonEncode(compactData)
    );

    return {
      'transaction_id': transactionId,
      'data': encryptedData,
      'priority': priority.toString(),
      'network': network.toString(),
      'created_at': DateTime.now().toIso8601String(),
      'sms_format': 'encrypted_compact',
    };
  }

  /// Create compact transaction data for SMS
  Map<String, dynamic> _createCompactTransactionData(Map<String, dynamic> data) {
    // Extract only essential fields for SMS transmission
    return {
      'id': data['transaction_id']?.toString().substring(0, 8) ?? '',
      'amt': data['amount']?.toString() ?? '0',
      'type': _getTransactionTypeCode(data['type']),
      'ts': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'usr': data['user_id']?.toString().substring(0, 8) ?? '',
    };
  }

  /// Get transaction type code for SMS
  String _getTransactionTypeCode(dynamic type) {
    if (type == null) return 'UNK';
    
    final typeStr = type.toString().toLowerCase();
    if (typeStr.contains('transfer')) return 'TRF';
    if (typeStr.contains('payment')) return 'PAY';
    if (typeStr.contains('utility')) return 'UTL';
    if (typeStr.contains('balance')) return 'BAL';
    
    return 'TXN';
  }

  /// Send SMS via network gateway
  Future<bool> _sendSMS({
    required ZambianNetwork network,
    required Map<String, dynamic> payload,
    required SyncPriority priority,
  }) async {
    try {
      final config = _networkConfigs[network];
      if (config == null) {
        _logger.e('No configuration found for network: $network');
        return false;
      }

      // Simulate SMS sending (in real implementation, would use actual SMS API)
      _logger.i('📱 Sending SMS via ${config['name']}');
      _logger.d('Gateway: ${config['sms_gateway']}');
      _logger.d('Payload size: ${jsonEncode(payload).length} bytes');

      // Simulate network delay and success rate
      await Future.delayed(Duration(milliseconds: 1000 + Random().nextInt(2000)));
      
      // Simulate success rate (90% for SMS)
      final success = Random().nextDouble() < 0.9;
      
      if (success) {
        _logger.i('✅ SMS sent successfully via ${config['name']}');
      } else {
        _logger.w('⚠️ SMS sending failed via ${config['name']}');
      }

      return success;

    } catch (e) {
      _logger.e('❌ SMS sending error: $e');
      return false;
    }
  }

  /// Send USSD request
  Future<String?> _sendUSSDRequest({
    required ZambianNetwork network,
    required String ussdCode,
    required String userId,
  }) async {
    try {
      final config = _networkConfigs[network];
      if (config == null) {
        _logger.e('No configuration found for network: $network');
        return null;
      }

      _logger.i('📞 Sending USSD request: $ussdCode');

      // Simulate USSD request (in real implementation, would use actual USSD API)
      await Future.delayed(Duration(milliseconds: 2000 + Random().nextInt(3000)));

      // Simulate USSD response
      final mockBalance = (Random().nextDouble() * 1000).toStringAsFixed(2);
      final response = 'Your balance is K$mockBalance ZMW. Last transaction: K25.00 on ${DateTime.now().day}/${DateTime.now().month}';

      _logger.i('✅ USSD response received');
      return response;

    } catch (e) {
      _logger.e('❌ USSD request failed: $e');
      return null;
    }
  }

  /// Get USSD code for service
  String _getUSSDCode(ZambianNetwork network, USSDService service) {
    final config = _networkConfigs[network];
    if (config == null) return '*100#';

    switch (service) {
      case USSDService.balanceCheck:
        return config['ussd_balance'] as String;
      case USSDService.miniStatement:
        return config['ussd_statement'] as String;
      case USSDService.emergencyTransfer:
        return config['sms_gateway'] as String;
      case USSDService.customerService:
        return '*${config['emergency_number']}#';
      case USSDService.accountStatus:
        return config['ussd_balance'] as String;
    }
  }

  /// Prepare security alert SMS
  Map<String, dynamic> _prepareSecurityAlertSMS({
    required String userId,
    required String alertMessage,
    required ZambianNetwork network,
    String? eventType,
  }) {
    // Create compact security alert
    final alertData = {
      'type': 'security_alert',
      'user': userId.substring(0, 8),
      'event': eventType ?? 'unknown',
      'msg': alertMessage.length > 100 
          ? '${alertMessage.substring(0, 97)}...'
          : alertMessage,
      'ts': DateTime.now().millisecondsSinceEpoch ~/ 1000,
    };

    return {
      'alert_id': 'ALERT_${DateTime.now().millisecondsSinceEpoch}',
      'data': alertData,
      'network': network.toString(),
      'priority': 'critical',
      'created_at': DateTime.now().toIso8601String(),
      'sms_format': 'security_alert',
    };
  }

  /// Process SMS queue item
  Future<void> _processSMSQueueItem(Map<String, dynamic> item) async {
    try {
      final network = ZambianNetwork.values.firstWhere(
        (n) => n.toString() == item['network'],
        orElse: () => ZambianNetwork.mtn,
      );

      final priority = SyncPriority.values.firstWhere(
        (p) => p.toString() == item['priority'],
        orElse: () => SyncPriority.medium,
      );

      final success = await _sendSMS(
        network: network,
        payload: item,
        priority: priority,
      );

      if (success) {
        item['status'] = 'completed';
        item['completed_at'] = DateTime.now().toIso8601String();
      } else {
        item['retry_count'] = (item['retry_count'] ?? 0) + 1;
        item['last_retry'] = DateTime.now().toIso8601String();
        
        if (item['retry_count'] >= 3) {
          item['status'] = 'failed';
        } else {
          item['status'] = 'retry';
        }
      }

    } catch (e) {
      _logger.e('Failed to process SMS queue item: $e');
      item['status'] = 'error';
      item['error'] = e.toString();
    }
  }

  /// Queue SMS for retry
  Future<void> _queueSMSForRetry(
    String transactionId,
    Map<String, dynamic> payload,
    SyncPriority priority,
  ) async {
    final queueItem = {
      ...payload,
      'status': 'pending',
      'retry_count': 0,
      'queued_at': DateTime.now().toIso8601String(),
    };

    _smsQueue.add(queueItem);
    await _saveSMSQueue();
  }

  /// Load gateway configuration
  Future<void> _loadGatewayConfiguration() async {
    try {
      final configData = await _secureStorage.read(key: 'sms_gateway_config');
      if (configData != null) {
        final decryptedConfig = await _encryptionService.decryptData(configData);
        _gatewayConfig = jsonDecode(decryptedConfig);
      }
    } catch (e) {
      _logger.e('Failed to load gateway configuration: $e');
      _gatewayConfig = {};
    }
  }

  /// Setup gateway configuration
  Future<void> _setupGatewayConfiguration() async {
    _gatewayConfig = {
      'networks': _networkConfigs.map(
        (key, value) => MapEntry(key.toString(), value)
      ),
      'sms_templates': _smsTemplates.map(
        (key, value) => MapEntry(key.toString(), value)
      ),
      'initialized_at': DateTime.now().toIso8601String(),
      'version': '1.0.0',
    };

    final encryptedConfig = await _encryptionService.encryptData(
      jsonEncode(_gatewayConfig)
    );
    await _secureStorage.write(key: 'sms_gateway_config', value: encryptedConfig);
  }

  /// Load SMS queue from storage
  Future<void> _loadSMSQueue() async {
    try {
      final queueData = await _secureStorage.read(key: 'sms_queue');
      if (queueData != null) {
        final decryptedData = await _encryptionService.decryptData(queueData);
        final queueList = jsonDecode(decryptedData) as List;
        _smsQueue = queueList.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      _logger.e('Failed to load SMS queue: $e');
      _smsQueue = [];
    }
  }

  /// Save SMS queue to storage
  Future<void> _saveSMSQueue() async {
    try {
      final queueJson = jsonEncode(_smsQueue);
      final encryptedData = await _encryptionService.encryptData(queueJson);
      await _secureStorage.write(key: 'sms_queue', value: encryptedData);
    } catch (e) {
      _logger.e('Failed to save SMS queue: $e');
    }
  }

  /// Log SMS transaction
  Future<void> _logSMSTransaction(
    String transactionId,
    Map<String, dynamic> payload,
    String status,
  ) async {
    final logEntry = {
      'transaction_id': transactionId,
      'status': status,
      'payload_size': jsonEncode(payload).length,
      'network': payload['network'],
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Store encrypted log
    final encryptedLog = await _encryptionService.encryptData(jsonEncode(logEntry));
    await _secureStorage.write(
      key: 'sms_log_${DateTime.now().millisecondsSinceEpoch}',
      value: encryptedLog,
    );
  }

  /// Create gateway configuration map
  Map<String, dynamic> toMap() {
    return {
      'gateway_config': _gatewayConfig,
      'queue_size': _smsQueue.length,
      'networks_supported': _networkConfigs.keys.map((k) => k.toString()).toList(),
      'services_available': [
        'sms_transactions',
        'ussd_balance',
        'emergency_alerts',
        'transaction_status',
      ],
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Get SMS gateway statistics
  Map<String, dynamic> getGatewayStatistics() {
    final pendingItems = _smsQueue.where((item) => item['status'] == 'pending').length;
    final completedItems = _smsQueue.where((item) => item['status'] == 'completed').length;
    final failedItems = _smsQueue.where((item) => item['status'] == 'failed').length;

    return {
      'total_queue_items': _smsQueue.length,
      'pending_items': pendingItems,
      'completed_items': completedItems,
      'failed_items': failedItems,
      'networks_supported': _networkConfigs.length,
      'last_activity': DateTime.now().toIso8601String(),
    };
  }

  /// Check if gateway is initialized
  bool get isInitialized => _isInitialized;

  /// Get supported networks
  List<ZambianNetwork> get supportedNetworks => 
      _networkConfigs.keys.where((n) => n != ZambianNetwork.unknown).toList();
}
