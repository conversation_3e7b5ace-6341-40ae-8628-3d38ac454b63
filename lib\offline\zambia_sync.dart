/// 🇿🇲 PAY MULE ZAMBIA - OFFLINE SYNCHRONIZATION ENGINE
/// 
/// Optimized for Zambian connectivity challenges including:
/// - Rainy season network disruptions
/// - 2G network optimization
/// - USSD/SMS fallback mechanisms
/// - Rural area connectivity issues
/// - Power outage resilience
/// 
/// FEATURES:
/// - Intelligent retry policies for seasonal variations
/// - Data conservation for limited bandwidth
/// - Emergency fallback to USSD/SMS
/// - Offline transaction queuing
/// - Smart sync prioritization

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:logger/logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../core/config/app_config.dart';
import '../core/security/encryption_service.dart';
import '../notifications/zambia_alert.dart';
import 'zambia_retry_policy.dart';
import 'zambia_data_saver.dart';
import 'zambia_sms_gateway.dart';

enum ConnectivityLevel {
  offline,
  poor2G,
  stable2G,
  edge,
  threeG,
  fourG,
  wifi
}

enum SyncPriority {
  critical,    // Security alerts, failed transactions
  high,        // Completed transactions, balance updates
  medium,      // Transaction history, user preferences
  low          // Analytics, non-essential data
}

enum ZambianSeason {
  drySeasonStable,     // May - October (stable connectivity)
  rainySeason,         // November - April (unstable connectivity)
  transitionPeriod     // Seasonal transition periods
}

class ZambiaSyncEngine {
  static final ZambiaSyncEngine _instance = ZambiaSyncEngine._internal();
  factory ZambiaSyncEngine() => _instance;
  ZambiaSyncEngine._internal();

  static final Logger _logger = Logger();
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  final EncryptionService _encryptionService = EncryptionService();
  final Connectivity _connectivity = Connectivity();

  bool _isInitialized = false;
  ConnectivityLevel _currentConnectivity = ConnectivityLevel.offline;
  ZambianSeason _currentSeason = ZambianSeason.drySeasonStable;
  Timer? _syncTimer;
  List<Map<String, dynamic>> _offlineQueue = [];
  Map<String, dynamic> _syncConfiguration = {};

  /// Initialize the Zambian sync engine
  Future<void> initialize() async {
    try {
      _logger.i('🇿🇲 Initializing Zambian offline sync engine');

      // Initialize encryption service
      await _encryptionService.initialize();

      // Detect current Zambian season
      await _detectZambianSeason();

      // Load offline queue from storage
      await _loadOfflineQueue();

      // Setup connectivity monitoring
      await _setupConnectivityMonitoring();

      // Configure sync engine for Zambian conditions
      optimizeForZMConnectivity();

      _isInitialized = true;
      _logger.i('✅ Zambian sync engine initialized successfully');

    } catch (e) {
      _logger.e('❌ Failed to initialize Zambian sync engine: $e');
      rethrow;
    }
  }

  /// 🇿🇲 OPTIMIZE FOR ZAMBIAN CONNECTIVITY
  /// Configures sync engine for local network conditions
  void optimizeForZMConnectivity() {
    _logger.i('🔧 Optimizing sync engine for Zambian connectivity');

    SyncEngine.configure(
      retryPolicy: ZambiaRetryPolicy(),     // Rainy-season adjusted
      dataConservation: ZambiaDataSaver(),  // 2G optimization
      emergencyFallback: ZambiaSMSGateway() // USSD fallback
    );

    _logger.i('✅ Zambian connectivity optimization completed');
  }

  /// Configure sync engine with Zambian-specific policies
  static void configure({
    required ZambiaRetryPolicy retryPolicy,
    required ZambiaDataSaver dataConservation,
    required ZambiaSMSGateway emergencyFallback,
  }) {
    final instance = ZambiaSyncEngine();
    
    instance._syncConfiguration = {
      'retry_policy': retryPolicy.toMap(),
      'data_conservation': dataConservation.toMap(),
      'emergency_fallback': emergencyFallback.toMap(),
      'configured_at': DateTime.now().toIso8601String(),
    };

    instance._logger.i('🔧 Sync engine configured with Zambian policies');
  }

  /// Add transaction to offline queue
  Future<void> queueOfflineTransaction({
    required String transactionId,
    required Map<String, dynamic> transactionData,
    required SyncPriority priority,
    String? userId,
  }) async {
    if (!_isInitialized) {
      throw Exception('Zambian sync engine not initialized');
    }

    _logger.i('📥 Queuing offline transaction: $transactionId');

    try {
      final queueItem = {
        'id': transactionId,
        'data': transactionData,
        'priority': priority.toString(),
        'user_id': userId,
        'queued_at': DateTime.now().toIso8601String(),
        'retry_count': 0,
        'last_attempt': null,
        'sync_status': 'pending',
      };

      // Encrypt sensitive transaction data
      final encryptedData = await _encryptionService.encryptData(
        jsonEncode(queueItem['data'])
      );
      queueItem['data'] = encryptedData;

      // Add to queue with priority ordering
      _offlineQueue.add(queueItem);
      _sortQueueByPriority();

      // Save queue to secure storage
      await _saveOfflineQueue();

      _logger.i('✅ Transaction queued successfully (Priority: $priority)');

      // Attempt immediate sync if connectivity allows
      if (_currentConnectivity != ConnectivityLevel.offline) {
        await _attemptSync();
      }

    } catch (e) {
      _logger.e('❌ Failed to queue offline transaction: $e');
      rethrow;
    }
  }

  /// Process offline queue based on connectivity
  Future<void> processOfflineQueue() async {
    if (!_isInitialized || _offlineQueue.isEmpty) {
      return;
    }

    _logger.i('🔄 Processing offline queue (${_offlineQueue.length} items)');

    try {
      // Filter items based on current connectivity
      final processableItems = _getProcessableItems();

      for (final item in processableItems) {
        await _processSyncItem(item);
      }

      // Clean up completed items
      _offlineQueue.removeWhere((item) => item['sync_status'] == 'completed');
      await _saveOfflineQueue();

    } catch (e) {
      _logger.e('❌ Failed to process offline queue: $e');
    }
  }

  /// Get items that can be processed based on current connectivity
  List<Map<String, dynamic>> _getProcessableItems() {
    final maxItems = _getMaxItemsForConnectivity();
    final priorityOrder = ['critical', 'high', 'medium', 'low'];

    final processableItems = <Map<String, dynamic>>[];

    for (final priority in priorityOrder) {
      final priorityItems = _offlineQueue
          .where((item) => 
              item['priority'] == priority && 
              item['sync_status'] == 'pending')
          .take(maxItems - processableItems.length)
          .toList();

      processableItems.addAll(priorityItems);

      if (processableItems.length >= maxItems) {
        break;
      }
    }

    return processableItems;
  }

  /// Get maximum items to process based on connectivity
  int _getMaxItemsForConnectivity() {
    switch (_currentConnectivity) {
      case ConnectivityLevel.wifi:
      case ConnectivityLevel.fourG:
        return 50;
      case ConnectivityLevel.threeG:
        return 20;
      case ConnectivityLevel.stable2G:
        return 10;
      case ConnectivityLevel.edge:
        return 5;
      case ConnectivityLevel.poor2G:
        return 2;
      case ConnectivityLevel.offline:
        return 0;
    }
  }

  /// Process individual sync item
  Future<void> _processSyncItem(Map<String, dynamic> item) async {
    try {
      _logger.i('🔄 Processing sync item: ${item['id']}');

      // Decrypt transaction data
      final encryptedData = item['data'] as String;
      final decryptedData = await _encryptionService.decryptData(encryptedData);
      final transactionData = jsonDecode(decryptedData) as Map<String, dynamic>;

      // Attempt to sync based on priority and connectivity
      final success = await _syncTransactionData(item['id'], transactionData);

      if (success) {
        item['sync_status'] = 'completed';
        item['synced_at'] = DateTime.now().toIso8601String();
        _logger.i('✅ Sync completed for item: ${item['id']}');
      } else {
        item['retry_count'] = (item['retry_count'] as int) + 1;
        item['last_attempt'] = DateTime.now().toIso8601String();
        
        // Check if we should use emergency fallback
        if (_shouldUseEmergencyFallback(item)) {
          await _useEmergencyFallback(item, transactionData);
        }
      }

    } catch (e) {
      _logger.e('❌ Failed to process sync item ${item['id']}: $e');
      item['sync_status'] = 'error';
      item['error'] = e.toString();
    }
  }

  /// Sync transaction data to server
  Future<bool> _syncTransactionData(String transactionId, Map<String, dynamic> data) async {
    try {
      // Simulate API call with connectivity-appropriate timeout
      final timeout = _getTimeoutForConnectivity();
      
      // In real implementation, this would make actual HTTP request
      await Future.delayed(Duration(milliseconds: 500));
      
      // Simulate success rate based on connectivity and season
      final successRate = _getSuccessRateForConditions();
      final random = Random();
      
      return random.nextDouble() < successRate;

    } catch (e) {
      _logger.e('Sync failed for transaction $transactionId: $e');
      return false;
    }
  }

  /// Get timeout based on current connectivity
  Duration _getTimeoutForConnectivity() {
    switch (_currentConnectivity) {
      case ConnectivityLevel.wifi:
      case ConnectivityLevel.fourG:
        return Duration(seconds: 10);
      case ConnectivityLevel.threeG:
        return Duration(seconds: 15);
      case ConnectivityLevel.stable2G:
        return Duration(seconds: 30);
      case ConnectivityLevel.edge:
        return Duration(seconds: 45);
      case ConnectivityLevel.poor2G:
        return Duration(seconds: 60);
      case ConnectivityLevel.offline:
        return Duration(seconds: 5);
    }
  }

  /// Get success rate based on connectivity and season
  double _getSuccessRateForConditions() {
    double baseRate;
    
    switch (_currentConnectivity) {
      case ConnectivityLevel.wifi:
        baseRate = 0.95;
        break;
      case ConnectivityLevel.fourG:
        baseRate = 0.90;
        break;
      case ConnectivityLevel.threeG:
        baseRate = 0.85;
        break;
      case ConnectivityLevel.stable2G:
        baseRate = 0.75;
        break;
      case ConnectivityLevel.edge:
        baseRate = 0.60;
        break;
      case ConnectivityLevel.poor2G:
        baseRate = 0.40;
        break;
      case ConnectivityLevel.offline:
        baseRate = 0.0;
        break;
    }

    // Adjust for Zambian seasonal conditions
    switch (_currentSeason) {
      case ZambianSeason.rainySeason:
        baseRate *= 0.7; // 30% reduction during rainy season
        break;
      case ZambianSeason.transitionPeriod:
        baseRate *= 0.85; // 15% reduction during transition
        break;
      case ZambianSeason.drySeasonStable:
        // No adjustment needed
        break;
    }

    return baseRate;
  }

  /// Check if emergency fallback should be used
  bool _shouldUseEmergencyFallback(Map<String, dynamic> item) {
    final retryCount = item['retry_count'] as int;
    final priority = item['priority'] as String;
    
    // Use emergency fallback for critical items after 2 retries
    if (priority == 'critical' && retryCount >= 2) {
      return true;
    }
    
    // Use emergency fallback for high priority items after 5 retries
    if (priority == 'high' && retryCount >= 5) {
      return true;
    }
    
    // Use emergency fallback if offline for extended period
    if (_currentConnectivity == ConnectivityLevel.offline && retryCount >= 3) {
      return true;
    }
    
    return false;
  }

  /// Use emergency fallback (SMS/USSD)
  Future<void> _useEmergencyFallback(Map<String, dynamic> item, Map<String, dynamic> data) async {
    _logger.w('📱 Using emergency fallback for item: ${item['id']}');
    
    try {
      final fallbackGateway = ZambiaSMSGateway();
      await fallbackGateway.sendTransactionViaSMS(
        transactionId: item['id'],
        transactionData: data,
        priority: SyncPriority.values.firstWhere(
          (p) => p.toString() == item['priority']
        ),
      );
      
      item['sync_status'] = 'fallback_sent';
      item['fallback_used'] = true;
      item['fallback_at'] = DateTime.now().toIso8601String();
      
    } catch (e) {
      _logger.e('❌ Emergency fallback failed: $e');
    }
  }

  /// Detect current Zambian season for connectivity optimization
  Future<void> _detectZambianSeason() async {
    final now = DateTime.now();
    final month = now.month;
    
    if (month >= 11 || month <= 4) {
      _currentSeason = ZambianSeason.rainySeason;
      _logger.i('🌧️ Detected rainy season - adjusting connectivity expectations');
    } else if (month == 5 || month == 10) {
      _currentSeason = ZambianSeason.transitionPeriod;
      _logger.i('🔄 Detected transition period - moderate connectivity adjustments');
    } else {
      _currentSeason = ZambianSeason.drySeasonStable;
      _logger.i('☀️ Detected dry season - stable connectivity expected');
    }
  }

  /// Setup connectivity monitoring
  Future<void> _setupConnectivityMonitoring() async {
    // Monitor connectivity changes
    _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
      _updateConnectivityLevel(result);
    });

    // Initial connectivity check
    final result = await _connectivity.checkConnectivity();
    _updateConnectivityLevel(result);

    // Setup periodic sync attempts
    _syncTimer = Timer.periodic(Duration(minutes: 5), (timer) {
      if (_offlineQueue.isNotEmpty) {
        processOfflineQueue();
      }
    });
  }

  /// Update connectivity level based on connection type
  void _updateConnectivityLevel(ConnectivityResult result) {
    final previousLevel = _currentConnectivity;
    
    switch (result) {
      case ConnectivityResult.wifi:
        _currentConnectivity = ConnectivityLevel.wifi;
        break;
      case ConnectivityResult.mobile:
        // In real implementation, would detect 2G/3G/4G
        _currentConnectivity = ConnectivityLevel.stable2G;
        break;
      case ConnectivityResult.none:
        _currentConnectivity = ConnectivityLevel.offline;
        break;
      default:
        _currentConnectivity = ConnectivityLevel.offline;
    }

    if (previousLevel != _currentConnectivity) {
      _logger.i('📶 Connectivity changed: $previousLevel → $_currentConnectivity');
      
      // Trigger sync attempt on connectivity improvement
      if (_currentConnectivity != ConnectivityLevel.offline && 
          previousLevel == ConnectivityLevel.offline) {
        _attemptSync();
      }
    }
  }

  /// Attempt immediate sync
  Future<void> _attemptSync() async {
    if (_offlineQueue.isNotEmpty) {
      _logger.i('🔄 Attempting immediate sync due to connectivity change');
      await processOfflineQueue();
    }
  }

  /// Sort queue by priority
  void _sortQueueByPriority() {
    final priorityOrder = {
      'critical': 0,
      'high': 1,
      'medium': 2,
      'low': 3,
    };

    _offlineQueue.sort((a, b) {
      final aPriority = priorityOrder[a['priority']] ?? 999;
      final bPriority = priorityOrder[b['priority']] ?? 999;
      return aPriority.compareTo(bPriority);
    });
  }

  /// Load offline queue from secure storage
  Future<void> _loadOfflineQueue() async {
    try {
      final queueData = await _secureStorage.read(key: 'offline_queue');
      if (queueData != null) {
        final decryptedData = await _encryptionService.decryptData(queueData);
        final queueList = jsonDecode(decryptedData) as List;
        _offlineQueue = queueList.cast<Map<String, dynamic>>();
        _logger.i('📥 Loaded ${_offlineQueue.length} items from offline queue');
      }
    } catch (e) {
      _logger.e('❌ Failed to load offline queue: $e');
      _offlineQueue = [];
    }
  }

  /// Save offline queue to secure storage
  Future<void> _saveOfflineQueue() async {
    try {
      final queueJson = jsonEncode(_offlineQueue);
      final encryptedData = await _encryptionService.encryptData(queueJson);
      await _secureStorage.write(key: 'offline_queue', value: encryptedData);
    } catch (e) {
      _logger.e('❌ Failed to save offline queue: $e');
    }
  }

  /// Get current sync statistics
  Map<String, dynamic> getSyncStatistics() {
    final pendingItems = _offlineQueue.where((item) => item['sync_status'] == 'pending').length;
    final completedItems = _offlineQueue.where((item) => item['sync_status'] == 'completed').length;
    final errorItems = _offlineQueue.where((item) => item['sync_status'] == 'error').length;

    return {
      'total_items': _offlineQueue.length,
      'pending_items': pendingItems,
      'completed_items': completedItems,
      'error_items': errorItems,
      'current_connectivity': _currentConnectivity.toString(),
      'current_season': _currentSeason.toString(),
      'last_sync_attempt': DateTime.now().toIso8601String(),
    };
  }

  /// Dispose resources
  void dispose() {
    _syncTimer?.cancel();
  }

  /// Check if sync engine is initialized
  bool get isInitialized => _isInitialized;

  /// Get current connectivity level
  ConnectivityLevel get currentConnectivity => _currentConnectivity;

  /// Get current Zambian season
  ZambianSeason get currentSeason => _currentSeason;

  /// Get offline queue size
  int get queueSize => _offlineQueue.length;
}

/// Main Sync Engine class for configuration
class SyncEngine {
  static ZambiaRetryPolicy? _retryPolicy;
  static ZambiaDataSaver? _dataConservation;
  static ZambiaSMSGateway? _emergencyFallback;

  /// Configure sync engine with Zambian-specific components
  static void configure({
    required ZambiaRetryPolicy retryPolicy,
    required ZambiaDataSaver dataConservation,
    required ZambiaSMSGateway emergencyFallback,
  }) {
    _retryPolicy = retryPolicy;
    _dataConservation = dataConservation;
    _emergencyFallback = emergencyFallback;

    final logger = Logger();
    logger.i('🔧 SyncEngine configured with Zambian components');
  }

  /// Get current retry policy
  static ZambiaRetryPolicy? get retryPolicy => _retryPolicy;

  /// Get current data conservation settings
  static ZambiaDataSaver? get dataConservation => _dataConservation;

  /// Get current emergency fallback gateway
  static ZambiaSMSGateway? get emergencyFallback => _emergencyFallback;

  /// Check if sync engine is configured
  static bool get isConfigured =>
      _retryPolicy != null &&
      _dataConservation != null &&
      _emergencyFallback != null;
}
