import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/config/compliance_config.dart';
import '../../core/security/compliance_service.dart';
import '../../core/constants/app_constants.dart';

/// BoZ Compliance Dashboard
/// 
/// Displays real-time compliance status for:
/// - PCI-DSS Section 3.2.1 requirements
/// - 5-year data retention (Zambia Financial Act 2022)
/// - Bank of Zambia regulatory compliance
class ComplianceDashboard extends StatefulWidget {
  const ComplianceDashboard({super.key});

  @override
  State<ComplianceDashboard> createState() => _ComplianceDashboardState();
}

class _ComplianceDashboardState extends State<ComplianceDashboard> {
  final ComplianceService _complianceService = ComplianceService();
  
  Map<String, dynamic>? _pciComplianceStatus;
  Map<String, dynamic>? _dataRetentionStatus;
  Map<String, dynamic>? _bozQuarterlyReport;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadComplianceData();
  }

  Future<void> _loadComplianceData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load PCI-DSS compliance status
      _pciComplianceStatus = await _complianceService.validatePCIDSSCompliance();
      
      // Load data retention status
      _dataRetentionStatus = await _complianceService._getDataRetentionStatus();
      
      // Generate BoZ quarterly report
      _bozQuarterlyReport = await _complianceService.generateBoZQuarterlyReport();
      
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'BoZ Compliance Dashboard',
          style: GoogleFonts.roboto(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadComplianceData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error: $_error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadComplianceData,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Compliance Overview
                      _buildComplianceOverview(),
                      
                      const SizedBox(height: 24),
                      
                      // PCI-DSS 3.2.1 Status
                      _buildPciDssStatus(),
                      
                      const SizedBox(height: 24),
                      
                      // Data Retention Status
                      _buildDataRetentionStatus(),
                      
                      const SizedBox(height: 24),
                      
                      // BoZ Regulatory Status
                      _buildBozRegulatoryStatus(),
                      
                      const SizedBox(height: 24),
                      
                      // Quick Actions
                      _buildQuickActions(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildComplianceOverview() {
    final pciScore = _pciComplianceStatus?['overall_compliance_score'] ?? 0.0;
    final retentionRate = double.tryParse(
      _dataRetentionStatus?['retention_compliance_rate'] ?? '0.0'
    ) ?? 0.0;
    
    final overallCompliance = (pciScore + retentionRate) / 2;
    final isCompliant = overallCompliance >= 95.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isCompliant ? Icons.verified : Icons.warning,
                  color: isCompliant ? Colors.green : Colors.orange,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'BoZ Compliance Status',
                        style: GoogleFonts.roboto(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        isCompliant ? 'COMPLIANT' : 'ATTENTION REQUIRED',
                        style: GoogleFonts.roboto(
                          color: isCompliant ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${overallCompliance.toStringAsFixed(1)}%',
                  style: GoogleFonts.roboto(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isCompliant ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Compliance breakdown
            Row(
              children: [
                Expanded(
                  child: _buildComplianceMetric(
                    'PCI-DSS 3.2.1',
                    pciScore,
                    Icons.security,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildComplianceMetric(
                    '5-Year Retention',
                    retentionRate,
                    Icons.storage,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Regulatory framework
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.gavel, color: Colors.blue),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      ComplianceConfig.regulatoryFramework,
                      style: GoogleFonts.roboto(
                        fontSize: 12,
                        color: Colors.blue[800],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComplianceMetric(String title, double score, IconData icon) {
    final isGood = score >= 95.0;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isGood ? Colors.green[50] : Colors.orange[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isGood ? Colors.green[200]! : Colors.orange[200]!,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: isGood ? Colors.green : Colors.orange,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.roboto(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            '${score.toStringAsFixed(1)}%',
            style: GoogleFonts.roboto(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isGood ? Colors.green : Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPciDssStatus() {
    final issues = _pciComplianceStatus?['encryption_compliance']?['issues'] as List? ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: Colors.blue),
                const SizedBox(width: 12),
                Text(
                  'PCI-DSS Section 3.2.1 Compliance',
                  style: GoogleFonts.roboto(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Do not store sensitive authentication data after authorization',
              style: GoogleFonts.roboto(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            
            const SizedBox(height: 16),
            
            if (issues.isEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 8),
                    const Text('All PCI-DSS 3.2.1 requirements met'),
                  ],
                ),
              )
            else
              ...issues.map((issue) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.red),
                    const SizedBox(width: 8),
                    Expanded(child: Text(issue.toString())),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRetentionStatus() {
    final totalRecords = _dataRetentionStatus?['total_records_tracked'] ?? 0;
    final expiredRecords = _dataRetentionStatus?['expired_records'] ?? 0;
    final deletedRecords = _dataRetentionStatus?['securely_deleted_records'] ?? 0;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.storage, color: Colors.purple),
                const SizedBox(width: 12),
                Text(
                  '5-Year Data Retention',
                  style: GoogleFonts.roboto(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Zambia Financial Act 2022 Section 45',
              style: GoogleFonts.roboto(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildRetentionMetric(
                    'Total Records',
                    totalRecords.toString(),
                    Icons.folder,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildRetentionMetric(
                    'Expired',
                    expiredRecords.toString(),
                    Icons.schedule,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildRetentionMetric(
                    'Deleted',
                    deletedRecords.toString(),
                    Icons.delete_forever,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRetentionMetric(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.roboto(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.roboto(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBozRegulatoryStatus() {
    final recommendations = _bozQuarterlyReport?['recommendations'] as List? ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.account_balance, color: Colors.green),
                const SizedBox(width: 12),
                Text(
                  'Bank of Zambia Status',
                  style: GoogleFonts.roboto(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (recommendations.isEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green),
                    SizedBox(width: 8),
                    Text('No regulatory actions required'),
                  ],
                ),
              )
            else
              ...recommendations.take(3).map((rec) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.priority_high,
                          color: rec['priority'] == 'CRITICAL' ? Colors.red : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          rec['priority'] ?? 'MEDIUM',
                          style: GoogleFonts.roboto(
                            fontWeight: FontWeight.bold,
                            color: rec['priority'] == 'CRITICAL' ? Colors.red : Colors.orange,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(rec['description'] ?? ''),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: GoogleFonts.roboto(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _exportBozReport(),
                    icon: const Icon(Icons.file_download),
                    label: const Text('Export BoZ Report'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _performDataCleanup(),
                    icon: const Icon(Icons.cleaning_services),
                    label: const Text('Data Cleanup'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _exportBozReport() async {
    try {
      final report = await _complianceService.exportComplianceDataForBoZ(
        startDate: DateTime.now().subtract(const Duration(days: 90)),
        endDate: DateTime.now(),
      );
      
      // In a real app, this would save the file or share it
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('BoZ report exported successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Export failed: $e')),
      );
    }
  }

  Future<void> _performDataCleanup() async {
    try {
      await _complianceService.performSecureDataDeletion();
      await _loadComplianceData(); // Refresh data
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Data cleanup completed')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Cleanup failed: $e')),
      );
    }
  }
}
