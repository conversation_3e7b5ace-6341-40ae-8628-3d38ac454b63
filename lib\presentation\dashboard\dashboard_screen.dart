import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/config/app_config.dart';
import '../../core/constants/app_constants.dart';
import '../../core/accessibility/voice_player.dart';
import '../../features/feature_lock.dart';
import '../widgets/payment_button.dart';

/// Main dashboard screen showing financial services and utilities
/// MOBILE MONEY MVP: Banking features disabled, mobile money core enabled
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> with FeatureAware {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // Play welcome message when dashboard loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      VoicePlayer.playWelcome();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppConfig.appName,
          style: GoogleFonts.roboto(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2E7D32),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.account_circle_outlined),
            onPressed: () {
              // TODO: Navigate to profile
            },
          ),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: const [
          _HomeTab(),
          _TransactionsTab(),
          _UtilitiesTab(),
          _MoreTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: const Color(0xFF2E7D32),
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.swap_horiz_outlined),
            activeIcon: Icon(Icons.swap_horiz),
            label: 'Transactions',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.electrical_services_outlined),
            activeIcon: Icon(Icons.electrical_services),
            label: 'Utilities',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.more_horiz_outlined),
            activeIcon: Icon(Icons.more_horiz),
            label: 'More',
          ),
        ],
      ),
    );
  }
}

class _HomeTab extends StatefulWidget {
  const _HomeTab();

  @override
  State<_HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<_HomeTab> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Balance Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
              ),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Total Balance',
                  style: GoogleFonts.roboto(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'K 2,450.00',
                  style: GoogleFonts.roboto(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Icon(
                      Icons.offline_bolt,
                      color: Colors.white.withOpacity(0.8),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Offline Mode Available',
                      style: GoogleFonts.roboto(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Quick Actions with Voice Guidance
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Quick Actions',
                style: GoogleFonts.roboto(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: Icon(
                  VoicePlayer.isVoiceEnabled ? Icons.volume_up : Icons.volume_off,
                  color: Colors.grey[600],
                ),
                onPressed: () {
                  VoicePlayer.setVoiceEnabled(!VoicePlayer.isVoiceEnabled);
                  setState(() {});
                },
                tooltip: 'Toggle voice guidance',
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Icon-first payment buttons with voice guidance
          PaymentButtonGrid(
            buttons: [
              SendMoneyButton(
                onPressed: () {
                  // Navigate to send money screen
                  _showComingSoon(context, 'Send Money');
                },
              ),
              ElectricityPaymentButton(
                onPressed: () {
                  // Navigate to ZESCO payment
                  _showZESCOPayment(context);
                },
              ),
              WaterPaymentButton(
                onPressed: () {
                  // Voice-guided UI (Bemba/Nyanja)
                  VoicePlayer.play("bemba", "tapili_water.wav");
                  _showWaterPayment(context);
                },
              ),
              AirtimeButton(
                onPressed: () {
                  _showComingSoon(context, 'Buy Airtime');
                },
              ),
            ],
            crossAxisCount: 2,
            spacing: 16,
            childAspectRatio: 1.1,
          ),
          
          const SizedBox(height: 24),
          
          // Recent Transactions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Transactions',
                style: GoogleFonts.roboto(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {},
                child: const Text('View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Transaction List
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              return _TransactionItem(
                title: index == 0 ? 'Sent to John Mwanza' : 
                       index == 1 ? 'ZESCO Bill Payment' : 'Airtime Purchase',
                amount: index == 0 ? '-K 150.00' : 
                        index == 1 ? '-K 280.50' : '-K 25.00',
                date: 'Today',
                icon: index == 0 ? Icons.send : 
                      index == 1 ? Icons.electrical_services : Icons.phone_android,
                isDebit: true,
              );
            },
          ),
        ],
      ),
    );
  }

  /// Show ZESCO payment dialog
  void _showZESCOPayment(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),

              // Title with voice
              Row(
                children: [
                  const Icon(Icons.electrical_services, color: Colors.orange),
                  const SizedBox(width: 12),
                  Text(
                    'ZESCO Electricity Payment',
                    style: GoogleFonts.roboto(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.volume_up),
                    onPressed: () => VoicePlayer.playElectricityBill(),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Payment form would go here
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.electrical_services,
                        size: 64,
                        color: Colors.orange,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'ZESCO Payment',
                        style: GoogleFonts.roboto(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Full payment interface coming soon',
                        style: GoogleFonts.roboto(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show water payment dialog with voice guidance
  void _showWaterPayment(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),

              // Title with voice
              Row(
                children: [
                  const Icon(Icons.water_drop, color: Colors.blue),
                  const SizedBox(width: 12),
                  Text(
                    'Water Bill Payment',
                    style: GoogleFonts.roboto(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.volume_up),
                    onPressed: () => VoicePlayer.playWaterBill(),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Water provider buttons
              Expanded(
                child: Column(
                  children: [
                    // NWSC Button (as in your example)
                    PaymentButton(
                      icon: Icons.water_drop,
                      label: "NWSC",
                      subtitle: "National Water & Sanitation",
                      iconColor: Colors.blue,
                      voicePrompt: 'water_bill',
                      onPressed: () {
                        Navigator.pop(context);
                        _showComingSoon(context, 'NWSC Water Payment');
                      },
                    ),

                    const SizedBox(height: 16),

                    // LWSC Button
                    PaymentButton(
                      icon: Icons.water_drop,
                      label: "LWSC",
                      subtitle: "Lusaka Water & Sewerage",
                      iconColor: Colors.lightBlue,
                      voicePrompt: 'water_bill',
                      onPressed: () {
                        Navigator.pop(context);
                        _showComingSoon(context, 'LWSC Water Payment');
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show coming soon dialog
  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.construction, color: Colors.orange, size: 48),
        title: Text(
          'Coming Soon',
          style: GoogleFonts.roboto(fontWeight: FontWeight.bold),
        ),
        content: Text('$feature feature is under development.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}



class _TransactionItem extends StatelessWidget {
  final String title;
  final String amount;
  final String date;
  final IconData icon;
  final bool isDebit;

  const _TransactionItem({
    required this.title,
    required this.amount,
    required this.date,
    required this.icon,
    required this.isDebit,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7D32).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF2E7D32),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                Text(
                  date,
                  style: GoogleFonts.roboto(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: GoogleFonts.roboto(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: isDebit ? Colors.red[600] : Colors.green[600],
            ),
          ),
        ],
      ),
    );
  }
}

class _TransactionsTab extends StatelessWidget {
  const _TransactionsTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Transactions Tab - Coming Soon'),
    );
  }
}

class _UtilitiesTab extends StatelessWidget {
  const _UtilitiesTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Utilities Tab - Coming Soon'),
    );
  }
}

class _MoreTab extends StatelessWidget {
  const _MoreTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('More Tab - Coming Soon'),
    );
  }
}
