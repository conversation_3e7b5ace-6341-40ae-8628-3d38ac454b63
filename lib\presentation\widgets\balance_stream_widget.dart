import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/constants/app_constants.dart';
import '../../features/mobile_money/data/services/momo_streaming_service.dart';

/// StreamBuilder widget for real-time balance updates
/// Displays live balance data with proper loading states and error handling
class BalanceStreamWidget extends StatefulWidget {
  final String? provider;
  final TextStyle? textStyle;
  final Color? loadingColor;
  final Widget? errorWidget;
  final bool showCurrency;
  final VoidCallback? onTap;

  const BalanceStreamWidget({
    super.key,
    this.provider,
    this.textStyle,
    this.loadingColor,
    this.errorWidget,
    this.showCurrency = true,
    this.onTap,
  });

  @override
  State<BalanceStreamWidget> createState() => _BalanceStreamWidgetState();
}

class _BalanceStreamWidgetState extends State<BalanceStreamWidget> {
  final MomoStreamingService _streamingService = MomoStreamingService();

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      await _streamingService.initialize();
    } catch (e) {
      debugPrint('Failed to initialize streaming service: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Map<String, double>>(
      stream: _streamingService.realtimeBalances,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return widget.errorWidget ?? _buildErrorWidget(context);
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingWidget(context);
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return _buildNoDataWidget(context);
        }

        final balances = snapshot.data!;
        
        if (widget.provider != null) {
          // Show specific provider balance
          final balance = balances[widget.provider] ?? 0.0;
          return _buildBalanceText(context, balance);
        } else {
          // Show all provider balances
          return _buildAllBalances(context, balances);
        }
      },
    );
  }

  Widget _buildBalanceText(BuildContext context, double balance) {
    final formattedBalance = _formatBalance(balance);
    
    return GestureDetector(
      onTap: widget.onTap,
      child: Text(
        formattedBalance,
        style: widget.textStyle ?? GoogleFonts.inter(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildAllBalances(BuildContext context, Map<String, double> balances) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (final entry in balances.entries)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getProviderDisplayName(entry.key),
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
                Text(
                  _formatBalance(entry.value),
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              widget.loadingColor ?? Theme.of(context).primaryColor,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'Loading...',
          style: widget.textStyle ?? GoogleFonts.inter(
            fontSize: 14,
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.error_outline,
          size: 16,
          color: Theme.of(context).colorScheme.error,
        ),
        const SizedBox(width: 8),
        Text(
          'Error loading balance',
          style: GoogleFonts.inter(
            fontSize: 14,
            color: Theme.of(context).colorScheme.error,
          ),
        ),
      ],
    );
  }

  Widget _buildNoDataWidget(BuildContext context) {
    return Text(
      widget.showCurrency ? 'K 0.00' : '0.00',
      style: widget.textStyle ?? GoogleFonts.inter(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.6),
      ),
    );
  }

  String _formatBalance(double balance) {
    final formatted = balance.toStringAsFixed(2);
    return widget.showCurrency ? '${AppConstants.currencySymbol} $formatted' : formatted;
  }

  String _getProviderDisplayName(String provider) {
    switch (provider) {
      case AppConstants.providerMTN:
        return 'MTN Mobile Money';
      case AppConstants.providerAirtel:
        return 'Airtel Money';
      case AppConstants.providerZamtel:
        return 'Zamtel Kwacha';
      default:
        return provider;
    }
  }
}

/// Specialized widget for transaction stream display
class TransactionStreamWidget extends StatefulWidget {
  final String? userId;
  final int limit;
  final Widget Function(BuildContext, List<dynamic>)? itemBuilder;

  const TransactionStreamWidget({
    super.key,
    this.userId,
    this.limit = 10,
    this.itemBuilder,
  });

  @override
  State<TransactionStreamWidget> createState() => _TransactionStreamWidgetState();
}

class _TransactionStreamWidgetState extends State<TransactionStreamWidget> {
  final MomoStreamingService _streamingService = MomoStreamingService();

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      await _streamingService.initialize();
    } catch (e) {
      debugPrint('Failed to initialize streaming service: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: _streamingService.realtimeTransactions,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return _buildErrorWidget(context);
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingWidget(context);
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return _buildNoDataWidget(context);
        }

        final transactions = snapshot.data!.take(widget.limit).toList();
        
        if (widget.itemBuilder != null) {
          return widget.itemBuilder!(context, transactions);
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: transactions.length,
          itemBuilder: (context, index) {
            final transaction = transactions[index];
            return _buildTransactionItem(context, transaction);
          },
        );
      },
    );
  }

  Widget _buildTransactionItem(BuildContext context, dynamic transaction) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
          child: Icon(
            _getTransactionIcon(transaction.transactionType),
            color: Theme.of(context).primaryColor,
          ),
        ),
        title: Text(
          transaction.description ?? 'Transaction',
          style: GoogleFonts.inter(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          '${transaction.provider} • ${_formatDate(transaction.createdAt)}',
          style: GoogleFonts.inter(fontSize: 12),
        ),
        trailing: Text(
          '${AppConstants.currencySymbol} ${transaction.amount.toStringAsFixed(2)}',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.bold,
            color: transaction.transactionType == AppConstants.transactionTypeSend
                ? Theme.of(context).colorScheme.error
                : Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 8),
            Text(
              'Error loading transactions',
              style: GoogleFonts.inter(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataWidget(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'No transactions found',
          style: GoogleFonts.inter(
            color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.6),
          ),
        ),
      ),
    );
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case AppConstants.transactionTypeSend:
        return Icons.send;
      case AppConstants.transactionTypeReceive:
        return Icons.call_received;
      default:
        return Icons.swap_horiz;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else {
      return '${difference.inDays} days ago';
    }
  }
}
