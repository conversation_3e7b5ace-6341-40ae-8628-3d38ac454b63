import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/accessibility/voice_player.dart';
import '../../core/constants/app_constants.dart';

/// Icon-first payment button for rural accessibility
/// Large icons, clear labels, voice guidance, and haptic feedback
class PaymentButton extends StatefulWidget {
  final IconData icon;
  final String label;
  final String? subtitle;
  final VoidCallback? onPressed;
  final Color? iconColor;
  final Color? backgroundColor;
  final String? voicePrompt;
  final bool isEnabled;
  final bool showBadge;
  final String? badgeText;
  final double? iconSize;
  final EdgeInsets? padding;

  const PaymentButton({
    super.key,
    required this.icon,
    required this.label,
    this.subtitle,
    this.onPressed,
    this.iconColor,
    this.backgroundColor,
    this.voicePrompt,
    this.isEnabled = true,
    this.showBadge = false,
    this.badgeText,
    this.iconSize,
    this.padding,
  });

  @override
  State<PaymentButton> createState() => _PaymentButtonState();
}

class _PaymentButtonState extends State<PaymentButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.isEnabled) return;
    
    setState(() => _isPressed = true);
    _animationController.forward();
    
    // Haptic feedback for better accessibility
    HapticFeedback.lightImpact();
    
    // Play voice guidance if available
    if (widget.voicePrompt != null) {
      VoicePlayer.playPrompt(widget.voicePrompt!);
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _handleTapEnd();
  }

  void _handleTapCancel() {
    _handleTapEnd();
  }

  void _handleTapEnd() {
    if (!mounted) return;
    
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _handleTap() {
    if (!widget.isEnabled || widget.onPressed == null) return;
    
    // Strong haptic feedback on selection
    HapticFeedback.mediumImpact();
    
    widget.onPressed!();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final iconColor = widget.iconColor ?? theme.primaryColor;
    final backgroundColor = widget.backgroundColor ?? Colors.white;
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            onTap: _handleTap,
            child: Container(
              padding: widget.padding ?? const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: widget.isEnabled ? backgroundColor : Colors.grey[100],
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(
                  color: _isPressed 
                      ? iconColor.withOpacity(0.5)
                      : Colors.grey[300]!,
                  width: _isPressed ? 2 : 1,
                ),
                boxShadow: widget.isEnabled ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: _isPressed ? 2 : 4,
                    offset: Offset(0, _isPressed ? 1 : 2),
                  ),
                ] : null,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon with badge
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      Icon(
                        widget.icon,
                        size: widget.iconSize ?? 48,
                        color: widget.isEnabled 
                            ? iconColor 
                            : Colors.grey[400],
                      ),
                      
                      // Badge for notifications or status
                      if (widget.showBadge && widget.badgeText != null)
                        Positioned(
                          right: -8,
                          top: -8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              widget.badgeText!,
                              style: GoogleFonts.roboto(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Main label
                  Text(
                    widget.label,
                    style: GoogleFonts.roboto(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: widget.isEnabled 
                          ? Colors.black87 
                          : Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  // Subtitle
                  if (widget.subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      widget.subtitle!,
                      style: GoogleFonts.roboto(
                        fontSize: 12,
                        color: widget.isEnabled 
                            ? Colors.grey[600] 
                            : Colors.grey[400],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Specialized payment buttons for common services
class WaterPaymentButton extends PaymentButton {
  const WaterPaymentButton({
    super.key,
    super.onPressed,
    super.isEnabled = true,
    String? provider,
  }) : super(
          icon: Icons.water_drop,
          label: provider ?? "NWSC",
          subtitle: "Water Bill",
          iconColor: Colors.blue,
          voicePrompt: 'water_bill',
        );
}

class ElectricityPaymentButton extends PaymentButton {
  const ElectricityPaymentButton({
    super.key,
    super.onPressed,
    super.isEnabled = true,
  }) : super(
          icon: Icons.electrical_services,
          label: "ZESCO",
          subtitle: "Electricity Bill",
          iconColor: Colors.orange,
          voicePrompt: 'electricity_bill',
        );
}

class SendMoneyButton extends PaymentButton {
  const SendMoneyButton({
    super.key,
    super.onPressed,
    super.isEnabled = true,
  }) : super(
          icon: Icons.send,
          label: "Send Money",
          subtitle: "MTN, Airtel, Zamtel",
          iconColor: Colors.green,
          voicePrompt: 'send_money',
        );
}

class AirtimeButton extends PaymentButton {
  const AirtimeButton({
    super.key,
    super.onPressed,
    super.isEnabled = true,
  }) : super(
          icon: Icons.phone_android,
          label: "Buy Airtime",
          subtitle: "All Networks",
          iconColor: Colors.purple,
          voicePrompt: 'buy_airtime',
        );
}

class QRScanButton extends PaymentButton {
  const QRScanButton({
    super.key,
    super.onPressed,
    super.isEnabled = true,
  }) : super(
          icon: Icons.qr_code_scanner,
          label: "Scan QR",
          subtitle: "Quick Payment",
          iconColor: Colors.indigo,
          voicePrompt: 'scan_qr',
        );
}

/// Grid layout for payment buttons optimized for rural use
class PaymentButtonGrid extends StatelessWidget {
  final List<PaymentButton> buttons;
  final int crossAxisCount;
  final double spacing;
  final double childAspectRatio;

  const PaymentButtonGrid({
    super.key,
    required this.buttons,
    this.crossAxisCount = 2,
    this.spacing = 16,
    this.childAspectRatio = 1.1,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: buttons.length,
      itemBuilder: (context, index) => buttons[index],
    );
  }
}

/// Accessibility-enhanced payment button with voice and visual feedback
class AccessiblePaymentButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final String? subtitle;
  final VoidCallback? onPressed;
  final Color? iconColor;
  final Color? backgroundColor;
  final String? voicePrompt;
  final bool isEnabled;
  final bool showBadge;
  final String? badgeText;

  const AccessiblePaymentButton({
    super.key,
    required this.icon,
    required this.label,
    this.subtitle,
    this.onPressed,
    this.iconColor,
    this.backgroundColor,
    this.voicePrompt,
    this.isEnabled = true,
    this.showBadge = false,
    this.badgeText,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: true,
      enabled: isEnabled,
      label: '$label${subtitle != null ? ' - $subtitle' : ''}',
      hint: 'Double tap to activate',
      onTap: onPressed,
      child: PaymentButton(
        icon: icon,
        label: label,
        subtitle: subtitle,
        onPressed: onPressed,
        iconColor: iconColor,
        backgroundColor: backgroundColor,
        voicePrompt: voicePrompt,
        isEnabled: isEnabled,
        showBadge: showBadge,
        badgeText: badgeText,
        iconSize: 56, // Larger icons for better visibility
        padding: const EdgeInsets.all(24), // More padding for easier tapping
      ),
    );
  }
}
