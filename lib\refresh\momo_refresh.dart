/// Mobile Money Refresh System for Pay Mule Zambia MVP
/// Optimized for Zambian network conditions with 2G-friendly refresh patterns
/// Integrates with feature lock system and wallet-only flow
/// 
/// CORE MANDATE: Mobile money-only release • No banking features • Zero breakage

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../core/refresh/smart_refresh_controller.dart';
import '../core/refresh/connection_aware_refresher.dart';
import '../core/data_usage/data_usage_monitor.dart';
import '../features/feature_lock.dart';
import '../features/mobile_money/data/services/mobile_money_service.dart';
import '../wallet/zambia_wallets.dart';

/// Refresh triggers for mobile money operations
enum RefreshTrigger {
  manual,        // User-initiated refresh
  automatic,     // Scheduled refresh
  connectivity,  // Network connectivity restored
  transaction,   // After transaction completion
  startup,       // App startup
}

/// Refresh actions for mobile money data
enum RefreshAction {
  updateBalances,      // Refresh wallet balances
  syncTransactions,    // Sync transaction history
  updateRates,         // Update exchange rates
  syncAgents,          // Update agent locations
  updateUtilities,     // Refresh utility bill status
  fullSync,            // Complete data synchronization
}

/// Mobile Money Refresh Controller
/// Optimized for Zambian mobile networks and data constraints
class MomoRefreshController extends ChangeNotifier {
  static final MomoRefreshController _instance = MomoRefreshController._internal();
  factory MomoRefreshController() => _instance;
  MomoRefreshController._internal();

  final Logger _logger = Logger();
  final SmartRefreshController _smartRefresh = SmartRefreshController();
  final ConnectionAwareRefresher _connectionRefresh = ConnectionAwareRefresher();
  final DataUsageMonitor _dataMonitor = DataUsageMonitor();
  final MobileMoneyService _momoService = MobileMoneyService();

  // Refresh state
  bool _isRefreshing = false;
  DateTime? _lastRefreshTime;
  final Map<RefreshAction, DateTime> _lastActionTimes = {};
  final List<RefreshListener> _listeners = [];

  // Configuration
  static const Duration _minRefreshInterval = Duration(minutes: 2);
  static const Duration _balanceRefreshInterval = Duration(minutes: 5);
  static const Duration _transactionRefreshInterval = Duration(minutes: 10);

  /// Initialize mobile money refresh system
  Future<void> initialize() async {
    _logger.i('🔄 Initializing Mobile Money Refresh System');
    
    // Ensure feature lock system is initialized
    if (!Features.isMobileMoneyEnabled()) {
      await Features.initialize();
    }

    // Initialize underlying refresh controllers
    await _smartRefresh.initialize();
    await _connectionRefresh.initialize();
    await _dataMonitor.initialize();

    _logger.i('✅ Mobile Money Refresh System initialized');
  }

  /// Enable balance refresh with optimized settings
  void enableBalanceRefresh() {
    _logger.i('📱 Enabling balance refresh for mobile money wallets');

    addRefreshListener(RefreshListener(
      trigger: RefreshTrigger.manual,
      actions: [
        RefreshAction.updateBalances,
        RefreshAction.syncTransactions,
      ],
      constraints: null, // Will use default 2G optimization
      priority: RefreshPriority.high,
    ));

    // Add automatic balance refresh for supported wallets
    for (final wallet in ZambiaWallets.supportedWallets) {
      _scheduleWalletRefresh(wallet);
    }

    _logger.i('✅ Balance refresh enabled for all supported wallets');
  }

  /// Add refresh listener
  void addRefreshListener(RefreshListener listener) {
    _listeners.add(listener);
    _logger.d('Added refresh listener: ${listener.trigger}');
  }

  /// Remove refresh listener
  void removeRefreshListener(RefreshListener listener) {
    _listeners.remove(listener);
    _logger.d('Removed refresh listener: ${listener.trigger}');
  }

  /// Trigger manual refresh
  Future<void> triggerRefresh({
    List<RefreshAction>? actions,
    bool forceRefresh = false,
  }) async {
    if (_isRefreshing && !forceRefresh) {
      _logger.w('Refresh already in progress, skipping');
      return;
    }

    _logger.i('🔄 Triggering manual refresh');
    
    await _performRefresh(
      trigger: RefreshTrigger.manual,
      actions: actions ?? [RefreshAction.updateBalances, RefreshAction.syncTransactions],
      forceRefresh: forceRefresh,
    );
  }

  /// Perform refresh operation
  Future<void> _performRefresh({
    required RefreshTrigger trigger,
    required List<RefreshAction> actions,
    bool forceRefresh = false,
  }) async {
    if (!_canRefresh(trigger, forceRefresh)) {
      _logger.w('Refresh blocked: ${_getRefreshBlockReason()}');
      return;
    }

    _setRefreshing(true);

    try {
      _logger.i('🔄 Performing refresh: ${actions.join(', ')}');

      // Execute actions based on priority and network conditions
      for (final action in actions) {
        await _executeRefreshAction(action, trigger);
      }

      _lastRefreshTime = DateTime.now();
      _logger.i('✅ Refresh completed successfully');

    } catch (e) {
      _logger.e('❌ Refresh failed: $e');
    } finally {
      _setRefreshing(false);
    }
  }

  /// Execute individual refresh action
  Future<void> _executeRefreshAction(RefreshAction action, RefreshTrigger trigger) async {
    // Check if action was recently performed
    if (!_shouldPerformAction(action)) {
      _logger.d('Skipping action $action - recently performed');
      return;
    }

    _logger.d('Executing refresh action: $action');

    try {
      switch (action) {
        case RefreshAction.updateBalances:
          await _refreshBalances();
          break;
        case RefreshAction.syncTransactions:
          await _syncTransactions();
          break;
        case RefreshAction.updateRates:
          await _updateExchangeRates();
          break;
        case RefreshAction.syncAgents:
          await _syncAgentLocations();
          break;
        case RefreshAction.updateUtilities:
          await _updateUtilityStatus();
          break;
        case RefreshAction.fullSync:
          await _performFullSync();
          break;
      }

      _lastActionTimes[action] = DateTime.now();
      _logger.d('✅ Action $action completed');

    } catch (e) {
      _logger.e('❌ Action $action failed: $e');
    }
  }

  /// Refresh wallet balances
  Future<void> _refreshBalances() async {
    await _connectionRefresh.performRefresh(
      refreshFunction: () async {
        final balances = await _momoService.getAllBalances();
        _logger.i('Updated balances for ${balances.length} wallets');
        return balances;
      },
      fallbackFunction: () async {
        _logger.i('Using cached balances');
        return <String, double>{};
      },
      operationName: 'balance_refresh',
      isEssential: true,
    );
  }

  /// Sync transaction history
  Future<void> _syncTransactions() async {
    await _connectionRefresh.performRefresh(
      refreshFunction: () async {
        // Sync transactions for all supported wallets
        for (final wallet in ZambiaWallets.supportedWallets) {
          await _syncWalletTransactions(wallet);
        }
        _logger.i('Synced transactions for all wallets');
      },
      fallbackFunction: () async {
        _logger.i('Using cached transactions');
      },
      operationName: 'transaction_sync',
      isEssential: false,
    );
  }

  /// Sync transactions for specific wallet
  Future<void> _syncWalletTransactions(MobileWallet wallet) async {
    try {
      // Implementation would sync transactions for specific wallet
      _logger.d('Syncing transactions for ${wallet.displayName}');
    } catch (e) {
      _logger.e('Failed to sync transactions for ${wallet.displayName}: $e');
    }
  }

  /// Update exchange rates
  Future<void> _updateExchangeRates() async {
    await _connectionRefresh.performRefresh(
      refreshFunction: () async {
        _logger.i('Updated exchange rates');
      },
      fallbackFunction: () async {
        _logger.i('Using cached exchange rates');
      },
      operationName: 'rate_update',
      isEssential: false,
    );
  }

  /// Sync agent locations
  Future<void> _syncAgentLocations() async {
    await _connectionRefresh.performRefresh(
      refreshFunction: () async {
        _logger.i('Synced agent locations');
      },
      fallbackFunction: () async {
        _logger.i('Using cached agent locations');
      },
      operationName: 'agent_sync',
      isEssential: false,
    );
  }

  /// Update utility bill status
  Future<void> _updateUtilityStatus() async {
    await _connectionRefresh.performRefresh(
      refreshFunction: () async {
        _logger.i('Updated utility bill status');
      },
      fallbackFunction: () async {
        _logger.i('Using cached utility status');
      },
      operationName: 'utility_update',
      isEssential: false,
    );
  }

  /// Perform full synchronization
  Future<void> _performFullSync() async {
    _logger.i('🔄 Performing full synchronization');
    
    await _executeRefreshAction(RefreshAction.updateBalances, RefreshTrigger.manual);
    await _executeRefreshAction(RefreshAction.syncTransactions, RefreshTrigger.manual);
    await _executeRefreshAction(RefreshAction.updateRates, RefreshTrigger.manual);
    await _executeRefreshAction(RefreshAction.syncAgents, RefreshTrigger.manual);
    await _executeRefreshAction(RefreshAction.updateUtilities, RefreshTrigger.manual);
    
    _logger.i('✅ Full synchronization completed');
  }

  /// Schedule wallet-specific refresh
  void _scheduleWalletRefresh(MobileWallet wallet) {
    Timer.periodic(_balanceRefreshInterval, (timer) {
      if (Features.isEnabled(Features.MOBILE_MONEY)) {
        _refreshWalletBalance(wallet);
      }
    });
  }

  /// Refresh specific wallet balance
  Future<void> _refreshWalletBalance(MobileWallet wallet) async {
    try {
      _logger.d('Refreshing balance for ${wallet.displayName}');
      // Implementation would refresh specific wallet balance
    } catch (e) {
      _logger.e('Failed to refresh ${wallet.displayName} balance: $e');
    }
  }

  /// Check if refresh can be performed
  bool _canRefresh(RefreshTrigger trigger, bool forceRefresh) {
    if (forceRefresh) return true;

    // Check if mobile money features are enabled
    if (!Features.isEnabled(Features.MOBILE_MONEY)) {
      return false;
    }

    // Check minimum refresh interval
    if (_lastRefreshTime != null) {
      final timeSinceRefresh = DateTime.now().difference(_lastRefreshTime!);
      if (timeSinceRefresh < _minRefreshInterval) {
        return false;
      }
    }

    // Check data usage constraints
    if (_dataMonitor.isDataSaverMode && trigger != RefreshTrigger.manual) {
      return false;
    }

    return true;
  }

  /// Check if specific action should be performed
  bool _shouldPerformAction(RefreshAction action) {
    final lastTime = _lastActionTimes[action];
    if (lastTime == null) return true;

    final interval = _getActionInterval(action);
    final timeSinceAction = DateTime.now().difference(lastTime);
    
    return timeSinceAction >= interval;
  }

  /// Get refresh interval for specific action
  Duration _getActionInterval(RefreshAction action) {
    switch (action) {
      case RefreshAction.updateBalances:
        return _balanceRefreshInterval;
      case RefreshAction.syncTransactions:
        return _transactionRefreshInterval;
      case RefreshAction.updateRates:
        return const Duration(hours: 1);
      case RefreshAction.syncAgents:
        return const Duration(hours: 6);
      case RefreshAction.updateUtilities:
        return const Duration(hours: 2);
      case RefreshAction.fullSync:
        return const Duration(hours: 12);
    }
  }

  /// Get reason why refresh is blocked
  String _getRefreshBlockReason() {
    if (!Features.isEnabled(Features.MOBILE_MONEY)) {
      return 'Mobile money features disabled';
    }

    if (_lastRefreshTime != null) {
      final timeSinceRefresh = DateTime.now().difference(_lastRefreshTime!);
      if (timeSinceRefresh < _minRefreshInterval) {
        return 'Minimum refresh interval not met';
      }
    }

    if (_dataMonitor.isDataSaverMode) {
      return 'Data saver mode active';
    }

    return 'Unknown reason';
  }

  /// Set refreshing state
  void _setRefreshing(bool refreshing) {
    if (_isRefreshing != refreshing) {
      _isRefreshing = refreshing;
      notifyListeners();
    }
  }

  // Getters
  bool get isRefreshing => _isRefreshing;
  DateTime? get lastRefreshTime => _lastRefreshTime;
  bool get canRefresh => _canRefresh(RefreshTrigger.manual, false);
}

/// Refresh listener configuration
class RefreshListener {
  final RefreshTrigger trigger;
  final List<RefreshAction> actions;
  final Map<String, dynamic>? constraints; // Generic constraints map
  final RefreshPriority priority;

  const RefreshListener({
    required this.trigger,
    required this.actions,
    this.constraints,
    this.priority = RefreshPriority.normal,
  });
}

/// Refresh priority levels
enum RefreshPriority {
  low,
  normal,
  high,
  critical,
}

/// Global function to enable balance refresh (as per your specification)
void enableBalanceRefresh() {
  final controller = MomoRefreshController();
  controller.enableBalanceRefresh();
}
