/// Agent Service for Zambian Financial Agents
/// Manages agent network, locations, and cash-in/cash-out services
/// Implements offline-first architecture with real-time sync
/// Integrates with Bank of Zambia approved agent registry

import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:convert';
import 'dart:async';
import '../core/production_lock.dart';
import '../core/config/production_config.dart';
import '../core/config/app_config.dart';
import '../data/database/database_helper.dart';
import '../features/geolocation/location_service.dart';
import '../features/financial_inclusion/zambia_financial_registry.dart';
import 'zambia_central_registry.dart';

/// Production mode flag - controlled by ProductionLock system
bool get kProductionMode => ProductionLock().isProductionMode;

/// Zambian provinces for agent filtering
enum Province {
  central,
  copperbelt,
  eastern,
  luapula,
  lusaka,
  muchinga,
  northern,
  northwestern,
  southern,
  western
}

class AgentService {
  static final AgentService _instance = AgentService._internal();
  factory AgentService() => _instance;
  AgentService._internal();

  static final Logger _logger = Logger();
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final LocationService _locationService = LocationService();
  final Connectivity _connectivity = Connectivity();

  // Agent cache and sync management
  List<ZambianAgent> _cachedAgents = [];
  DateTime? _lastSyncTime;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  Timer? _syncTimer;

  // Agent search and filtering
  static const double _defaultSearchRadius = 5.0; // 5km default radius
  static const int _maxAgentsPerQuery = 50;

  /// 🇿🇲 LOAD PRODUCTION AGENTS
  /// Loads agents from Zambia Central Registry with province and rating filtering
  void loadProductionAgents() {
    _logger.i('🏪 Loading production agents from Zambia Central Registry');

    final agents = ZambiaCentralRegistry.getAgents(
      provinces: [Province.eastern, Province.lusaka],
      minRating: 4.0
    );
    _cacheAgentData(agents);

    _logger.i('✅ Production agents loaded successfully');
  }

  /// Cache agent data for offline access
  void _cacheAgentData(List<PayMuleAgent> agents) {
    _logger.i('💾 Caching ${agents.length} agents for offline access');

    try {
      // Convert PayMuleAgent to ZambianAgent for compatibility
      final zambianAgents = agents.map((agent) => _convertToZambianAgent(agent)).toList();

      // Update in-memory cache
      _cachedAgents = zambianAgents;

      // Save to database
      _cacheAgentLocations(zambianAgents);

      _logger.i('✅ Agent data cached successfully');

    } catch (e) {
      _logger.e('❌ Failed to cache agent data: $e');
    }
  }

  /// Convert PayMuleAgent to ZambianAgent for compatibility
  ZambianAgent _convertToZambianAgent(PayMuleAgent agent) {
    return ZambianAgent(
      agentId: agent.agentId,
      name: agent.name,
      phoneNumber: agent.phoneNumber,
      locationName: agent.location,
      latitude: agent.latitude,
      longitude: agent.longitude,
      services: agent.services.map((s) => s.toString().split('.').last).toList(),
      operatingHours: Map<String, String>.from(agent.businessHours),
      commissionRates: {'default': agent.commissionRate},
      cashLimit: 50000.0, // Default cash limit
      status: agent.status.toString().split('.').last,
      bozLicense: agent.verificationData['boz_license'] ?? 'VERIFIED',
      lastUpdated: agent.lastActiveAt,
    );
  }

  /// Load Zambian agents with offline-first architecture
  Future<void> loadZambianAgents() async {
    try {
      _logger.i('🇿🇲 Loading Zambian financial agents');

      // Get approved agents from Zambian Financial Registry
      final agents = await ZambiaFinancialRegistry.getApprovedAgents();

      // Cache agent locations for offline access
      await _cacheAgentLocations(agents);

      _logger.i('✅ Loaded ${agents.length} approved Zambian agents');

      // Setup offline-first connectivity monitoring
      await _setupOfflineFirstSync();

    } catch (e) {
      _logger.e('❌ Failed to load Zambian agents: $e');

      // Fallback to cached agents if available
      await _loadCachedAgents();
    }
  }

  /// Setup offline-first connectivity monitoring and sync
  Future<void> _setupOfflineFirstSync() async {
    try {
      _logger.i('📡 Setting up offline-first agent sync');

      // OFFLINE FIRST - Listen for connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen((status) {
        _logger.i('📶 Connectivity changed: $status');
        
        if (status != ConnectivityResult.none) {
          // Real-time sync when connection is available
          _syncAgentDatabase();
        } else {
          _logger.i('📴 Offline mode: Using cached agent data');
        }
      });

      // Setup periodic sync timer (every 30 minutes when online)
      _syncTimer = Timer.periodic(Duration(minutes: 30), (timer) {
        _checkAndSync();
      });

      // Initial sync if online
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        await _syncAgentDatabase();
      }

      _logger.i('✅ Offline-first sync setup completed');
    } catch (e) {
      _logger.e('❌ Failed to setup offline-first sync: $e');
    }
  }

  /// Cache agent locations for offline access
  Future<void> _cacheAgentLocations(List<ZambianAgent> agents) async {
    try {
      _logger.i('💾 Caching ${agents.length} agent locations');

      final db = await _dbHelper.database;
      
      // Clear existing cache
      await db.delete('cached_agents');

      // Insert new agent data
      for (final agent in agents) {
        await db.insert('cached_agents', {
          'agent_id': agent.agentId,
          'name': agent.name,
          'phone_number': agent.phoneNumber,
          'location_name': agent.locationName,
          'latitude': agent.latitude,
          'longitude': agent.longitude,
          'services': jsonEncode(agent.services),
          'operating_hours': jsonEncode(agent.operatingHours),
          'commission_rates': jsonEncode(agent.commissionRates),
          'cash_limit': agent.cashLimit,
          'status': agent.status,
          'boz_license': agent.bozLicense,
          'last_updated': DateTime.now().millisecondsSinceEpoch,
        });
      }

      // Update in-memory cache
      _cachedAgents = agents;
      _lastSyncTime = DateTime.now();

      _logger.i('✅ Agent locations cached successfully');
    } catch (e) {
      _logger.e('❌ Failed to cache agent locations: $e');
      rethrow;
    }
  }

  /// Real-time sync with Zambian Financial Registry
  Future<void> _syncAgentDatabase() async {
    try {
      _logger.i('🔄 Syncing agent database with Zambian Financial Registry');

      if (kProductionMode) {
        // Production: Sync with real BoZ registry
        await _syncWithBozRegistry();
      } else {
        // Development: Simulate sync
        await _simulateSync();
      }

      _lastSyncTime = DateTime.now();
      _logger.i('✅ Agent database sync completed');
    } catch (e) {
      _logger.e('❌ Agent database sync failed: $e');
      // Continue with cached data on sync failure
    }
  }

  /// Find nearby agents based on user location
  Future<List<ZambianAgent>> findNearbyAgents({
    Position? userLocation,
    double radiusKm = _defaultSearchRadius,
    List<String>? serviceTypes,
    int maxResults = _maxAgentsPerQuery,
  }) async {
    try {
      _logger.i('📍 Finding nearby agents within ${radiusKm}km');

      // Get user location if not provided
      userLocation ??= await _locationService.getCurrentLocation();
      
      if (userLocation == null) {
        _logger.w('⚠️ Location not available, returning all agents');
        return _getAllCachedAgents(maxResults);
      }

      // Search cached agents by distance
      final nearbyAgents = <ZambianAgent>[];
      
      for (final agent in _cachedAgents) {
        final distance = Geolocator.distanceBetween(
          userLocation.latitude,
          userLocation.longitude,
          agent.latitude,
          agent.longitude,
        ) / 1000; // Convert to kilometers

        if (distance <= radiusKm) {
          // Filter by service types if specified
          if (serviceTypes == null || 
              serviceTypes.any((service) => agent.services.contains(service))) {
            agent.distanceKm = distance;
            nearbyAgents.add(agent);
          }
        }
      }

      // Sort by distance and limit results
      nearbyAgents.sort((a, b) => a.distanceKm!.compareTo(b.distanceKm!));
      final results = nearbyAgents.take(maxResults).toList();

      _logger.i('✅ Found ${results.length} nearby agents');
      return results;
    } catch (e) {
      _logger.e('❌ Failed to find nearby agents: $e');
      return _getAllCachedAgents(maxResults);
    }
  }

  /// Get agent by ID
  Future<ZambianAgent?> getAgentById(String agentId) async {
    try {
      // First check in-memory cache
      final cachedAgent = _cachedAgents.firstWhere(
        (agent) => agent.agentId == agentId,
        orElse: () => throw StateError('Agent not found'),
      );
      
      if (cachedAgent != null) {
        return cachedAgent;
      }

      // Fallback to database
      final db = await _dbHelper.database;
      final result = await db.query(
        'cached_agents',
        where: 'agent_id = ?',
        whereArgs: [agentId],
      );

      if (result.isNotEmpty) {
        return ZambianAgent.fromMap(result.first);
      }

      return null;
    } catch (e) {
      _logger.e('Failed to get agent by ID: $e');
      return null;
    }
  }

  /// Search agents by name or location
  Future<List<ZambianAgent>> searchAgents({
    required String query,
    int maxResults = _maxAgentsPerQuery,
  }) async {
    try {
      _logger.i('🔍 Searching agents for: $query');

      final searchResults = _cachedAgents.where((agent) {
        final searchTerm = query.toLowerCase();
        return agent.name.toLowerCase().contains(searchTerm) ||
               agent.locationName.toLowerCase().contains(searchTerm) ||
               agent.phoneNumber.contains(searchTerm);
      }).take(maxResults).toList();

      _logger.i('✅ Found ${searchResults.length} agents matching: $query');
      return searchResults;
    } catch (e) {
      _logger.e('❌ Agent search failed: $e');
      return [];
    }
  }

  /// Get agents by service type
  Future<List<ZambianAgent>> getAgentsByService({
    required String serviceType,
    Position? userLocation,
    int maxResults = _maxAgentsPerQuery,
  }) async {
    try {
      _logger.i('🏪 Getting agents for service: $serviceType');

      var filteredAgents = _cachedAgents.where((agent) {
        return agent.services.contains(serviceType) && 
               agent.status == 'active';
      }).toList();

      // Sort by distance if location is available
      if (userLocation != null) {
        for (final agent in filteredAgents) {
          final distance = Geolocator.distanceBetween(
            userLocation.latitude,
            userLocation.longitude,
            agent.latitude,
            agent.longitude,
          ) / 1000;
          agent.distanceKm = distance;
        }
        filteredAgents.sort((a, b) => a.distanceKm!.compareTo(b.distanceKm!));
      }

      final results = filteredAgents.take(maxResults).toList();
      _logger.i('✅ Found ${results.length} agents for service: $serviceType');
      return results;
    } catch (e) {
      _logger.e('❌ Failed to get agents by service: $e');
      return [];
    }
  }

  /// Get agent statistics
  Future<AgentStatistics> getAgentStatistics() async {
    try {
      final totalAgents = _cachedAgents.length;
      final activeAgents = _cachedAgents.where((a) => a.status == 'active').length;
      final serviceTypes = <String>{};
      
      for (final agent in _cachedAgents) {
        serviceTypes.addAll(agent.services);
      }

      return AgentStatistics(
        totalAgents: totalAgents,
        activeAgents: activeAgents,
        availableServices: serviceTypes.toList(),
        lastSyncTime: _lastSyncTime,
        cacheSize: _cachedAgents.length,
      );
    } catch (e) {
      _logger.e('Failed to get agent statistics: $e');
      return AgentStatistics(
        totalAgents: 0,
        activeAgents: 0,
        availableServices: [],
        lastSyncTime: null,
        cacheSize: 0,
      );
    }
  }

  /// Check connectivity and sync if needed
  Future<void> _checkAndSync() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult != ConnectivityResult.none) {
        // Check if sync is needed (every 4 hours)
        if (_lastSyncTime == null || 
            DateTime.now().difference(_lastSyncTime!).inHours >= 4) {
          await _syncAgentDatabase();
        }
      }
    } catch (e) {
      _logger.e('Connectivity check failed: $e');
    }
  }

  /// Sync with Bank of Zambia registry (production)
  Future<void> _syncWithBozRegistry() async {
    try {
      _logger.i('🏛️ Syncing with Bank of Zambia agent registry');
      
      final updatedAgents = await ZambiaFinancialRegistry.fetchLatestAgents();
      await _cacheAgentLocations(updatedAgents);
      
      _logger.i('✅ BoZ registry sync completed');
    } catch (e) {
      _logger.e('❌ BoZ registry sync failed: $e');
      rethrow;
    }
  }

  /// Simulate sync for development
  Future<void> _simulateSync() async {
    await Future.delayed(Duration(seconds: 2));
    _logger.i('🧪 Development: Agent sync simulated');
  }

  /// Load cached agents from database
  Future<void> _loadCachedAgents() async {
    try {
      final db = await _dbHelper.database;
      final result = await db.query('cached_agents');
      
      _cachedAgents = result.map((map) => ZambianAgent.fromMap(map)).toList();
      _logger.i('📱 Loaded ${_cachedAgents.length} cached agents');
    } catch (e) {
      _logger.e('Failed to load cached agents: $e');
      _cachedAgents = [];
    }
  }

  /// Get all cached agents with limit
  List<ZambianAgent> _getAllCachedAgents(int maxResults) {
    return _cachedAgents.take(maxResults).toList();
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
    _logger.i('🧹 Agent service disposed');
  }
}

/// Zambian Financial Agent Model
class ZambianAgent {
  final String agentId;
  final String name;
  final String phoneNumber;
  final String locationName;
  final double latitude;
  final double longitude;
  final List<String> services;
  final Map<String, String> operatingHours;
  final Map<String, double> commissionRates;
  final double cashLimit;
  final String status;
  final String bozLicense;
  final DateTime lastUpdated;

  // Calculated fields
  double? distanceKm;
  bool? isOpen;

  ZambianAgent({
    required this.agentId,
    required this.name,
    required this.phoneNumber,
    required this.locationName,
    required this.latitude,
    required this.longitude,
    required this.services,
    required this.operatingHours,
    required this.commissionRates,
    required this.cashLimit,
    required this.status,
    required this.bozLicense,
    required this.lastUpdated,
    this.distanceKm,
    this.isOpen,
  });

  /// Create agent from database map
  factory ZambianAgent.fromMap(Map<String, dynamic> map) {
    return ZambianAgent(
      agentId: map['agent_id'] as String,
      name: map['name'] as String,
      phoneNumber: map['phone_number'] as String,
      locationName: map['location_name'] as String,
      latitude: map['latitude'] as double,
      longitude: map['longitude'] as double,
      services: List<String>.from(jsonDecode(map['services'] as String)),
      operatingHours: Map<String, String>.from(jsonDecode(map['operating_hours'] as String)),
      commissionRates: Map<String, double>.from(jsonDecode(map['commission_rates'] as String)),
      cashLimit: map['cash_limit'] as double,
      status: map['status'] as String,
      bozLicense: map['boz_license'] as String,
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(map['last_updated'] as int),
    );
  }

  /// Convert agent to database map
  Map<String, dynamic> toMap() {
    return {
      'agent_id': agentId,
      'name': name,
      'phone_number': phoneNumber,
      'location_name': locationName,
      'latitude': latitude,
      'longitude': longitude,
      'services': jsonEncode(services),
      'operating_hours': jsonEncode(operatingHours),
      'commission_rates': jsonEncode(commissionRates),
      'cash_limit': cashLimit,
      'status': status,
      'boz_license': bozLicense,
      'last_updated': lastUpdated.millisecondsSinceEpoch,
    };
  }

  /// Check if agent is currently open
  bool get isCurrentlyOpen {
    if (isOpen != null) return isOpen!;

    final now = DateTime.now();
    final dayOfWeek = _getDayOfWeek(now.weekday);
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    final todayHours = operatingHours[dayOfWeek];
    if (todayHours == null || todayHours == 'closed') {
      isOpen = false;
      return false;
    }

    // Parse hours (format: "09:00-17:00")
    final hoursParts = todayHours.split('-');
    if (hoursParts.length != 2) {
      isOpen = false;
      return false;
    }

    final openTime = hoursParts[0];
    final closeTime = hoursParts[1];

    isOpen = currentTime.compareTo(openTime) >= 0 && currentTime.compareTo(closeTime) <= 0;
    return isOpen!;
  }

  /// Get formatted distance
  String get formattedDistance {
    if (distanceKm == null) return 'Distance unknown';
    if (distanceKm! < 1) {
      return '${(distanceKm! * 1000).round()}m away';
    }
    return '${distanceKm!.toStringAsFixed(1)}km away';
  }

  /// Get available services as formatted string
  String get formattedServices {
    return services.join(', ');
  }

  /// Get day of week string
  String _getDayOfWeek(int weekday) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return days[weekday - 1];
  }

  /// Check if agent provides specific service
  bool providesService(String serviceType) {
    return services.contains(serviceType);
  }

  /// Get commission rate for service
  double? getCommissionRate(String serviceType) {
    return commissionRates[serviceType];
  }
}

/// Agent statistics model
class AgentStatistics {
  final int totalAgents;
  final int activeAgents;
  final List<String> availableServices;
  final DateTime? lastSyncTime;
  final int cacheSize;

  AgentStatistics({
    required this.totalAgents,
    required this.activeAgents,
    required this.availableServices,
    this.lastSyncTime,
    required this.cacheSize,
  });

  /// Get formatted sync time
  String get formattedLastSync {
    if (lastSyncTime == null) return 'Never synced';

    final now = DateTime.now();
    final difference = now.difference(lastSyncTime!);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inDays} days ago';
    }
  }

  /// Get sync status
  String get syncStatus {
    if (lastSyncTime == null) return 'Not synced';

    final now = DateTime.now();
    final difference = now.difference(lastSyncTime!);

    if (difference.inHours < 4) {
      return 'Up to date';
    } else if (difference.inHours < 24) {
      return 'Needs sync';
    } else {
      return 'Outdated';
    }
  }
}
