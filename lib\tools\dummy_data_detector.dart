#!/usr/bin/env dart

/// Pay Mule Dummy Data Detector
/// Scans the codebase for test data, sandbox configurations, and dummy content
/// that should be removed before production deployment

import 'dart:io';
import 'dart:convert';

class DummyDataDetector {
  static const List<String> suspiciousPatterns = [
    // Test/Sandbox patterns
    'test_account',
    'dummy_merchant',
    'sandbox',
    'mock',
    'fake',
    'placeholder',
    'YOUR_API_KEY',
    'YOUR_SUBSCRIPTION_KEY',
    'REPLACE_WITH',
    'TODO:',
    'FIXME:',
    
    // Test phone numbers
    '***********',
    '***********', 
    '***********',
    '***********',
    '***********',
    
    // Test endpoints
    'localhost',
    'sandbox.momo',
    'test.api',
    'staging.',
    'dev.',
    
    // Development flags
    'isProduction = false',
    'debug_mode.*true',
    'test_data_enabled.*true',
    'mock_services.*true',
  ];
  
  static const List<String> allowedTestPatterns = [
    // These are OK to keep (test files, documentation)
    'test/',
    'integration_test/',
    '_test.dart',
    'README.md',
    'TESTING.md',
    '.md',
    'example/',
    'docs/',
  ];
  
  static const List<String> criticalFiles = [
    'lib/core/config/app_config.dart',
    'lib/core/config/environment_config.dart',
    'lib/core/config/mtn_sandbox_config.dart',
    'lib/core/config/production_config.dart',
    'android/app/src/main/AndroidManifest.xml',
    'ios/Runner/Info.plist',
    'pubspec.yaml',
  ];
  
  final List<DetectionResult> results = [];
  final bool fullScan;
  
  DummyDataDetector({this.fullScan = false});
  
  /// Run the dummy data detection scan
  Future<void> runScan() async {
    print('🔍 Pay Mule Dummy Data Detector');
    print('================================');
    print('Full Scan: $fullScan');
    print('');
    
    if (fullScan) {
      await scanDirectory(Directory('lib'));
      await scanDirectory(Directory('android'));
      await scanDirectory(Directory('ios'));
      await scanFile(File('pubspec.yaml'));
    } else {
      // Quick scan of critical files only
      for (final filePath in criticalFiles) {
        final file = File(filePath);
        if (file.existsSync()) {
          await scanFile(file);
        }
      }
    }
    
    generateReport();
  }
  
  /// Scan a directory recursively
  Future<void> scanDirectory(Directory dir) async {
    if (!dir.existsSync()) return;
    
    await for (final entity in dir.list(recursive: true)) {
      if (entity is File && shouldScanFile(entity)) {
        await scanFile(entity);
      }
    }
  }
  
  /// Check if a file should be scanned
  bool shouldScanFile(File file) {
    final path = file.path;
    
    // Skip binary files
    if (path.endsWith('.png') || 
        path.endsWith('.jpg') || 
        path.endsWith('.ico') ||
        path.endsWith('.so') ||
        path.endsWith('.jar')) {
      return false;
    }
    
    // Skip build directories
    if (path.contains('/build/') || 
        path.contains('\\build\\') ||
        path.contains('/.dart_tool/') ||
        path.contains('\\.dart_tool\\')) {
      return false;
    }
    
    // Skip allowed test patterns in full scan
    if (fullScan) {
      for (final pattern in allowedTestPatterns) {
        if (path.contains(pattern)) {
          return false;
        }
      }
    }
    
    return true;
  }
  
  /// Scan a single file for suspicious patterns
  Future<void> scanFile(File file) async {
    try {
      final content = await file.readAsString();
      final lines = content.split('\n');
      
      for (int i = 0; i < lines.length; i++) {
        final line = lines[i];
        final lineNumber = i + 1;
        
        for (final pattern in suspiciousPatterns) {
          if (RegExp(pattern, caseSensitive: false).hasMatch(line)) {
            results.add(DetectionResult(
              file: file.path,
              lineNumber: lineNumber,
              pattern: pattern,
              content: line.trim(),
              severity: getSeverity(pattern, file.path),
            ));
          }
        }
      }
    } catch (e) {
      print('⚠️  Error scanning ${file.path}: $e');
    }
  }
  
  /// Determine severity of a detection
  Severity getSeverity(String pattern, String filePath) {
    // Critical patterns that must be fixed
    if (pattern.contains('isProduction = false') ||
        pattern.contains('YOUR_API_KEY') ||
        pattern.contains('REPLACE_WITH') ||
        pattern.contains('test_account') ||
        pattern.contains('dummy_merchant')) {
      return Severity.critical;
    }
    
    // High severity for production config files
    if (criticalFiles.any((critical) => filePath.contains(critical))) {
      if (pattern.contains('sandbox') || 
          pattern.contains('test') ||
          pattern.contains('mock')) {
        return Severity.high;
      }
    }
    
    // Medium severity for other suspicious patterns
    if (pattern.contains('TODO') || 
        pattern.contains('FIXME') ||
        pattern.contains('localhost')) {
      return Severity.medium;
    }
    
    return Severity.low;
  }
  
  /// Generate and display the detection report
  void generateReport() {
    print('📊 Detection Results');
    print('===================');
    
    if (results.isEmpty) {
      print('✅ No suspicious patterns detected!');
      print('   Codebase appears clean for production deployment.');
      return;
    }
    
    // Group results by severity
    final critical = results.where((r) => r.severity == Severity.critical).toList();
    final high = results.where((r) => r.severity == Severity.high).toList();
    final medium = results.where((r) => r.severity == Severity.medium).toList();
    final low = results.where((r) => r.severity == Severity.low).toList();
    
    print('Summary:');
    print('  🔴 Critical: ${critical.length}');
    print('  🟠 High: ${high.length}');
    print('  🟡 Medium: ${medium.length}');
    print('  🟢 Low: ${low.length}');
    print('');
    
    // Display critical issues first
    if (critical.isNotEmpty) {
      print('🔴 CRITICAL ISSUES (Must fix before production):');
      for (final result in critical) {
        print('   ${result.file}:${result.lineNumber}');
        print('   Pattern: ${result.pattern}');
        print('   Content: ${result.content}');
        print('');
      }
    }
    
    // Display high severity issues
    if (high.isNotEmpty) {
      print('🟠 HIGH SEVERITY ISSUES:');
      for (final result in high) {
        print('   ${result.file}:${result.lineNumber}');
        print('   Pattern: ${result.pattern}');
        print('   Content: ${result.content}');
        print('');
      }
    }
    
    // Display medium severity issues (abbreviated)
    if (medium.isNotEmpty) {
      print('🟡 MEDIUM SEVERITY ISSUES (${medium.length} found):');
      for (final result in medium.take(5)) {
        print('   ${result.file}:${result.lineNumber} - ${result.pattern}');
      }
      if (medium.length > 5) {
        print('   ... and ${medium.length - 5} more');
      }
      print('');
    }
    
    // Display low severity issues (summary only)
    if (low.isNotEmpty) {
      print('🟢 LOW SEVERITY ISSUES: ${low.length} found');
      print('');
    }
    
    // Generate recommendations
    generateRecommendations(critical, high, medium, low);
    
    // Exit with error code if critical issues found
    if (critical.isNotEmpty) {
      print('❌ PRODUCTION DEPLOYMENT BLOCKED');
      print('   Fix all critical issues before proceeding.');
      exit(1);
    } else if (high.isNotEmpty) {
      print('⚠️  HIGH SEVERITY ISSUES DETECTED');
      print('   Review and fix before production deployment.');
      exit(2);
    } else {
      print('✅ PRODUCTION DEPLOYMENT APPROVED');
      print('   No critical or high severity issues detected.');
    }
  }
  
  /// Generate recommendations based on findings
  void generateRecommendations(
    List<DetectionResult> critical,
    List<DetectionResult> high,
    List<DetectionResult> medium,
    List<DetectionResult> low,
  ) {
    print('💡 Recommendations:');
    
    if (critical.isNotEmpty) {
      print('   1. Replace all placeholder API keys with production values');
      print('   2. Set isProduction = true in app configuration');
      print('   3. Remove all test accounts and dummy merchant references');
    }
    
    if (high.isNotEmpty) {
      print('   4. Update sandbox URLs to production endpoints');
      print('   5. Remove test phone numbers from configuration');
      print('   6. Disable debug and mock services');
    }
    
    if (medium.isNotEmpty) {
      print('   7. Resolve TODO and FIXME comments');
      print('   8. Replace localhost URLs with production URLs');
    }
    
    if (low.isNotEmpty) {
      print('   9. Review remaining low-severity items');
    }
    
    print('   10. Run full test suite after making changes');
    print('   11. Verify production API connectivity');
    print('');
  }
}

/// Represents a detection result
class DetectionResult {
  final String file;
  final int lineNumber;
  final String pattern;
  final String content;
  final Severity severity;
  
  DetectionResult({
    required this.file,
    required this.lineNumber,
    required this.pattern,
    required this.content,
    required this.severity,
  });
}

/// Severity levels for detections
enum Severity {
  critical,
  high,
  medium,
  low,
}

/// Main entry point
void main(List<String> args) async {
  final fullScan = args.contains('--full-scan');
  
  final detector = DummyDataDetector(fullScan: fullScan);
  await detector.runScan();
}
