/// Zambia Mobile Money Wallet Configuration for Pay Mule MVP
/// CORE MANDATE: Mobile money-only release • No banking features • Zero breakage
/// 
/// This module configures the wallet-only flow for Zambian mobile money providers
/// and integrates with the feature lock system to ensure MVP compliance.

import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../core/config/app_config.dart';
import '../features/feature_lock.dart';
import '../features/mobile_money/data/services/mobile_money_service.dart';

/// Supported mobile money wallets in Zambia
enum MobileWallet {
  MTN_MONEY('MTN Mobile Money', 'MTN', '96', 'https://momodeveloper.mtn.com/v1'),
  AIRTEL_MONEY('Airtel Money', 'AIRTEL', '97', 'https://openapiuat.airtel.africa/mw/v2'),
  ZAMTEL_KWACHA('Zamtel Kwacha', 'ZAMTEL', '95', 'https://api.zamtel.zm/v1');

  const MobileWallet(this.displayName, this.code, this.prefix, this.apiUrl);
  
  final String displayName;
  final String code;
  final String prefix;
  final String apiUrl;
}

/// Zambian mobile money wallet configuration
class ZambiaWallets {
  static final Logger _logger = Logger();
  
  /// Supported wallets for MVP (mobile money only)
  static const List<MobileWallet> supportedWallets = [
    MobileWallet.MTN_MONEY,
    MobileWallet.AIRTEL_MONEY,
    MobileWallet.ZAMTEL_KWACHA,
  ];
  
  /// Initialize wallet-only flow for mobile money MVP
  static Future<void> setupWalletOnlyFlow() async {
    _logger.i('🇿🇲 Setting up wallet-only flow for Zambia mobile money MVP');
    
    // Ensure feature lock system is initialized
    if (!Features.isMobileMoneyEnabled()) {
      await Features.initialize();
    }
    
    // Configure registration for phone-only (no banking details)
    await Registration.requirePhoneOnly();
    
    // Set default payment providers to mobile money wallets only
    await PaymentOptions.setDefaultProviders(supportedWallets);
    
    // Configure wallet-specific settings
    await _configureWalletSettings();
    
    // Validate wallet-only configuration
    await _validateWalletOnlySetup();
    
    _logger.i('✅ Wallet-only flow setup completed successfully');
  }
  
  /// Configure wallet-specific settings for each provider
  static Future<void> _configureWalletSettings() async {
    _logger.i('⚙️ Configuring wallet-specific settings...');
    
    for (final wallet in supportedWallets) {
      await _configureWallet(wallet);
    }
    
    _logger.i('✅ All wallet settings configured');
  }
  
  /// Configure individual wallet settings
  static Future<void> _configureWallet(MobileWallet wallet) async {
    switch (wallet) {
      case MobileWallet.MTN_MONEY:
        await _configureMTNWallet();
        break;
      case MobileWallet.AIRTEL_MONEY:
        await _configureAirtelWallet();
        break;
      case MobileWallet.ZAMTEL_KWACHA:
        await _configureZamtelWallet();
        break;
    }
  }
  
  /// Configure MTN Mobile Money wallet
  static Future<void> _configureMTNWallet() async {
    _logger.d('📱 Configuring MTN Mobile Money wallet');
    
    final config = {
      'provider': 'MTN',
      'displayName': 'MTN Mobile Money',
      'prefix': '96',
      'countryCode': '260',
      'currency': 'ZMW',
      'maxAmount': 50000.0,
      'minAmount': 1.0,
      'fee': 0.0021, // Zambia mobile money levy
      'features': [
        'send_money',
        'receive_money',
        'buy_airtime',
        'pay_bills',
        'balance_inquiry',
      ],
      'ussdCode': '*303#',
      'emergencyNumber': '303',
    };
    
    await WalletRegistry.registerWallet('MTN', config);
  }
  
  /// Configure Airtel Money wallet
  static Future<void> _configureAirtelWallet() async {
    _logger.d('📱 Configuring Airtel Money wallet');
    
    final config = {
      'provider': 'AIRTEL',
      'displayName': 'Airtel Money',
      'prefix': '97',
      'countryCode': '260',
      'currency': 'ZMW',
      'maxAmount': 50000.0,
      'minAmount': 1.0,
      'fee': 0.0021,
      'features': [
        'send_money',
        'receive_money',
        'buy_airtime',
        'pay_bills',
        'balance_inquiry',
      ],
      'ussdCode': '*432#',
      'emergencyNumber': '432',
    };
    
    await WalletRegistry.registerWallet('AIRTEL', config);
  }
  
  /// Configure Zamtel Kwacha wallet
  static Future<void> _configureZamtelWallet() async {
    _logger.d('📱 Configuring Zamtel Kwacha wallet');
    
    final config = {
      'provider': 'ZAMTEL',
      'displayName': 'Zamtel Kwacha',
      'prefix': '95',
      'countryCode': '260',
      'currency': 'ZMW',
      'maxAmount': 50000.0,
      'minAmount': 1.0,
      'fee': 0.0021,
      'features': [
        'send_money',
        'receive_money',
        'buy_airtime',
        'pay_bills',
        'balance_inquiry',
      ],
      'ussdCode': '*511#',
      'emergencyNumber': '511',
    };
    
    await WalletRegistry.registerWallet('ZAMTEL', config);
  }
  
  /// Validate wallet-only setup
  static Future<void> _validateWalletOnlySetup() async {
    _logger.i('🔍 Validating wallet-only setup...');
    
    // Ensure banking features are disabled
    if (!Features.areBankingFeaturesDisabled()) {
      throw Exception('Banking features must be disabled for wallet-only flow');
    }
    
    // Ensure mobile money is enabled
    if (!Features.isMobileMoneyEnabled()) {
      throw Exception('Mobile money features must be enabled for wallet-only flow');
    }
    
    // Validate all wallets are registered
    for (final wallet in supportedWallets) {
      if (!await WalletRegistry.isWalletRegistered(wallet.code)) {
        throw Exception('Wallet ${wallet.code} is not properly registered');
      }
    }
    
    _logger.i('✅ Wallet-only setup validation passed');
  }
  
  /// Get wallet by phone number prefix
  static MobileWallet? getWalletByPhoneNumber(String phoneNumber) {
    // Remove country code and formatting
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Extract prefix (assuming Zambian format: 260XXXXXXXXX)
    String prefix;
    if (cleanNumber.startsWith('260')) {
      prefix = cleanNumber.substring(3, 5);
    } else if (cleanNumber.length >= 2) {
      prefix = cleanNumber.substring(0, 2);
    } else {
      return null;
    }
    
    // Find matching wallet
    for (final wallet in supportedWallets) {
      if (wallet.prefix == prefix) {
        return wallet;
      }
    }
    
    return null;
  }
  
  /// Get all supported wallet providers
  static List<String> getSupportedProviders() {
    return supportedWallets.map((wallet) => wallet.code).toList();
  }
  
  /// Check if a provider is supported
  static bool isProviderSupported(String provider) {
    return getSupportedProviders().contains(provider.toUpperCase());
  }
  
  /// Get wallet display information
  static Map<String, dynamic> getWalletInfo(MobileWallet wallet) {
    return {
      'code': wallet.code,
      'displayName': wallet.displayName,
      'prefix': wallet.prefix,
      'apiUrl': wallet.apiUrl,
      'icon': _getWalletIcon(wallet),
      'color': _getWalletColor(wallet),
    };
  }
  
  /// Get wallet-specific icon
  static IconData _getWalletIcon(MobileWallet wallet) {
    switch (wallet) {
      case MobileWallet.MTN_MONEY:
        return Icons.phone_android;
      case MobileWallet.AIRTEL_MONEY:
        return Icons.smartphone;
      case MobileWallet.ZAMTEL_KWACHA:
        return Icons.mobile_friendly;
    }
  }
  
  /// Get wallet-specific color
  static Color _getWalletColor(MobileWallet wallet) {
    switch (wallet) {
      case MobileWallet.MTN_MONEY:
        return const Color(0xFFFFD700); // MTN Yellow
      case MobileWallet.AIRTEL_MONEY:
        return const Color(0xFFFF0000); // Airtel Red
      case MobileWallet.ZAMTEL_KWACHA:
        return const Color(0xFF008000); // Zamtel Green
    }
  }
}

/// Registration configuration for wallet-only flow
class Registration {
  static final Logger _logger = Logger();
  
  /// Configure registration to require phone number only (no banking details)
  static Future<void> requirePhoneOnly() async {
    _logger.i('📱 Configuring phone-only registration for wallet MVP');
    
    final registrationConfig = {
      'required_fields': ['phone_number'],
      'optional_fields': ['full_name', 'email'],
      'disabled_fields': [
        'bank_account_number',
        'bank_name',
        'bank_branch',
        'bank_swift_code',
        'debit_card_number',
        'credit_card_number',
      ],
      'verification_method': 'SMS_OTP',
      'kyc_level': 'BASIC', // Start with basic KYC
      'wallet_only': true,
    };
    
    await RegistrationService.updateConfiguration(registrationConfig);
    _logger.i('✅ Phone-only registration configured');
  }
}

/// Payment options configuration for wallet-only flow
class PaymentOptions {
  static final Logger _logger = Logger();
  
  /// Set default payment providers to mobile money wallets only
  static Future<void> setDefaultProviders(List<MobileWallet> wallets) async {
    _logger.i('💳 Setting default payment providers to mobile money wallets');
    
    final providerCodes = wallets.map((wallet) => wallet.code).toList();
    
    final paymentConfig = {
      'default_providers': providerCodes,
      'enabled_methods': ['MOBILE_MONEY'],
      'disabled_methods': [
        'BANK_TRANSFER',
        'DEBIT_CARD',
        'CREDIT_CARD',
        'BANK_ACCOUNT',
      ],
      'fallback_order': providerCodes,
      'auto_detection': true,
      'wallet_only_mode': true,
    };
    
    await PaymentService.updateConfiguration(paymentConfig);
    _logger.i('✅ Default payment providers set to: ${providerCodes.join(', ')}');
  }
}

/// Wallet registry for managing wallet configurations
class WalletRegistry {
  static final Map<String, Map<String, dynamic>> _registeredWallets = {};
  static final Logger _logger = Logger();
  
  /// Register a wallet with its configuration
  static Future<void> registerWallet(String code, Map<String, dynamic> config) async {
    _registeredWallets[code] = config;
    _logger.d('📝 Registered wallet: $code');
  }
  
  /// Check if a wallet is registered
  static Future<bool> isWalletRegistered(String code) async {
    return _registeredWallets.containsKey(code);
  }
  
  /// Get wallet configuration
  static Map<String, dynamic>? getWalletConfig(String code) {
    return _registeredWallets[code];
  }
  
  /// Get all registered wallets
  static Map<String, Map<String, dynamic>> getAllWallets() {
    return Map.from(_registeredWallets);
  }
}

/// Placeholder services (to be implemented)
class RegistrationService {
  static Future<void> updateConfiguration(Map<String, dynamic> config) async {
    // Implementation would update registration configuration
  }
}

class PaymentService {
  static Future<void> updateConfiguration(Map<String, dynamic> config) async {
    // Implementation would update payment configuration
  }
}
