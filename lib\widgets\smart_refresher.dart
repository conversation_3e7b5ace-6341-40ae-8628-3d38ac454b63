import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../core/refresh/smart_refresh_controller.dart';
import '../core/data_usage/data_usage_monitor.dart';
import '../core/network/network_quality_detector.dart';

/// ZambiaRefresh - Smart refresh widget optimized for Zambian mobile networks
/// Features data-saving mode, connection-aware refreshing, and exponential backoff
class ZambiaRefresh extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final Future<void> Function()? onLoading;
  final String? operationName;
  final bool enablePullUp;
  final bool enablePullDown;
  final Widget? header;
  final Widget? footer;

  const ZambiaRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.onLoading,
    this.operationName,
    this.enablePullUp = false,
    this.enablePullDown = true,
    this.header,
    this.footer,
  });

  @override
  State<ZambiaRefresh> createState() => _ZambiaRefreshState();
}

class _ZambiaRefreshState extends State<ZambiaRefresh> {
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  final SmartRefreshController _smartController = SmartRefreshController();
  final DataUsageMonitor _dataMonitor = DataUsageMonitor();
  final NetworkQualityDetector _networkDetector = NetworkQualityDetector();

  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  Future<void> _initializeControllers() async {
    try {
      await Future.wait([
        _smartController.initialize(),
        _dataMonitor.initialize(),
        _networkDetector.initialize(),
      ]);
      
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      debugPrint('Failed to initialize refresh controllers: $e');
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  /// Handle pull-to-refresh
  void _onRefresh() async {
    if (!_isInitialized) {
      _refreshController.refreshCompleted();
      return;
    }

    try {
      // Check connection first
      final hasConnection = await Connectivity().checkConnectivity();
      
      if (hasConnection == ConnectivityResult.none) {
        _showOfflineSnackbar();
        _refreshController.refreshCompleted();
        return;
      }

      // Check data usage limits
      final estimatedDataUsage = _dataMonitor.getEstimatedDataUsage(
        widget.operationName ?? 'refresh'
      );

      if (!_dataMonitor.canUseDataForOperation(estimatedDataUsage)) {
        _showDataLimitSnackbar();
        _refreshController.refreshCompleted();
        return;
      }

      // Perform smart refresh
      await _smartController.smartRefresh(
        refreshFunction: () async {
          await widget.onRefresh();
          
          // Track data usage
          await _dataMonitor.trackDataUsage(
            dataUsedMB: estimatedDataUsage,
            category: 'refresh',
            operation: widget.operationName,
          );
        },
        fallbackFunction: () async {
          // Use cached data - no network call needed
        },
        operationName: widget.operationName ?? 'refresh',
      );

      _refreshController.refreshCompleted();

    } catch (e) {
      debugPrint('Refresh failed: $e');
      _refreshController.refreshFailed();
      _showErrorSnackbar(e.toString());
    }
  }

  /// Handle load more
  void _onLoading() async {
    if (!_isInitialized || widget.onLoading == null) {
      _refreshController.loadComplete();
      return;
    }

    try {
      // Check connection
      final hasConnection = await Connectivity().checkConnectivity();
      
      if (hasConnection == ConnectivityResult.none) {
        _refreshController.loadNoData();
        return;
      }

      // Check data usage
      final estimatedDataUsage = _dataMonitor.getEstimatedDataUsage('load_more');

      if (!_dataMonitor.canUseDataForOperation(estimatedDataUsage)) {
        _refreshController.loadNoData();
        return;
      }

      // Perform load more
      await widget.onLoading!();
      
      // Track data usage
      await _dataMonitor.trackDataUsage(
        dataUsedMB: estimatedDataUsage,
        category: 'refresh',
        operation: 'load_more',
      );

      _refreshController.loadComplete();

    } catch (e) {
      debugPrint('Load more failed: $e');
      _refreshController.loadFailed();
    }
  }

  /// Show offline mode snackbar
  void _showOfflineSnackbar() {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.wifi_off, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'No internet connection. Showing cached data.',
                style: GoogleFonts.inter(fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange[700],
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () => _onRefresh(),
        ),
      ),
    );
  }

  /// Show data limit snackbar
  void _showDataLimitSnackbar() {
    if (!mounted) return;
    
    final usagePercentage = _dataMonitor.monthlyUsagePercentage;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.data_usage, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Data limit reached (${usagePercentage.toStringAsFixed(0)}%). Using cached data.',
                style: GoogleFonts.inter(fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red[700],
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'View Usage',
          textColor: Colors.white,
          onPressed: () => _showDataUsageDialog(),
        ),
      ),
    );
  }

  /// Show error snackbar
  void _showErrorSnackbar(String error) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Refresh failed. Using cached data.',
                style: GoogleFonts.inter(fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red[600],
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () => _onRefresh(),
        ),
      ),
    );
  }

  /// Show data usage dialog
  void _showDataUsageDialog() {
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => DataUsageDialog(dataMonitor: _dataMonitor),
    );
  }

  /// Build custom refresh header
  Widget _buildRefreshHeader() {
    if (widget.header != null) return widget.header!;
    
    return CustomHeader(
      builder: (context, mode) {
        Widget body;
        
        if (mode == RefreshStatus.idle) {
          body = Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.arrow_downward, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                'Pull to refresh',
                style: GoogleFonts.inter(color: Colors.grey[600]),
              ),
            ],
          );
        } else if (mode == RefreshStatus.canRefresh) {
          body = Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.refresh, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'Release to refresh',
                style: GoogleFonts.inter(color: Theme.of(context).primaryColor),
              ),
            ],
          );
        } else if (mode == RefreshStatus.refreshing) {
          body = Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation(Theme.of(context).primaryColor),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Refreshing...',
                style: GoogleFonts.inter(color: Theme.of(context).primaryColor),
              ),
            ],
          );
        } else {
          body = Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.green[600]),
              const SizedBox(width: 8),
              Text(
                'Refresh complete',
                style: GoogleFonts.inter(color: Colors.green[600]),
              ),
            ],
          );
        }
        
        return Container(
          height: 60,
          child: Center(child: body),
        );
      },
    );
  }

  /// Build custom load footer
  Widget _buildLoadFooter() {
    if (widget.footer != null) return widget.footer!;
    
    return CustomFooter(
      builder: (context, mode) {
        Widget body;
        
        if (mode == LoadStatus.idle) {
          body = Text(
            'Pull up to load more',
            style: GoogleFonts.inter(color: Colors.grey[600]),
          );
        } else if (mode == LoadStatus.loading) {
          body = Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation(Theme.of(context).primaryColor),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Loading...',
                style: GoogleFonts.inter(color: Theme.of(context).primaryColor),
              ),
            ],
          );
        } else if (mode == LoadStatus.noMore) {
          body = Text(
            'No more data',
            style: GoogleFonts.inter(color: Colors.grey[600]),
          );
        } else {
          body = Text(
            'Load failed',
            style: GoogleFonts.inter(color: Colors.red[600]),
          );
        }
        
        return Container(
          height: 50,
          child: Center(child: body),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Initializing smart refresh...',
              style: GoogleFonts.inter(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: widget.enablePullDown,
      enablePullUp: widget.enablePullUp,
      header: _buildRefreshHeader(),
      footer: _buildLoadFooter(),
      onRefresh: _onRefresh,
      onLoading: widget.enablePullUp ? _onLoading : null,
      child: widget.child,
    );
  }
}

/// Data usage dialog widget
class DataUsageDialog extends StatelessWidget {
  final DataUsageMonitor dataMonitor;

  const DataUsageDialog({
    super.key,
    required this.dataMonitor,
  });

  @override
  Widget build(BuildContext context) {
    final usage = dataMonitor.getUsageSummary();
    final monthly = usage['monthly'] as Map<String, dynamic>;
    final daily = usage['daily'] as Map<String, dynamic>;

    return AlertDialog(
      title: Text(
        'Data Usage',
        style: GoogleFonts.inter(fontWeight: FontWeight.w600),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildUsageSection('Monthly Usage', monthly),
          const SizedBox(height: 16),
          _buildUsageSection('Daily Usage', daily),
          const SizedBox(height: 16),
          Text(
            'Tip: Connect to WiFi to avoid data charges',
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Close',
            style: GoogleFonts.inter(color: Theme.of(context).primaryColor),
          ),
        ),
      ],
    );
  }

  Widget _buildUsageSection(String title, Map<String, dynamic> data) {
    final used = (data['used'] as double);
    final limit = (data['limit'] as double);
    final percentage = (data['percentage'] as double);
    final isWarning = data['isWarning'] as bool;
    final isCritical = data['isCritical'] as bool;

    Color progressColor = Colors.green;
    if (isCritical) {
      progressColor = Colors.red;
    } else if (isWarning) {
      progressColor = Colors.orange;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.inter(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation(progressColor),
        ),
        const SizedBox(height: 4),
        Text(
          '${used.toStringAsFixed(2)} MB / ${limit.toStringAsFixed(1)} MB (${percentage.toStringAsFixed(1)}%)',
          style: GoogleFonts.inter(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }
}
