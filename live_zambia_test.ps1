# Zambia Pay - Live End-to-End Testing Framework (PowerShell)
# Real-world scenario testing on physical devices with Eastern Province simulation
# Usage: .\live_zambia_test.ps1 -UserPhone "+26096XXXXXXX" -Scenarios "market_payment,zesco_bill,chilimba_request" -NetworkProfile "unstable_2g" -EnableVoiceGuidance -MonitorRAM "512mb"

param(
    [string]$UserPhone = "",
    [string]$Scenarios = "market_payment,zesco_bill,chilimba_request",
    [string]$NetworkProfile = "unstable_2g",
    [switch]$EnableVoiceGuidance,
    [string]$MonitorRAM = "512mb",
    [string]$DeviceID = "",
    [int]$TestDuration = 1800,
    [string]$Region = "eastern_province",
    [string]$Language = "nyanja",
    [switch]$VerboseOutput,
    [switch]$Help
)

# Global variables
$script:StartTime = Get-Date
$script:TotalScenarios = 0
$script:PassedScenarios = 0
$script:FailedScenarios = 0
$script:DevicePerformance = @()
$script:NetworkIssues = 0
$script:OutputDir = "live_test_results"

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Magenta = "Magenta"
$Cyan = "Cyan"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor $Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Debug {
    param([string]$Message)
    if ($VerboseOutput) {
        Write-Host "[DEBUG] $Message" -ForegroundColor $Magenta
    }
}

function Write-Critical {
    param([string]$Message)
    Write-Host "[CRITICAL] $Message" -ForegroundColor $Cyan
}

# Show help information
function Show-Help {
    @"
Zambia Pay Live End-to-End Testing Framework (PowerShell)

USAGE:
    .\live_zambia_test.ps1 [OPTIONS]

REQUIRED OPTIONS:
    -UserPhone PHONE             Zambian phone number for testing (+26096XXXXXXX)

OPTIONAL OPTIONS:
    -Scenarios LIST              Comma-separated test scenarios
                                (default: "market_payment,zesco_bill,chilimba_request")
    -NetworkProfile PROFILE      Network simulation profile
                                (options: stable_4g, unstable_3g, unstable_2g, intermittent)
    -EnableVoiceGuidance         Enable voice guidance testing
    -MonitorRAM SIZE            RAM constraint simulation (default: 512mb)
    -DeviceID ID                Specific device ID for testing
    -TestDuration SECONDS       Test duration in seconds (default: 1800)
    -Region REGION              Zambian region (default: eastern_province)
    -Language LANG              Test language (default: nyanja)
    -VerboseOutput              Enable verbose output
    -Help                       Show this help message

AVAILABLE SCENARIOS:
    market_payment              Rural market vendor payment with QR code
    zesco_bill                  ZESCO electricity bill payment with auto-alerts
    chilimba_request            Community savings group loan request
    airtime_purchase            Emergency airtime purchase
    water_bill                  NWSC water bill payment
    school_fees                 School fee payment with receipt

NETWORK PROFILES:
    stable_4g                   Good 4G connection (urban areas)
    unstable_3g                 Intermittent 3G (semi-urban)
    unstable_2g                 Poor 2G connection (rural areas)
    intermittent                Frequent disconnections (remote areas)

EXAMPLES:
    # Basic rural market test
    .\live_zambia_test.ps1 -UserPhone "+************"

    # Comprehensive Eastern Province test
    .\live_zambia_test.ps1 -UserPhone "+************" -Scenarios "market_payment,zesco_bill,chilimba_request" -NetworkProfile "unstable_2g" -EnableVoiceGuidance -MonitorRAM "512mb"

"@
}

# Initialize testing environment
function Initialize-Testing {
    Write-Info "Zambia Pay Live Testing Framework"
    Write-Info "Region: $Region | Language: $Language"
    Write-Info "User Phone: $UserPhone"
    Write-Info "Scenarios: $Scenarios"
    Write-Info "Network Profile: $NetworkProfile"
    Write-Info "RAM Constraint: $MonitorRAM"
    Write-Host ""
    
    # Validate user phone number
    if ($UserPhone -notmatch '^\+260(96|97|95)[0-9]{7}$') {
        Write-Error "Invalid Zambian phone number format. Expected: +26096XXXXXXX, +26097XXXXXXX, or +26095XXXXXXX"
        exit 1
    }
    
    # Create output directory
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    New-Item -ItemType Directory -Path "$script:OutputDir\$timestamp" -Force | Out-Null
    
    # Check ADB and device connectivity
    Test-DeviceConnectivity
    
    # Install or update APK
    Install-ReleaseAPK
    
    # Configure device for testing
    Set-DeviceConfiguration
    
    Write-Status "Testing environment initialized"
}

# Check device connectivity
function Test-DeviceConnectivity {
    Write-Info "Checking device connectivity..."
    
    if (-not (Get-Command adb -ErrorAction SilentlyContinue)) {
        Write-Error "ADB (Android Debug Bridge) is not installed"
        exit 1
    }
    
    # Get connected devices
    $devices = adb devices | Where-Object { $_ -match "device$" }
    
    if ($devices.Count -eq 0) {
        Write-Error "No Android devices connected. Please connect a device and enable USB debugging."
        exit 1
    } elseif ($devices.Count -gt 1 -and -not $DeviceID) {
        Write-Warning "Multiple devices connected. Please specify -DeviceID or disconnect other devices."
        adb devices
        exit 1
    }
    
    # Set device ID if not specified
    if (-not $DeviceID) {
        $script:DeviceID = ($devices[0] -split "\s+")[0]
    }
    
    Write-Status "Connected to device: $script:DeviceID"
    
    # Get device info
    $deviceModel = adb -s $script:DeviceID shell getprop ro.product.model
    $androidVersion = adb -s $script:DeviceID shell getprop ro.build.version.release
    
    Write-Info "Device: $deviceModel (Android $androidVersion)"
}

# Install release APK
function Install-ReleaseAPK {
    Write-Info "Installing Zambia Pay release APK..."
    
    # Look for APK file
    $apkFile = ""
    if (Test-Path "build\app\outputs\flutter-apk\app-release.apk") {
        $apkFile = "build\app\outputs\flutter-apk\app-release.apk"
    } elseif (Test-Path "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk") {
        $apkFile = "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk"
    } else {
        Write-Warning "No release APK found. Building APK..."
        Build-ReleaseAPK
        $apkFile = "build\app\outputs\flutter-apk\app-release.apk"
    }
    
    # Install APK
    Write-Debug "Installing APK: $apkFile"
    adb -s $script:DeviceID install -r $apkFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Status "APK installed successfully"
    } else {
        Write-Error "Failed to install APK"
        exit 1
    }
}

# Build release APK
function Build-ReleaseAPK {
    Write-Info "Building release APK for Eastern Province testing..."
    
    flutter clean
    flutter pub get
    
    # Build with Eastern Province configuration
    flutter build apk `
        --dart-define=ENV=sandbox `
        --dart-define=REGION=$Region `
        --dart-define=LANGUAGE=$Language `
        --dart-define=TEST_MODE=true `
        --dart-define=LIVE_TESTING=true `
        --dart-define=NETWORK_PROFILE=$NetworkProfile `
        --dart-define=ENABLE_VOICE_GUIDANCE=$EnableVoiceGuidance `
        --release
    
    if ($LASTEXITCODE -eq 0) {
        Write-Status "APK built successfully"
    } else {
        Write-Error "Failed to build APK"
        exit 1
    }
}

# Configure device for testing
function Set-DeviceConfiguration {
    Write-Info "Configuring device for Zambian testing..."
    
    # Set device language and region
    adb -s $script:DeviceID shell "setprop persist.sys.language en"
    adb -s $script:DeviceID shell "setprop persist.sys.country ZM"
    
    # Configure network simulation
    Set-NetworkProfile
    
    # Enable accessibility for voice guidance testing
    if ($EnableVoiceGuidance) {
        Set-VoiceGuidance
    }
    
    # Clear app data for fresh start
    adb -s $script:DeviceID shell pm clear com.zambiapay.app 2>$null
    
    Write-Status "Device configured for testing"
}

# Configure network profile simulation
function Set-NetworkProfile {
    Write-Debug "Setting network profile: $NetworkProfile"
    
    switch ($NetworkProfile) {
        "stable_4g" {
            Write-Debug "Configuring stable 4G profile"
        }
        "unstable_3g" {
            Write-Debug "Configuring unstable 3G profile"
            adb -s $script:DeviceID shell "settings put global airplane_mode_on 0"
        }
        "unstable_2g" {
            Write-Debug "Configuring unstable 2G profile"
        }
        "intermittent" {
            Write-Debug "Configuring intermittent profile"
        }
        default {
            Write-Warning "Unknown network profile: $NetworkProfile"
        }
    }
}

# Configure voice guidance
function Set-VoiceGuidance {
    Write-Debug "Enabling voice guidance accessibility features"
    
    # Enable TalkBack/accessibility services
    adb -s $script:DeviceID shell "settings put secure accessibility_enabled 1"
}

# Execute test scenarios
function Start-ScenarioExecution {
    Write-Info "Executing test scenarios..."
    
    $scenarioList = $Scenarios -split ','
    $script:TotalScenarios = $scenarioList.Count
    
    foreach ($scenario in $scenarioList) {
        $scenario = $scenario.Trim()
        Write-Info "Starting scenario: $scenario"
        
        switch ($scenario) {
            "market_payment" { Invoke-MarketPaymentScenario }
            "zesco_bill" { Invoke-ZescoBillScenario }
            "chilimba_request" { Invoke-ChilimbaRequestScenario }
            "airtime_purchase" { Invoke-AirtimePurchaseScenario }
            "water_bill" { Invoke-WaterBillScenario }
            "school_fees" { Invoke-SchoolFeesScenario }
            default { Write-Warning "Unknown scenario: $scenario"; continue }
        }
        
        # Monitor device performance after each scenario
        Test-DevicePerformance $scenario
        
        # Brief pause between scenarios
        Start-Sleep -Seconds 5
    }
}

# Execute market payment scenario
function Invoke-MarketPaymentScenario {
    Write-Info "Testing rural market vendor payment..."
    
    $scenarioStart = Get-Date
    
    # Launch app and execute scenario steps
    Start-App
    Invoke-UserAction "tap" "qr_payment_button"
    Start-Sleep -Seconds 2
    
    # Simulate QR scan
    Invoke-QRScan "MARKET_VENDOR_001" "K25.50"
    
    # Enter PIN and confirm
    Enter-PIN "1234"
    Invoke-UserAction "tap" "confirm_payment"
    
    # Wait for completion
    $success = Wait-TransactionCompletion 30
    
    $scenarioEnd = Get-Date
    $duration = ($scenarioEnd - $scenarioStart).TotalSeconds
    
    if ($success) {
        Write-Status "Market payment scenario completed successfully ($($duration)s)"
        $script:PassedScenarios++
    } else {
        Write-Error "Market payment scenario failed ($($duration)s)"
        $script:FailedScenarios++
    }
}

# Main execution function
function Main {
    if ($Help) {
        Show-Help
        return
    }

    # Validate required parameters
    if (-not $UserPhone) {
        Write-Error "User phone number is required. Use -UserPhone '+26096XXXXXXX'"
        Show-Help
        exit 1
    }

    # Initialize testing environment
    Initialize-Testing
    
    # Execute test scenarios
    Start-ScenarioExecution
    
    # Generate test report
    New-TestReport
    
    # Final validation
    $successRate = [math]::Round(($script:PassedScenarios * 100 / $script:TotalScenarios), 0)
    
    if ($successRate -ge 80) {
        Write-Critical "Zambia Pay LIVE TESTING SUCCESSFUL - Ready for Eastern Province deployment"
        Write-Host ""
        Write-Status "Summary:"
        Write-Status "  Scenarios Passed: $($script:PassedScenarios)/$($script:TotalScenarios) ($successRate%)"
        Write-Status "  Device Performance: Acceptable"
        Write-Status "  Network Resilience: $(if ($script:NetworkIssues -lt 5) { "Good" } else { "Needs Improvement" })"
        Write-Host ""
        Write-Info "Ready for rural Zambian deployment!"
        exit 0
    } else {
        Write-Critical "Zambia Pay LIVE TESTING FAILED - Critical issues detected"
        Write-Host ""
        Write-Error "Summary:"
        Write-Error "  Scenarios Failed: $($script:FailedScenarios)/$($script:TotalScenarios)"
        Write-Error "  Success Rate: $successRate% (minimum required: 80%)"
        Write-Host ""
        Write-Error "DO NOT DEPLOY - Fix critical issues first!"
        exit 1
    }
}

# Execute main function
Main
