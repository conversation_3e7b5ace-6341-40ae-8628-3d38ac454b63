#!/bin/bash

# Payment Success Rate Monitoring and Automatic Rollback System
# Monitors payment success rates and triggers rollback if below 99.9%
# Usage: ./payment_monitoring.sh [--continuous] [--threshold=99.9]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PAYMENT_SUCCESS_THRESHOLD=${PAYMENT_SUCCESS_THRESHOLD:-99.9}
STABLE_COMMIT="PAYMULE_STABLE_v3"
MONITORING_INTERVAL=60  # seconds
LOG_FILE="payment_monitoring_$(date +%Y%m%d_%H%M%S).log"
ALERT_PHONE_NUMBERS=("260961234567" "260971234567" "260951234567")  # Zambian numbers
CONTINUOUS_MODE=false
CHECK_DURATION=300  # 5 minutes of data for rate calculation

print_info() {
    echo -e "${BLUE}[MONITOR]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[CRITICAL]${NC} $1" | tee -a "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}================================${NC}" | tee -a "$LOG_FILE"
    echo -e "${BLUE}$1${NC}" | tee -a "$LOG_FILE"
    echo -e "${BLUE}================================${NC}" | tee -a "$LOG_FILE"
}

# Initialize monitoring system
initialize_monitoring() {
    print_header "PAYMENT SUCCESS RATE MONITORING SYSTEM"
    
    echo "Payment Monitoring System Started: $(date)" > "$LOG_FILE"
    echo "Threshold: ${PAYMENT_SUCCESS_THRESHOLD}%" >> "$LOG_FILE"
    echo "Stable Commit: $STABLE_COMMIT" >> "$LOG_FILE"
    echo "========================================" >> "$LOG_FILE"
    
    print_info "Monitoring system initialized"
    print_info "Success rate threshold: ${PAYMENT_SUCCESS_THRESHOLD}%"
    print_info "Stable commit for rollback: $STABLE_COMMIT"
    print_info "Log file: $LOG_FILE"
}

# Get current payment success rate
get_payment_success_rate() {
    local success_rate
    
    # In production, this would query your actual payment database
    # For demo, we'll simulate payment data
    
    print_info "Calculating payment success rate over last $CHECK_DURATION seconds..."
    
    # Simulate payment data collection
    local total_payments=$(get_total_payments)
    local successful_payments=$(get_successful_payments)
    
    if [ "$total_payments" -eq 0 ]; then
        print_warning "No payment data available"
        echo "100.0"  # Default to 100% if no data
        return
    fi
    
    # Calculate success rate
    success_rate=$(echo "scale=2; ($successful_payments * 100) / $total_payments" | bc -l)
    
    print_info "Payment statistics:"
    print_info "  Total payments: $total_payments"
    print_info "  Successful payments: $successful_payments"
    print_info "  Success rate: ${success_rate}%"
    
    echo "$success_rate"
}

# Simulate getting total payments (replace with actual database query)
get_total_payments() {
    # In production, this would be something like:
    # sqlite3 payments.db "SELECT COUNT(*) FROM transactions WHERE created_at > datetime('now', '-$CHECK_DURATION seconds')"
    
    # Simulate varying payment volumes
    local base_payments=1000
    local variance=$((RANDOM % 200))
    echo $((base_payments + variance))
}

# Simulate getting successful payments (replace with actual database query)
get_successful_payments() {
    local total_payments=$(get_total_payments)
    
    # Simulate different success rates for testing
    local current_hour=$(date +%H)
    
    if [ "$current_hour" -eq 14 ]; then
        # Simulate a problem at 2 PM for testing
        local success_rate=98.5
    else
        # Normal operation
        local success_rate=99.95
    fi
    
    local successful=$(echo "scale=0; ($total_payments * $success_rate) / 100" | bc -l)
    echo "${successful%.*}"  # Remove decimal part
}

# Check if git repository is available
check_git_repository() {
    if [ ! -d ".git" ]; then
        print_error "Not in a git repository"
        return 1
    fi
    
    # Check if stable commit exists
    if ! git rev-parse --verify "$STABLE_COMMIT" >/dev/null 2>&1; then
        print_error "Stable commit $STABLE_COMMIT not found"
        print_info "Available commits:"
        git log --oneline -10
        return 1
    fi
    
    return 0
}

# Revert to stable commit
revert_to_commit() {
    local commit="$1"
    
    print_error "🚨 INITIATING EMERGENCY ROLLBACK 🚨"
    print_error "Reverting to stable commit: $commit"
    
    # Create backup of current state
    local backup_branch="backup_$(date +%Y%m%d_%H%M%S)"
    git checkout -b "$backup_branch"
    print_info "Current state backed up to branch: $backup_branch"
    
    # Revert to stable commit
    git checkout main
    git reset --hard "$commit"
    
    print_success "Successfully reverted to commit: $commit"
    
    # Log the rollback
    echo "EMERGENCY ROLLBACK EXECUTED: $(date)" >> "$LOG_FILE"
    echo "Reverted to: $commit" >> "$LOG_FILE"
    echo "Backup branch: $backup_branch" >> "$LOG_FILE"
    
    return 0
}

# Send SMS alert
send_sms_alert() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local full_message="[ZAMBIA PAY ALERT] $message - $timestamp"
    
    print_error "📱 Sending SMS alerts..."
    
    for phone in "${ALERT_PHONE_NUMBERS[@]}"; do
        if send_sms_to_number "$phone" "$full_message"; then
            print_success "SMS sent to $phone"
        else
            print_error "Failed to send SMS to $phone"
        fi
    done
}

# Send SMS to specific number (implement with your SMS provider)
send_sms_to_number() {
    local phone="$1"
    local message="$2"
    
    # In production, integrate with your SMS provider (MTN, Airtel, Zamtel)
    # Example implementations:
    
    # MTN SMS API
    # curl -X POST "https://api.mtn.zm/sms/v1/send" \
    #   -H "Authorization: Bearer $MTN_API_TOKEN" \
    #   -H "Content-Type: application/json" \
    #   -d "{\"to\":\"$phone\",\"message\":\"$message\"}"
    
    # Airtel SMS API
    # curl -X POST "https://api.airtel.zm/sms/v1/send" \
    #   -H "Authorization: Bearer $AIRTEL_API_TOKEN" \
    #   -H "Content-Type: application/json" \
    #   -d "{\"to\":\"$phone\",\"message\":\"$message\"}"
    
    # For demo, simulate SMS sending
    print_info "SMS to $phone: $message"
    
    # Simulate success/failure
    if [ $((RANDOM % 10)) -lt 9 ]; then
        return 0  # 90% success rate
    else
        return 1  # 10% failure rate
    fi
}

# Main monitoring function
monitor_payment_success_rate() {
    local payment_success_rate
    
    print_info "Checking payment success rate..."
    
    payment_success_rate=$(get_payment_success_rate)
    
    # Compare with threshold using bc for floating point comparison
    local below_threshold=$(echo "$payment_success_rate < $PAYMENT_SUCCESS_THRESHOLD" | bc -l)
    
    if [ "$below_threshold" -eq 1 ]; then
        print_error "🚨 CRITICAL: Payment success rate ${payment_success_rate}% is below threshold ${PAYMENT_SUCCESS_THRESHOLD}%"
        
        # Execute rollback sequence
        if check_git_repository; then
            if revert_to_commit "$STABLE_COMMIT"; then
                send_sms_alert "Deployment failed - Reverted to $STABLE_COMMIT (Success rate: ${payment_success_rate}%)"
                print_error "🔄 Emergency rollback completed"
                return 1  # Indicate rollback was triggered
            else
                send_sms_alert "CRITICAL: Rollback failed - Manual intervention required"
                print_error "💥 Rollback failed - manual intervention required"
                return 2  # Indicate rollback failed
            fi
        else
            send_sms_alert "CRITICAL: Cannot rollback - Git repository issues"
            print_error "💥 Cannot perform rollback - git repository issues"
            return 2
        fi
    else
        print_success "✅ Payment success rate ${payment_success_rate}% is above threshold ${PAYMENT_SUCCESS_THRESHOLD}%"
        return 0  # All good
    fi
}

# Continuous monitoring mode
run_continuous_monitoring() {
    print_header "STARTING CONTINUOUS PAYMENT MONITORING"
    
    print_info "Continuous monitoring enabled"
    print_info "Check interval: $MONITORING_INTERVAL seconds"
    print_info "Press Ctrl+C to stop monitoring"
    
    local check_count=0
    
    while true; do
        check_count=$((check_count + 1))
        
        print_info "--- Check #$check_count at $(date) ---"
        
        local result
        monitor_payment_success_rate
        result=$?
        
        case $result in
            0)
                print_success "System operating normally"
                ;;
            1)
                print_error "Emergency rollback triggered - stopping monitoring"
                break
                ;;
            2)
                print_error "Critical error - continuing monitoring but manual intervention needed"
                ;;
        esac
        
        print_info "Next check in $MONITORING_INTERVAL seconds..."
        sleep $MONITORING_INTERVAL
    done
}

# Parse command line arguments
parse_arguments() {
    for arg in "$@"; do
        case $arg in
            --continuous)
                CONTINUOUS_MODE=true
                shift
                ;;
            --threshold=*)
                PAYMENT_SUCCESS_THRESHOLD="${arg#*=}"
                shift
                ;;
            --interval=*)
                MONITORING_INTERVAL="${arg#*=}"
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown argument: $arg"
                show_help
                exit 1
                ;;
        esac
    done
}

show_help() {
    echo "Payment Success Rate Monitoring System"
    echo "======================================"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --continuous              Run in continuous monitoring mode"
    echo "  --threshold=PERCENT       Set success rate threshold (default: 99.9)"
    echo "  --interval=SECONDS        Set monitoring interval (default: 60)"
    echo "  --help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                        # Single check"
    echo "  $0 --continuous           # Continuous monitoring"
    echo "  $0 --threshold=99.5       # Custom threshold"
    echo "  $0 --continuous --interval=30  # Check every 30 seconds"
    echo ""
    echo "Environment Variables:"
    echo "  PAYMENT_SUCCESS_THRESHOLD # Success rate threshold (default: 99.9)"
}

# Main execution
main() {
    parse_arguments "$@"
    initialize_monitoring
    
    # Check if bc is available for floating point calculations
    if ! command -v bc &> /dev/null; then
        print_error "bc calculator not found - required for floating point calculations"
        exit 1
    fi
    
    if [ "$CONTINUOUS_MODE" = true ]; then
        run_continuous_monitoring
    else
        monitor_payment_success_rate
        exit $?
    fi
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
