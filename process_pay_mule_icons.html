<!DOCTYPE html>
<html>
<head>
    <title>Pay Mule Icon Processor</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        canvas { 
            border: 1px solid #ccc; 
            border-radius: 5px;
            margin: 10px 0;
        }
        .download-btn {
            background: #228B22;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin: 5px;
            display: block;
            width: 100%;
        }
        .download-btn:hover {
            background: #1e7b1e;
        }
        .instructions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .progress {
            background: #f0f0f0;
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
        }
        .progress-bar {
            background: #228B22;
            height: 20px;
            border-radius: 10px;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇿🇲 Pay Mule Icon Processor</h1>
        <p>Processing your Pay Mule wallet icons for all required sizes</p>
        
        <div class="progress">
            <div>Processing icons...</div>
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        
        <div class="icon-grid" id="iconGrid">
            <!-- Icons will be generated here -->
        </div>
        
        <div class="instructions">
            <h3>📋 Automated Icon Processing Complete!</h3>
            <p>All Pay Mule icons have been generated. Click the download buttons above to get each size.</p>
            <h4>Next Steps:</h4>
            <ol>
                <li><strong>Download all icons</strong> using the buttons above</li>
                <li><strong>Replace placeholder files</strong> in your project:
                    <ul>
                        <li>Android: Place in <code>android/app/src/main/res/mipmap-*/</code></li>
                        <li>iOS: Place in <code>ios/Runner/Assets.xcassets/AppIcon.appiconset/</code></li>
                    </ul>
                </li>
                <li><strong>Validate:</strong> Run <code>flutter test integration_test/icon_validation_test.dart</code></li>
                <li><strong>Test:</strong> Run <code>flutter clean && flutter run</code></li>
            </ol>
        </div>
        
        <button class="download-btn" onclick="downloadAllAsZip()" style="background: #DC143C; font-size: 16px; font-weight: bold;">
            📦 Download All Icons as ZIP
        </button>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js"></script>
    <script>
        // Icon configurations
        const androidSizes = [
            { name: 'mdpi', size: 48, folder: 'mipmap-mdpi' },
            { name: 'hdpi', size: 72, folder: 'mipmap-hdpi' },
            { name: 'xhdpi', size: 96, folder: 'mipmap-xhdpi' },
            { name: 'xxhdpi', size: 144, folder: 'mipmap-xxhdpi' },
            { name: 'xxxhdpi', size: 192, folder: 'mipmap-xxxhdpi' }
        ];
        
        const iosSizes = [
            { name: 'Icon-App-20x20@1x', size: 20 },
            { name: 'Icon-App-20x20@2x', size: 40 },
            { name: 'Icon-App-20x20@3x', size: 60 },
            { name: 'Icon-App-29x29@1x', size: 29 },
            { name: 'Icon-App-29x29@2x', size: 58 },
            { name: 'Icon-App-29x29@3x', size: 87 },
            { name: 'Icon-App-40x40@1x', size: 40 },
            { name: 'Icon-App-40x40@2x', size: 80 },
            { name: 'Icon-App-40x40@3x', size: 120 },
            { name: 'Icon-App-60x60@2x', size: 120 },
            { name: 'Icon-App-60x60@3x', size: 180 },
            { name: 'Icon-App-76x76@1x', size: 76 },
            { name: 'Icon-App-76x76@2x', size: 152 },
            { name: 'Icon-App-83.5x83.5@2x', size: 167 },
            { name: 'Icon-App-1024x1024@1x', size: 1024 }
        ];
        
        let generatedIcons = [];
        
        function drawPayMuleIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 512; // Base size 512
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Colors
            const black = '#000000';
            const white = '#FFFFFF';
            const lightGray = '#F5F5F5';
            const zambiaGreen = '#228B22';
            const zambiaOrange = '#FF8C00';
            const zambiaRed = '#DC143C';
            
            // Scale all coordinates
            function s(value) {
                return value * scale;
            }
            
            // Background (subtle gradient)
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#f8f8f8');
            gradient.addColorStop(1, '#e8e8e8');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Top card/stripe
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(s(80), s(60), s(352), s(80), s(40));
            ctx.fill();
            
            // White stripe in top card
            ctx.fillStyle = white;
            ctx.beginPath();
            ctx.roundRect(s(120), s(80), s(272), s(40), s(20));
            ctx.fill();
            
            // Main wallet body
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(s(80), s(160), s(320), s(240), s(40));
            ctx.fill();
            
            // Wallet interior (white/light area)
            ctx.fillStyle = lightGray;
            ctx.beginPath();
            ctx.roundRect(s(110), s(190), s(260), s(180), s(20));
            ctx.fill();
            
            // Card handle/tab on the right
            ctx.fillStyle = black;
            ctx.beginPath();
            ctx.roundRect(s(380), s(220), s(60), s(100), s(20));
            ctx.fill();
            
            // Card handle interior
            ctx.fillStyle = white;
            ctx.beginPath();
            ctx.roundRect(s(395), s(240), s(30), s(60), s(10));
            ctx.fill();
            
            // Zambian flag colors (small rectangles)
            const colorWidth = s(40);
            const colorHeight = s(12);
            const colorY = s(210);
            const startX = s(130);
            
            // Green
            ctx.fillStyle = zambiaGreen;
            ctx.beginPath();
            ctx.roundRect(startX, colorY, colorWidth, colorHeight, s(6));
            ctx.fill();
            
            // Orange
            ctx.fillStyle = zambiaOrange;
            ctx.beginPath();
            ctx.roundRect(startX + s(50), colorY, colorWidth, colorHeight, s(6));
            ctx.fill();
            
            // Red
            ctx.fillStyle = zambiaRed;
            ctx.beginPath();
            ctx.roundRect(startX + s(100), colorY, colorWidth, colorHeight, s(6));
            ctx.fill();
            
            // "PM" text
            ctx.fillStyle = black;
            ctx.font = `bold ${s(80)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('PM', s(240), s(300));
        }
        
        function generateIcon(config, platform) {
            const canvas = document.createElement('canvas');
            canvas.width = config.size;
            canvas.height = config.size;
            
            drawPayMuleIcon(canvas, config.size);
            
            return {
                canvas: canvas,
                name: config.name,
                size: config.size,
                platform: platform,
                folder: config.folder || ''
            };
        }
        
        function createIconDisplay(iconData) {
            const iconItem = document.createElement('div');
            iconItem.className = 'icon-item';
            
            // Create display canvas (smaller for UI)
            const displayCanvas = document.createElement('canvas');
            const displaySize = Math.min(iconData.size, 100);
            displayCanvas.width = displaySize;
            displayCanvas.height = displaySize;
            
            const displayCtx = displayCanvas.getContext('2d');
            displayCtx.drawImage(iconData.canvas, 0, 0, displaySize, displaySize);
            
            iconItem.innerHTML = `
                <h4>${iconData.name}</h4>
                <div>${iconData.size}x${iconData.size}px</div>
                <div style="margin: 10px 0;"></div>
                <button class="download-btn" onclick="downloadIcon('${iconData.name}', ${iconData.size})">
                    Download ${iconData.platform}
                </button>
            `;
            
            iconItem.insertBefore(displayCanvas, iconItem.children[2]);
            
            return iconItem;
        }
        
        function downloadIcon(name, size) {
            const iconData = generatedIcons.find(icon => icon.name === name && icon.size === size);
            if (iconData) {
                const link = document.createElement('a');
                link.download = `${name}.png`;
                link.href = iconData.canvas.toDataURL('image/png');
                link.click();
            }
        }
        
        async function downloadAllAsZip() {
            const zip = new JSZip();
            
            // Add Android icons
            const androidFolder = zip.folder('android');
            generatedIcons.filter(icon => icon.platform === 'Android').forEach(icon => {
                const folderName = icon.folder;
                const folder = androidFolder.folder(folderName);
                const dataUrl = icon.canvas.toDataURL('image/png');
                const base64Data = dataUrl.split(',')[1];
                folder.file('ic_launcher.png', base64Data, {base64: true});
            });
            
            // Add iOS icons
            const iosFolder = zip.folder('ios/AppIcon.appiconset');
            generatedIcons.filter(icon => icon.platform === 'iOS').forEach(icon => {
                const dataUrl = icon.canvas.toDataURL('image/png');
                const base64Data = dataUrl.split(',')[1];
                iosFolder.file(`${icon.name}.png`, base64Data, {base64: true});
            });
            
            // Generate and download zip
            const content = await zip.generateAsync({type: 'blob'});
            const link = document.createElement('a');
            link.download = 'pay_mule_icons.zip';
            link.href = URL.createObjectURL(content);
            link.click();
        }
        
        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }
        
        function generateAllIcons() {
            const iconGrid = document.getElementById('iconGrid');
            let progress = 0;
            const totalIcons = androidSizes.length + iosSizes.length;
            
            // Generate Android icons
            androidSizes.forEach((config, index) => {
                setTimeout(() => {
                    const iconData = generateIcon(config, 'Android');
                    generatedIcons.push(iconData);
                    iconGrid.appendChild(createIconDisplay(iconData));
                    
                    progress++;
                    updateProgress((progress / totalIcons) * 100);
                }, index * 100);
            });
            
            // Generate iOS icons
            iosSizes.forEach((config, index) => {
                setTimeout(() => {
                    const iconData = generateIcon(config, 'iOS');
                    generatedIcons.push(iconData);
                    iconGrid.appendChild(createIconDisplay(iconData));
                    
                    progress++;
                    updateProgress((progress / totalIcons) * 100);
                }, (androidSizes.length + index) * 100);
            });
        }
        
        // Start generating icons when page loads
        window.onload = function() {
            generateAllIcons();
        };
    </script>
</body>
</html>
