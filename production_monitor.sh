#!/bin/bash

# Production Payment Success Rate Monitor
# Based on your exact specification

source ./rollback_functions.sh

# Your exact implementation in production format
monitor_and_rollback() {
    local payment_success_rate
    payment_success_rate=$(get_payment_success_rate)
    
    echo "[$(date)] Checking payment success rate: ${payment_success_rate}%"
    
    # Convert to integer for comparison (multiply by 10 to handle one decimal place)
    local current_rate_int=$(echo "$payment_success_rate" | sed 's/\.//' | sed 's/^0*//')
    current_rate_int=${current_rate_int:-0}

    if [ "$current_rate_int" -lt 999 ]; then  # < 99.9
        echo "[$(date)] CRITICAL: Payment success rate below 99.9% - initiating rollback"
        
        revert_to_commit PAYMULE_STABLE_v3
        send_sms_alert "Deployment failed - Reverted"
        
        echo "[$(date)] Emergency rollback completed"
        return 1
    else
        echo "[$(date)] Payment success rate healthy: ${payment_success_rate}%"
        return 0
    fi
}

# Run monitoring
monitor_and_rollback
