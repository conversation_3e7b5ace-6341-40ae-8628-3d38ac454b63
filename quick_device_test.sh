#!/bin/bash

# quick_device_test.sh - Quick device connectivity and APK installation test
# Simple script for immediate testing

set -e

APK_FILE="paymule_compatible_v1.1.apk"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if APK exists
if [[ ! -f "$APK_FILE" ]]; then
    print_error "APK file not found: $APK_FILE"
    print_status "Available APK files:"
    ls -la *.apk 2>/dev/null || print_status "No APK files found"
    exit 1
fi

# Check ADB
if ! command -v adb &> /dev/null; then
    print_error "ADB not found. Please install Android SDK platform-tools."
    exit 1
fi

print_status "🔍 Checking connected devices..."
devices=$(adb devices | grep -v "List of devices" | grep -v "^$" | wc -l)

if [[ $devices -eq 0 ]]; then
    print_error "❌ No devices connected"
    print_status "Please:"
    print_status "1. Connect Android device via USB"
    print_status "2. Enable USB debugging in Developer Options"
    print_status "3. Accept USB debugging prompt on device"
    exit 1
fi

print_success "✅ Device connected"

# Get device info
device_model=$(adb shell getprop ro.product.model 2>/dev/null | tr -d '\r' || echo "Unknown")
android_version=$(adb shell getprop ro.build.version.release 2>/dev/null | tr -d '\r' || echo "Unknown")

print_status "📱 Device: $device_model (Android $android_version)"

# Quick installation test
print_status "🚀 Testing APK installation..."

if adb install -r -g -t "$APK_FILE" 2>/dev/null; then
    print_success "✅ Installation successful!"
    print_status "APK is compatible with this device"
else
    print_error "❌ Installation failed"
    print_status "Check device compatibility or APK issues"
fi

print_status "Done! For detailed testing, use:"
print_status "  ./verify_real_device.sh --apk=$APK_FILE"
