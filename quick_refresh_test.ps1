# Quick Refresh Testing Script for Zambia Pay Mobile Money
# Simplified version for immediate validation
# Usage: .\quick_refresh_test.ps1 -NetworkProfiles "2g,3g,4g,offline" -Iterations 5

param(
    [string]$NetworkProfiles = "2g,3g,4g,offline",
    [int]$Iterations = 5,
    [switch]$ValidateDataConsistency,
    [switch]$Verbose
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Network configurations
$NetworkConfigs = @{
    "2g" = @{ speed = 0.1; latency = 1000; packetLoss = 5 }
    "3g" = @{ speed = 1.0; latency = 300; packetLoss = 2 }
    "4g" = @{ speed = 10.0; latency = 100; packetLoss = 1 }
    "offline" = @{ speed = 0; latency = 9999; packetLoss = 100 }
}

# Test results
$TestResults = @{}
$TotalTests = 0
$PassedTests = 0
$FailedTests = 0

# Logging function
function Write-Log {
    param([string]$Level, [string]$Message)
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "INFO" { Write-Host "[$Level] $timestamp - $Message" -ForegroundColor $Blue }
        "SUCCESS" { Write-Host "[$Level] $timestamp - $Message" -ForegroundColor $Green }
        "WARNING" { Write-Host "[$Level] $timestamp - $Message" -ForegroundColor $Yellow }
        "ERROR" { Write-Host "[$Level] $timestamp - $Message" -ForegroundColor $Red }
    }
}

# Create simple test
function New-SimpleTest {
    param([string]$NetworkProfile, [int]$Iteration)
    
    $config = $NetworkConfigs[$NetworkProfile]
    $testFile = "temp_test_${NetworkProfile}_${Iteration}.dart"
    
    $testContent = @"
import 'dart:async';
import 'dart:math';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('$NetworkProfile Network Test $Iteration', () {
    test('should simulate $NetworkProfile conditions', () async {
      // Network simulation
      final speed = $($config.speed);
      final latency = $($config.latency);
      final packetLoss = $($config.packetLoss);
      
      var successCount = 0;
      var failureCount = 0;
      
      // Simulate 5 network requests
      for (int i = 0; i < 5; i++) {
        try {
          // Simulate network delay
          await Future.delayed(Duration(milliseconds: latency ~/ 10));
          
          // Simulate packet loss
          if (Random().nextInt(100) < packetLoss) {
            throw Exception('Packet lost');
          }
          
          successCount++;
          print('Request \$i succeeded on $NetworkProfile');
          
        } catch (e) {
          failureCount++;
          print('Request \$i failed on $NetworkProfile - \$e');
        }
      }
      
      // Validate based on network type
      if (speed > 0) {
        expect(successCount, greaterThan(0), 
               reason: 'Should have some success on $NetworkProfile');
      } else {
        expect(failureCount, equals(5), 
               reason: 'Should fail all requests when offline');
      }
      
      print('$NetworkProfile test completed - Success: \$successCount, Failures: \$failureCount');
    });
  });
}
"@
    
    Set-Content -Path $testFile -Value $testContent
    return $testFile
}

# Run single test
function Invoke-SimpleTest {
    param([string]$NetworkProfile, [int]$Iteration)
    
    Write-Log "INFO" "Running $NetworkProfile test iteration $Iteration"
    
    $testFile = New-SimpleTest -NetworkProfile $NetworkProfile -Iteration $Iteration
    
    try {
        $output = flutter test $testFile --reporter=expanded 2>&1
        $success = $LASTEXITCODE -eq 0
        
        if ($Verbose) {
            Write-Host $output
        }
        
        # Clean up
        Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
        
        # Update results
        $script:TotalTests++
        if ($success) {
            $script:PassedTests++
            $TestResults["${NetworkProfile}_${Iteration}"] = "PASSED"
            Write-Log "SUCCESS" "$NetworkProfile iteration $Iteration passed"
            return $true
        } else {
            $script:FailedTests++
            $TestResults["${NetworkProfile}_${Iteration}"] = "FAILED"
            Write-Log "ERROR" "$NetworkProfile iteration $Iteration failed"
            return $false
        }
    }
    catch {
        Write-Log "ERROR" "Exception in $NetworkProfile iteration $Iteration`: $_"
        Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
        $script:TotalTests++
        $script:FailedTests++
        return $false
    }
}

# Test data consistency
function Test-DataConsistency {
    if (-not $ValidateDataConsistency) {
        return $true
    }
    
    Write-Log "INFO" "Testing data consistency..."
    
    $consistencyTest = "temp_consistency_test.dart"
    $testContent = @"
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Data Consistency Test', () async {
    // Simulate data usage tracking
    var totalUsage = 0.0;
    final monthlyLimit = 2.0; // 2MB
    
    // Simulate multiple operations
    for (int i = 0; i < 10; i++) {
      final operationUsage = 0.01; // 10KB per operation
      totalUsage += operationUsage;
      
      // Check if we're approaching limit
      if (totalUsage > monthlyLimit * 0.9) {
        print('Warning: Approaching data limit');
        break;
      }
    }
    
    // Validate usage is within limits
    expect(totalUsage, lessThanOrEqualTo(monthlyLimit), 
           reason: 'Should not exceed monthly limit');
    
    expect(totalUsage, greaterThan(0), 
           reason: 'Should track some usage');
    
    print('Data consistency validated - Usage: \${totalUsage.toStringAsFixed(3)}MB');
  });
}
"@
    
    Set-Content -Path $consistencyTest -Value $testContent
    
    try {
        flutter test $consistencyTest --reporter=expanded | Out-Null
        $success = $LASTEXITCODE -eq 0
        
        Remove-Item -Path $consistencyTest -Force -ErrorAction SilentlyContinue
        
        if ($success) {
            Write-Log "SUCCESS" "Data consistency test passed"
            return $true
        } else {
            Write-Log "ERROR" "Data consistency test failed"
            return $false
        }
    }
    catch {
        Write-Log "ERROR" "Data consistency test exception: $_"
        Remove-Item -Path $consistencyTest -Force -ErrorAction SilentlyContinue
        return $false
    }
}

# Main execution
function Main {
    Write-Log "INFO" "Starting Quick Refresh Test Suite"
    Write-Log "INFO" "Profiles: $NetworkProfiles, Iterations: $Iterations"
    
    # Check Flutter
    try {
        $flutterVersion = flutter --version 2>$null | Select-Object -First 1
        Write-Log "INFO" "Flutter: $flutterVersion"
    }
    catch {
        Write-Log "ERROR" "Flutter not found"
        exit 1
    }
    
    # Parse profiles
    $profiles = $NetworkProfiles -split ','
    
    # Validate profiles
    foreach ($profile in $profiles) {
        if (-not $NetworkConfigs.ContainsKey($profile)) {
            Write-Log "ERROR" "Unknown profile: $profile"
            exit 1
        }
    }
    
    # Run tests
    foreach ($profile in $profiles) {
        Write-Log "INFO" "Testing $profile network ($Iterations iterations)"
        
        for ($i = 1; $i -le $Iterations; $i++) {
            Invoke-SimpleTest -NetworkProfile $profile -Iteration $i
        }
    }
    
    # Test data consistency
    $consistencyPassed = Test-DataConsistency
    
    # Generate summary
    $successRate = if ($TotalTests -gt 0) { [math]::Round(($PassedTests * 100) / $TotalTests, 1) } else { 0 }
    
    Write-Log "INFO" "=== QUICK TEST SUMMARY ==="
    Write-Log "INFO" "Total Tests: $TotalTests"
    Write-Log "SUCCESS" "Passed: $PassedTests"
    if ($FailedTests -gt 0) {
        Write-Log "ERROR" "Failed: $FailedTests"
    } else {
        Write-Log "SUCCESS" "Failed: $FailedTests"
    }
    Write-Log "INFO" "Success Rate: $successRate%"
    
    if ($ValidateDataConsistency) {
        if ($consistencyPassed) {
            Write-Log "SUCCESS" "Data Consistency: PASSED"
        } else {
            Write-Log "ERROR" "Data Consistency: FAILED"
        }
    }
    
    # Profile breakdown
    foreach ($profile in $profiles) {
        $profilePassed = 0
        $profileTotal = 0
        
        foreach ($key in $TestResults.Keys) {
            if ($key -like "${profile}_*") {
                $profileTotal++
                if ($TestResults[$key] -eq "PASSED") {
                    $profilePassed++
                }
            }
        }
        
        $profileRate = if ($profileTotal -gt 0) { [math]::Round(($profilePassed * 100) / $profileTotal, 1) } else { 0 }
        Write-Log "INFO" "$profile`: $profilePassed/$profileTotal ($profileRate%)"
    }
    
    # Final result
    if ($successRate -ge 80 -and ($consistencyPassed -or -not $ValidateDataConsistency)) {
        Write-Log "SUCCESS" "QUICK TESTS PASSED - Refresh system is working!"
        exit 0
    } else {
        Write-Log "ERROR" "QUICK TESTS FAILED - Issues detected"
        exit 1
    }
}

# Run main
Main
