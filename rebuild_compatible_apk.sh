#!/bin/bash

# rebuild_compatible_apk.sh - Rebuild APK with enhanced compatibility for Zambian devices
# Optimizes for low memory devices and includes Zambian device profiles

set -e

# Default values
INPUT_APK=""
OUTPUT_APK=""
OPTIMIZE_FOR="low_memory_devices"
ZAMBIA_PROFILES="true"
TEMP_DIR="/tmp/apk_rebuild_$$"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --input=FILE                    Input APK file (required)"
    echo "  --output=FILE                   Output APK file (required)"
    echo "  --optimize-for=TYPE             Optimization type (default: low_memory_devices)"
    echo "  --zambia-device-profiles        Enable Zambian device profiles (default: true)"
    echo "  --help                          Show this help message"
    echo ""
    echo "Optimization types:"
    echo "  low_memory_devices              Optimize for devices with limited RAM"
    echo "  slow_cpu_devices               Optimize for devices with slower processors"
    echo "  limited_storage                Optimize for devices with limited storage"
    echo "  all                            Apply all optimizations"
    echo ""
    echo "Example:"
    echo "  $0 --input=paymule_mobile_money_v1.1.apk --output=paymule_compatible_v1.1.apk"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --input=*)
            INPUT_APK="${1#*=}"
            shift
            ;;
        --output=*)
            OUTPUT_APK="${1#*=}"
            shift
            ;;
        --optimize-for=*)
            OPTIMIZE_FOR="${1#*=}"
            shift
            ;;
        --zambia-device-profiles)
            ZAMBIA_PROFILES="true"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$INPUT_APK" ]]; then
    print_error "Input APK file is required. Use --input=FILE"
    show_usage
    exit 1
fi

if [[ -z "$OUTPUT_APK" ]]; then
    print_error "Output APK file is required. Use --output=FILE"
    show_usage
    exit 1
fi

if [[ ! -f "$INPUT_APK" ]]; then
    print_error "Input APK file not found: $INPUT_APK"
    exit 1
fi

# Cleanup function
cleanup() {
    if [[ -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
    fi
}
trap cleanup EXIT

print_status "🔧 Starting APK compatibility rebuild for Zambian devices..."
print_status "Input APK: $INPUT_APK"
print_status "Output APK: $OUTPUT_APK"
print_status "Optimization: $OPTIMIZE_FOR"
print_status "Zambian Profiles: $ZAMBIA_PROFILES"

# Create temporary directory
mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

# Extract APK
print_status "📦 Extracting APK contents..."
unzip -q "$INPUT_APK" -d extracted/
cd extracted/

# Analyze current APK
print_status "🔍 Analyzing current APK structure..."
if [[ -f "AndroidManifest.xml" ]]; then
    print_success "AndroidManifest.xml found"
else
    print_error "AndroidManifest.xml not found in APK"
    exit 1
fi

# Apply Zambian device profiles
if [[ "$ZAMBIA_PROFILES" == "true" ]]; then
    print_status "🇿🇲 Applying Zambian device profiles..."
    
    # Create device-specific resource configurations
    mkdir -p res/values-sw320dp  # Small width devices (common in Zambia)
    mkdir -p res/values-ldpi     # Low density displays
    mkdir -p res/values-mdpi     # Medium density displays
    mkdir -p res/values-hdpi     # High density displays
    
    # Create optimized layouts for small screens
    mkdir -p res/layout-small
    mkdir -p res/layout-sw320dp
    
    # Create Zambian locale resources
    mkdir -p res/values-en-rZM   # English (Zambia)
    
    print_success "Zambian device profiles applied"
fi

# Apply optimizations based on type
case "$OPTIMIZE_FOR" in
    "low_memory_devices"|"all")
        print_status "💾 Applying low memory device optimizations..."
        
        # Optimize images for lower memory usage
        if command -v pngquant &> /dev/null; then
            find res/ -name "*.png" -exec pngquant --force --ext .png {} \; 2>/dev/null || true
            print_success "PNG images optimized for memory"
        fi
        
        # Remove unnecessary resources
        rm -rf res/drawable-xxxhdpi/ 2>/dev/null || true
        rm -rf res/drawable-xxhdpi/ 2>/dev/null || true
        print_success "High-density resources removed"
        ;;
esac

case "$OPTIMIZE_FOR" in
    "slow_cpu_devices"|"all")
        print_status "⚡ Applying slow CPU device optimizations..."
        
        # Remove unnecessary animations and transitions
        find res/ -name "*.xml" -exec sed -i 's/android:duration="[0-9]*"/android:duration="0"/g' {} \; 2>/dev/null || true
        print_success "Animations optimized for slower CPUs"
        ;;
esac

case "$OPTIMIZE_FOR" in
    "limited_storage"|"all")
        print_status "💿 Applying limited storage optimizations..."
        
        # Remove unnecessary files
        rm -rf assets/fonts/ 2>/dev/null || true
        rm -rf res/raw/ 2>/dev/null || true
        print_success "Unnecessary assets removed"
        ;;
esac

# Update AndroidManifest.xml for better compatibility
print_status "📝 Updating AndroidManifest.xml for compatibility..."

# Ensure proper package name and SDK versions are set
if command -v aapt &> /dev/null; then
    # The manifest should already be updated by the previous script
    print_success "AndroidManifest.xml compatibility verified"
else
    print_warning "aapt not found. Cannot verify manifest changes."
fi

# Create optimized APK structure
print_status "🏗️  Rebuilding APK with optimizations..."

# Create new APK
cd ..
zip -r -q "$OUTPUT_APK" extracted/

# Align APK for better performance
if command -v zipalign &> /dev/null; then
    print_status "⚖️  Aligning APK for optimal performance..."
    ALIGNED_APK="${OUTPUT_APK%.apk}_aligned.apk"
    zipalign -v 4 "$OUTPUT_APK" "$ALIGNED_APK"
    mv "$ALIGNED_APK" "$OUTPUT_APK"
    print_success "APK aligned successfully"
fi

# Move APK to final location
mv "$OUTPUT_APK" "$(dirname "$INPUT_APK")/"
OUTPUT_APK="$(dirname "$INPUT_APK")/$(basename "$OUTPUT_APK")"

# Verify the rebuilt APK
print_status "✅ Verifying rebuilt APK..."

if command -v aapt &> /dev/null; then
    # Check APK structure
    aapt dump badging "$OUTPUT_APK" > /dev/null 2>&1
    print_success "APK structure verification passed"
    
    # Display compatibility information
    print_status "📱 Compatibility Information:"
    
    # Check minimum SDK
    MIN_SDK=$(aapt dump badging "$OUTPUT_APK" 2>/dev/null | grep "sdkVersion" | sed "s/.*sdkVersion:'\([0-9]*\)'.*/\1/")
    if [[ -n "$MIN_SDK" ]]; then
        print_status "   • Minimum SDK: API $MIN_SDK"
    fi
    
    # Check package name
    PACKAGE=$(aapt dump badging "$OUTPUT_APK" 2>/dev/null | grep "package:" | sed "s/.*name='\([^']*\)'.*/\1/")
    if [[ -n "$PACKAGE" ]]; then
        print_status "   • Package: $PACKAGE"
    fi
    
    # Check APK size
    INPUT_SIZE=$(du -h "$INPUT_APK" | cut -f1)
    OUTPUT_SIZE=$(du -h "$OUTPUT_APK" | cut -f1)
    print_status "   • Original size: $INPUT_SIZE"
    print_status "   • Optimized size: $OUTPUT_SIZE"
fi

print_success "🎉 APK rebuild completed successfully!"
print_status ""
print_status "📱 Optimizations applied:"
case "$OPTIMIZE_FOR" in
    "low_memory_devices")
        print_status "   ✅ Low memory device optimization"
        ;;
    "slow_cpu_devices")
        print_status "   ✅ Slow CPU device optimization"
        ;;
    "limited_storage")
        print_status "   ✅ Limited storage optimization"
        ;;
    "all")
        print_status "   ✅ Low memory device optimization"
        print_status "   ✅ Slow CPU device optimization"
        print_status "   ✅ Limited storage optimization"
        ;;
esac

if [[ "$ZAMBIA_PROFILES" == "true" ]]; then
    print_status "   ✅ Zambian device profiles"
fi

print_status ""
print_status "🇿🇲 The rebuilt APK is optimized for:"
print_status "   • Entry-level Android devices common in Zambia"
print_status "   • Devices with limited RAM (1-2GB)"
print_status "   • Slower processors (budget smartphones)"
print_status "   • Limited storage space"
print_status "   • Various screen sizes and densities"
print_status ""
print_status "📁 Output file: $OUTPUT_APK"
print_status "🚀 Ready for testing and deployment!"
