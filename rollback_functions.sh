#!/bin/bash

# Rollback Functions for Zambia Pay
# Implements automatic rollback based on payment success rate monitoring
# Source this file to use the rollback functions in your deployment scripts

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PAYMENT_SUCCESS_THRESHOLD=${PAYMENT_SUCCESS_THRESHOLD:-99.9}
STABLE_COMMIT="PAYMULE_STABLE_v3"
ALERT_PHONE_NUMBERS=("260961234567" "260971234567" "260951234567")

print_rollback_info() {
    echo -e "${BLUE}[ROLLBACK]${NC} $1"
}

print_rollback_success() {
    echo -e "${GREEN}[ROLLBACK]${NC} $1"
}

print_rollback_error() {
    echo -e "${RED}[R<PERSON><PERSON><PERSON>CK]${NC} $1"
}

print_rollback_warning() {
    echo -e "${YELLOW}[ROLLBACK]${NC} $1"
}

# Get current payment success rate
get_payment_success_rate() {
    # In production, this would query your actual payment database
    # Example SQL queries for different databases:
    
    # SQLite example:
    # local total=$(sqlite3 payments.db "SELECT COUNT(*) FROM transactions WHERE created_at > datetime('now', '-5 minutes') AND status IN ('COMPLETED', 'FAILED')")
    # local successful=$(sqlite3 payments.db "SELECT COUNT(*) FROM transactions WHERE created_at > datetime('now', '-5 minutes') AND status = 'COMPLETED'")
    
    # PostgreSQL example:
    # local total=$(psql -t -c "SELECT COUNT(*) FROM transactions WHERE created_at > NOW() - INTERVAL '5 minutes' AND status IN ('COMPLETED', 'FAILED')")
    # local successful=$(psql -t -c "SELECT COUNT(*) FROM transactions WHERE created_at > NOW() - INTERVAL '5 minutes' AND status = 'COMPLETED'")
    
    # For demo purposes, simulate payment data
    local total_payments=1000
    local successful_payments=995  # 99.5% success rate for testing
    
    if [ "$total_payments" -eq 0 ]; then
        echo "100.0"  # Default to 100% if no data
        return
    fi
    
    # Calculate success rate using bash arithmetic (avoid bc dependency)
    local success_rate_int=$(( (successful_payments * 10000) / total_payments ))
    local whole_part=$(( success_rate_int / 100 ))
    local decimal_part=$(( success_rate_int % 100 ))
    printf "%d.%02d" "$whole_part" "$decimal_part"
}

# Check if payment success rate is below threshold
check_payment_success_rate() {
    local current_rate
    current_rate=$(get_payment_success_rate)
    
    print_rollback_info "Current payment success rate: ${current_rate}%"
    print_rollback_info "Threshold: ${PAYMENT_SUCCESS_THRESHOLD}%"
    
    # Convert to integer comparison (multiply by 100 to handle decimals)
    local current_rate_int=$(echo "$current_rate" | awk '{printf "%.0f", $1 * 100}')
    local threshold_int=$(echo "$PAYMENT_SUCCESS_THRESHOLD" | awk '{printf "%.0f", $1 * 100}')

    local below_threshold=0
    if [ "$current_rate_int" -lt "$threshold_int" ]; then
        below_threshold=1
    fi
    
    if [ "$below_threshold" -eq 1 ]; then
        print_rollback_error "Payment success rate ${current_rate}% is below threshold ${PAYMENT_SUCCESS_THRESHOLD}%"
        return 1  # Below threshold
    else
        print_rollback_success "Payment success rate ${current_rate}% is above threshold ${PAYMENT_SUCCESS_THRESHOLD}%"
        return 0  # Above threshold
    fi
}

# Revert to specific commit
revert_to_commit() {
    local commit="$1"
    
    print_rollback_error "🚨 INITIATING EMERGENCY ROLLBACK 🚨"
    print_rollback_info "Reverting to commit: $commit"
    
    # Validate git repository
    if [ ! -d ".git" ]; then
        print_rollback_error "Not in a git repository"
        return 1
    fi
    
    # Check if commit exists
    if ! git rev-parse --verify "$commit" >/dev/null 2>&1; then
        print_rollback_error "Commit $commit not found"
        return 1
    fi
    
    # Create backup branch
    local backup_branch="backup_$(date +%Y%m%d_%H%M%S)"
    if git checkout -b "$backup_branch" >/dev/null 2>&1; then
        print_rollback_info "Current state backed up to branch: $backup_branch"
    else
        print_rollback_warning "Could not create backup branch"
    fi
    
    # Perform rollback
    if git checkout main >/dev/null 2>&1 && git reset --hard "$commit" >/dev/null 2>&1; then
        print_rollback_success "Successfully reverted to commit: $commit"
        
        # Log the rollback
        echo "$(date): Emergency rollback to $commit" >> rollback.log
        return 0
    else
        print_rollback_error "Failed to revert to commit: $commit"
        return 1
    fi
}

# Send SMS alert using Zambian mobile networks
send_sms_alert() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local full_message="[ZAMBIA PAY] $message - $timestamp"
    
    print_rollback_info "📱 Sending SMS alerts..."
    
    local sms_sent=false
    
    for phone in "${ALERT_PHONE_NUMBERS[@]}"; do
        if send_sms_via_zambian_networks "$phone" "$full_message"; then
            print_rollback_success "SMS sent to $phone"
            sms_sent=true
        else
            print_rollback_error "Failed to send SMS to $phone"
        fi
    done
    
    if [ "$sms_sent" = true ]; then
        return 0
    else
        return 1
    fi
}

# Send SMS via Zambian mobile networks (MTN, Airtel, Zamtel)
send_sms_via_zambian_networks() {
    local phone="$1"
    local message="$2"
    
    # Determine network based on phone number prefix
    local network=""
    case "${phone:3:2}" in
        "96"|"76"|"86")
            network="MTN"
            ;;
        "97"|"77"|"87")
            network="AIRTEL"
            ;;
        "95"|"75"|"85")
            network="ZAMTEL"
            ;;
        *)
            network="UNKNOWN"
            ;;
    esac
    
    print_rollback_info "Sending SMS to $phone via $network network"
    
    case "$network" in
        "MTN")
            send_sms_mtn "$phone" "$message"
            ;;
        "AIRTEL")
            send_sms_airtel "$phone" "$message"
            ;;
        "ZAMTEL")
            send_sms_zamtel "$phone" "$message"
            ;;
        *)
            print_rollback_warning "Unknown network for $phone, using default SMS service"
            send_sms_default "$phone" "$message"
            ;;
    esac
}

# MTN SMS API integration
send_sms_mtn() {
    local phone="$1"
    local message="$2"
    
    # In production, use actual MTN SMS API
    # curl -X POST "https://api.mtn.zm/sms/v1/send" \
    #   -H "Authorization: Bearer $MTN_SMS_TOKEN" \
    #   -H "Content-Type: application/json" \
    #   -d "{\"to\":\"$phone\",\"message\":\"$message\"}"
    
    # Simulate for demo
    print_rollback_info "MTN SMS API: $message -> $phone"
    return 0
}

# Airtel SMS API integration
send_sms_airtel() {
    local phone="$1"
    local message="$2"
    
    # In production, use actual Airtel SMS API
    # curl -X POST "https://api.airtel.zm/sms/v1/send" \
    #   -H "Authorization: Bearer $AIRTEL_SMS_TOKEN" \
    #   -H "Content-Type: application/json" \
    #   -d "{\"to\":\"$phone\",\"message\":\"$message\"}"
    
    # Simulate for demo
    print_rollback_info "Airtel SMS API: $message -> $phone"
    return 0
}

# Zamtel SMS API integration
send_sms_zamtel() {
    local phone="$1"
    local message="$2"
    
    # In production, use actual Zamtel SMS API
    # curl -X POST "https://api.zamtel.zm/sms/v1/send" \
    #   -H "Authorization: Bearer $ZAMTEL_SMS_TOKEN" \
    #   -H "Content-Type: application/json" \
    #   -d "{\"to\":\"$phone\",\"message\":\"$message\"}"
    
    # Simulate for demo
    print_rollback_info "Zamtel SMS API: $message -> $phone"
    return 0
}

# Default SMS service
send_sms_default() {
    local phone="$1"
    local message="$2"
    
    print_rollback_info "Default SMS service: $message -> $phone"
    return 0
}

# Your exact implementation as specified
payment_success_monitoring() {
    local payment_success_rate
    payment_success_rate=$(get_payment_success_rate)
    
    # Convert to comparison format (remove decimal point for integer comparison)
    local rate_int=$(echo "$payment_success_rate * 10" | bc -l | cut -d. -f1)
    local threshold_int=$(echo "$PAYMENT_SUCCESS_THRESHOLD * 10" | bc -l | cut -d. -f1)
    
    if [ "$rate_int" -lt "$threshold_int" ]; then
        revert_to_commit "$STABLE_COMMIT"
        send_sms_alert "Deployment failed - Reverted"
        return 1
    else
        return 0
    fi
}

# Enhanced version with logging and error handling
enhanced_payment_monitoring() {
    print_rollback_info "Starting payment success rate monitoring..."
    
    local payment_success_rate
    payment_success_rate=$(get_payment_success_rate)
    
    print_rollback_info "Current payment success rate: ${payment_success_rate}%"
    print_rollback_info "Threshold: ${PAYMENT_SUCCESS_THRESHOLD}%"
    
    # Use integer comparison (multiply by 100 to handle decimals)
    local current_rate_int=$(echo "$payment_success_rate" | awk '{printf "%.0f", $1 * 100}')
    local threshold_int=$(echo "$PAYMENT_SUCCESS_THRESHOLD" | awk '{printf "%.0f", $1 * 100}')

    local below_threshold=0
    if [ "$current_rate_int" -lt "$threshold_int" ]; then
        below_threshold=1
    fi
    
    if [ "$below_threshold" -eq 1 ]; then
        print_rollback_error "🚨 Payment success rate below threshold!"
        
        if revert_to_commit "$STABLE_COMMIT"; then
            if send_sms_alert "Deployment failed - Reverted to $STABLE_COMMIT (Rate: ${payment_success_rate}%)"; then
                print_rollback_success "Emergency rollback completed successfully"
            else
                print_rollback_warning "Rollback completed but SMS alerts failed"
            fi
        else
            print_rollback_error "Rollback failed!"
            send_sms_alert "CRITICAL: Rollback failed - Manual intervention required"
            return 2
        fi
        return 1
    else
        print_rollback_success "Payment success rate is healthy"
        return 0
    fi
}

# Continuous monitoring function
start_continuous_monitoring() {
    local interval=${1:-60}  # Default 60 seconds
    
    print_rollback_info "Starting continuous payment monitoring (interval: ${interval}s)"
    
    while true; do
        print_rollback_info "--- Monitoring check at $(date) ---"
        
        if ! enhanced_payment_monitoring; then
            print_rollback_error "Monitoring triggered rollback - stopping continuous monitoring"
            break
        fi
        
        print_rollback_info "Next check in ${interval} seconds..."
        sleep "$interval"
    done
}

# Export functions for use in other scripts
export -f get_payment_success_rate
export -f check_payment_success_rate
export -f revert_to_commit
export -f send_sms_alert
export -f payment_success_monitoring
export -f enhanced_payment_monitoring
export -f start_continuous_monitoring

# Show usage information
show_rollback_help() {
    echo "Zambia Pay Rollback Functions"
    echo "============================="
    echo ""
    echo "Available functions:"
    echo "  payment_success_monitoring()     - Your exact implementation"
    echo "  enhanced_payment_monitoring()    - Enhanced version with logging"
    echo "  start_continuous_monitoring()    - Continuous monitoring mode"
    echo "  get_payment_success_rate()       - Get current success rate"
    echo "  check_payment_success_rate()     - Check if rate is above threshold"
    echo "  revert_to_commit()               - Revert to specific commit"
    echo "  send_sms_alert()                 - Send SMS alerts"
    echo ""
    echo "Usage:"
    echo "  source rollback_functions.sh"
    echo "  payment_success_monitoring"
    echo ""
    echo "Environment Variables:"
    echo "  PAYMENT_SUCCESS_THRESHOLD        - Success rate threshold (default: 99.9)"
    echo "  STABLE_COMMIT                    - Stable commit for rollback (default: PAYMULE_STABLE_v3)"
}

# If script is called with --help, show usage
if [[ "${1}" == "--help" ]] || [[ "${1}" == "-h" ]]; then
    show_rollback_help
fi
