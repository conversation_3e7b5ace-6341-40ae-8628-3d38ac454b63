#!/bin/bash

# 🇿🇲 PAY MULE ZAMBIA - PRODUCTION CHECKLIST RUNNER
# 
# Shell wrapper for the Zambian production checklist
# Provides easy command-line interface for production validation
# 
# USAGE:
# ./run_zambia_prod_checklist.sh \
#   --required-tests="mtn_live_tx,airtel_balance_check,zesco_payment" \
#   --ussd-flow="*211*1*26097XXXXXX*5.0#" \
#   --agent-verification="Chipata Market" \
#   --failure-mode="rainy_season_sim" \
#   --report-format=pdf

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script information
SCRIPT_VERSION="1.0.0"
SCRIPT_NAME="Zambia Production Checklist Runner"

# Default values
DEFAULT_TESTS="mtn_live_tx,airtel_balance_check,zesco_payment"
DEFAULT_USSD=""
DEFAULT_AGENT=""
DEFAULT_FAILURE_MODE="normal"
DEFAULT_REPORT_FORMAT="console"

# Function to print colored output
print_header() {
    echo -e "${CYAN}🇿🇲 $SCRIPT_NAME v$SCRIPT_VERSION${NC}"
    echo -e "${CYAN}================================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --required-tests=TESTS     Comma-separated list of required tests"
    echo "                            Default: $DEFAULT_TESTS"
    echo "  --ussd-flow=FLOW          USSD flow to verify (e.g., *211*1*26097XXXXXX*5.0#)"
    echo "  --agent-verification=NAME Agent location to verify (e.g., 'Chipata Market')"
    echo "  --failure-mode=MODE       Failure mode to simulate"
    echo "                            Options: normal, rainy_season_sim, network_outage, power_failure, high_load"
    echo "                            Default: $DEFAULT_FAILURE_MODE"
    echo "  --report-format=FORMAT    Report output format"
    echo "                            Options: console, pdf, json, html"
    echo "                            Default: $DEFAULT_REPORT_FORMAT"
    echo "  --help                    Show this help message"
    echo ""
    echo "Available Tests:"
    echo "  mtn_live_tx              Test MTN live transaction"
    echo "  airtel_balance_check     Test Airtel balance inquiry"
    echo "  zesco_payment           Test ZESCO utility payment"
    echo "  nwsc_payment            Test NWSC water payment"
    echo "  agent_discovery         Test agent discovery system"
    echo "  offline_sync            Test offline synchronization"
    echo "  security_alerts         Test security alert system"
    echo ""
    echo "Examples:"
    echo "  # Basic production checklist"
    echo "  $0"
    echo ""
    echo "  # Full production validation with PDF report"
    echo "  $0 \\"
    echo "    --required-tests=\"mtn_live_tx,airtel_balance_check,zesco_payment\" \\"
    echo "    --ussd-flow=\"*211*1*26097XXXXXX*5.0#\" \\"
    echo "    --agent-verification=\"Chipata Market\" \\"
    echo "    --failure-mode=\"rainy_season_sim\" \\"
    echo "    --report-format=pdf"
    echo ""
    echo "  # Test specific components"
    echo "  $0 --required-tests=\"zesco_payment,nwsc_payment\" --report-format=json"
    echo ""
    echo "  # Simulate rainy season conditions"
    echo "  $0 --failure-mode=\"rainy_season_sim\" --report-format=html"
}

# Function to validate Dart installation
check_dart_installation() {
    if ! command -v dart &> /dev/null; then
        print_error "Dart SDK not found. Please install Dart SDK to run the checklist."
        echo "Visit: https://dart.dev/get-dart"
        exit 1
    fi
    
    print_info "Dart SDK found: $(dart --version 2>&1 | head -n1)"
}

# Function to validate script file
check_script_file() {
    local script_file="lib/scripts/run_zambia_prod_checklist.dart"
    
    if [ ! -f "$script_file" ]; then
        print_error "Checklist script not found: $script_file"
        print_info "Please ensure you're running this from the project root directory."
        exit 1
    fi
    
    print_info "Checklist script found: $script_file"
}

# Function to validate arguments
validate_arguments() {
    local tests="$1"
    local failure_mode="$2"
    local report_format="$3"
    
    # Validate failure mode
    case "$failure_mode" in
        normal|rainy_season_sim|rainy_season|network_outage|power_failure|high_load)
            ;;
        *)
            print_error "Invalid failure mode: $failure_mode"
            echo "Valid options: normal, rainy_season_sim, network_outage, power_failure, high_load"
            exit 1
            ;;
    esac
    
    # Validate report format
    case "$report_format" in
        console|pdf|json|html)
            ;;
        *)
            print_error "Invalid report format: $report_format"
            echo "Valid options: console, pdf, json, html"
            exit 1
            ;;
    esac
    
    print_info "Arguments validated successfully"
}

# Function to run the checklist
run_checklist() {
    local tests="$1"
    local ussd_flow="$2"
    local agent_verification="$3"
    local failure_mode="$4"
    local report_format="$5"
    
    print_info "Starting Zambian production checklist..."
    print_info "Required tests: $tests"
    print_info "USSD flow: $ussd_flow"
    print_info "Agent verification: $agent_verification"
    print_info "Failure mode: $failure_mode"
    print_info "Report format: $report_format"
    echo ""
    
    # Build Dart command
    local dart_cmd="dart lib/scripts/run_zambia_prod_checklist.dart"
    
    if [ -n "$tests" ]; then
        dart_cmd="$dart_cmd --required-tests=\"$tests\""
    fi
    
    if [ -n "$ussd_flow" ]; then
        dart_cmd="$dart_cmd --ussd-flow=\"$ussd_flow\""
    fi
    
    if [ -n "$agent_verification" ]; then
        dart_cmd="$dart_cmd --agent-verification=\"$agent_verification\""
    fi
    
    if [ -n "$failure_mode" ]; then
        dart_cmd="$dart_cmd --failure-mode=\"$failure_mode\""
    fi
    
    if [ -n "$report_format" ]; then
        dart_cmd="$dart_cmd --report-format=$report_format"
    fi
    
    # Execute the checklist
    print_info "Executing: $dart_cmd"
    echo ""
    
    if eval "$dart_cmd"; then
        echo ""
        print_success "Zambian production checklist completed successfully!"
        
        # Show generated reports
        if [ "$report_format" != "console" ]; then
            echo ""
            print_info "Generated reports:"
            find . -name "zambia_production_checklist_*.$report_format" -o -name "zambia_production_checklist_*.txt" 2>/dev/null | while read -r file; do
                if [ -f "$file" ]; then
                    print_info "  📄 $file"
                fi
            done
        fi
        
        return 0
    else
        echo ""
        print_error "Zambian production checklist failed!"
        print_warning "Please review the output above and resolve any issues before production deployment."
        return 1
    fi
}

# Function to show pre-flight checks
show_preflight_checks() {
    echo ""
    print_info "Running pre-flight checks..."
    
    # Check Dart installation
    check_dart_installation
    
    # Check script file
    check_script_file
    
    # Check project structure
    if [ ! -d "lib" ]; then
        print_warning "lib/ directory not found. Ensure you're in the project root."
    fi
    
    print_success "Pre-flight checks completed"
    echo ""
}

# Main execution
main() {
    # Parse command line arguments
    local required_tests="$DEFAULT_TESTS"
    local ussd_flow="$DEFAULT_USSD"
    local agent_verification="$DEFAULT_AGENT"
    local failure_mode="$DEFAULT_FAILURE_MODE"
    local report_format="$DEFAULT_REPORT_FORMAT"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --required-tests=*)
                required_tests="${1#*=}"
                shift
                ;;
            --ussd-flow=*)
                ussd_flow="${1#*=}"
                shift
                ;;
            --agent-verification=*)
                agent_verification="${1#*=}"
                shift
                ;;
            --failure-mode=*)
                failure_mode="${1#*=}"
                shift
                ;;
            --report-format=*)
                report_format="${1#*=}"
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Show header
    print_header
    
    # Show pre-flight checks
    show_preflight_checks
    
    # Validate arguments
    validate_arguments "$required_tests" "$failure_mode" "$report_format"
    
    # Run the checklist
    if run_checklist "$required_tests" "$ussd_flow" "$agent_verification" "$failure_mode" "$report_format"; then
        echo ""
        print_success "🇿🇲 Pay Mule Zambia is ready for production deployment!"
        exit 0
    else
        echo ""
        print_error "🇿🇲 Pay Mule Zambia is NOT ready for production deployment!"
        print_warning "Please resolve the issues identified in the checklist before proceeding."
        exit 1
    fi
}

# Execute main function with all arguments
main "$@"
