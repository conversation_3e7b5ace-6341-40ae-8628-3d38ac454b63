# Zambia Pay - Safety Override System (PowerShell)
# Automatic failure recovery with data preservation and developer notifications
# Usage: .\safety_override.ps1 -RestorePoint "paymule_stable_v2.1"

param(
    [string]$RestorePoint = "",
    [string]$CrashReportsDir = "crash_reports",
    [string]$BackupDir = "backups",
    [switch]$NoAutoRevert,
    [switch]$NoNotifications,
    [switch]$PreserveUserData = $true,
    [string]$DeveloperWebhook = "",
    [string]$SlackWebhook = "",
    [string]$EmailRecipients = "",
    [switch]$Help
)

# Global variables
$script:FailureCount = 0
$script:RecoveryStartTime = Get-Date
$script:CriticalServices = @("database", "api", "payment_processor", "notification_service")
$script:UserDataPaths = @("user_profiles", "transaction_history", "offline_queue", "chilimba_groups")

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Magenta = "Magenta"
$Cyan = "Cyan"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor $Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Critical {
    param([string]$Message)
    Write-Host "[CRITICAL] $Message" -ForegroundColor $Cyan
}

function Write-Recovery {
    param([string]$Message)
    Write-Host "[RECOVERY] $Message" -ForegroundColor $Magenta
}

# Show help information
function Show-Help {
    @"
Zambia Pay Safety Override System (PowerShell)

USAGE:
    .\safety_override.ps1 [OPTIONS]

OPTIONS:
    -RestorePoint POINT         Git commit/tag to restore to (e.g., paymule_stable_v2.1)
    -CrashReportsDir DIR        Directory to store crash reports (default: crash_reports)
    -NoAutoRevert              Disable automatic revert to stable commit
    -NoNotifications           Disable push notifications to developers
    -PreserveUserData          Preserve user data during recovery (default: true)
    -DeveloperWebhook URL      Webhook URL for developer notifications
    -SlackWebhook URL          Slack webhook for team notifications
    -EmailRecipients ADDRESSES Comma-separated email addresses for alerts
    -Help                      Show this help message

SAFETY FEATURES:
    1. Auto-revert to last stable commit
    2. Preserve error logs in crash reports directory
    3. Send push notifications to developers
    4. Generate fix suggestion reports
    5. Backup critical user data
    6. Service health monitoring
    7. Automatic rollback procedures

EXAMPLES:
    # Basic recovery to stable version
    .\safety_override.ps1 -RestorePoint "paymule_stable_v2.1"

    # Recovery with custom crash reports location
    .\safety_override.ps1 -RestorePoint "paymule_stable_v2.1" -CrashReportsDir "C:\logs\crashes"

    # Recovery with notifications
    .\safety_override.ps1 -RestorePoint "paymule_stable_v2.1" -SlackWebhook "https://hooks.slack.com/services/..."

RESTORE POINTS:
    paymule_stable_v2.1         Latest stable release
    paymule_stable_v2.0         Previous stable release
    paymule_emergency_backup    Emergency fallback version
    paymule_minimal_core        Minimal core functionality only

"@
}

# Initialize safety override system
function Initialize-SafetySystem {
    Write-Critical "Zambia Pay Safety Override System Activated"
    Write-Info "Restore Point: $(if ($RestorePoint) { $RestorePoint } else { 'Auto-detect latest stable' })"
    Write-Info "Crash Reports: $CrashReportsDir"
    Write-Info "Auto-revert: $(if (-not $NoAutoRevert) { 'Enabled' } else { 'Disabled' })"
    Write-Info "Notifications: $(if (-not $NoNotifications) { 'Enabled' } else { 'Disabled' })"
    Write-Host ""
    
    # Create necessary directories
    New-Item -ItemType Directory -Path $CrashReportsDir -Force | Out-Null
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    
    Write-Status "Safety system initialized"
}

# Detect system failures
function Test-SystemFailures {
    Write-Info "Detecting system failures..."
    
    $failures = @()
    
    # Check critical services
    foreach ($service in $script:CriticalServices) {
        if (-not (Test-ServiceHealth $service)) {
            $failures += $service
            $script:FailureCount++
        }
    }
    
    # Check application health
    if (-not (Test-ApplicationHealth)) {
        $failures += "application"
        $script:FailureCount++
    }
    
    # Check database integrity
    if (-not (Test-DatabaseIntegrity)) {
        $failures += "database_integrity"
        $script:FailureCount++
    }
    
    # Check file system
    if (-not (Test-FilesystemHealth)) {
        $failures += "filesystem"
        $script:FailureCount++
    }
    
    if ($failures.Count -gt 0) {
        Write-Error "Detected failures in: $($failures -join ', ')"
        return $false
    } else {
        Write-Status "No critical failures detected"
        return $true
    }
}

# Check service health
function Test-ServiceHealth {
    param([string]$Service)
    
    switch ($Service) {
        "database" {
            # Check if database file exists and is accessible
            if (Test-Path "data\zambia_pay.db") {
                try {
                    # Simple check - if sqlite3 is available, test connection
                    if (Get-Command sqlite3 -ErrorAction SilentlyContinue) {
                        $result = sqlite3 "data\zambia_pay.db" "SELECT 1;" 2>$null
                        return $result -eq "1"
                    }
                    return $true
                } catch {
                    return $false
                }
            }
            return $false
        }
        "api" {
            # Check if API endpoints are responding
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                return $response.StatusCode -eq 200
            } catch {
                return $false
            }
        }
        "payment_processor" {
            # Check payment processor status
            return (Test-Path "logs\payment_processor.log") -and ((Get-Item "logs\payment_processor.log").Length -gt 0)
        }
        "notification_service" {
            # Check notification service
            return (Test-Path "logs\notifications.log") -and ((Get-Item "logs\notifications.log").Length -gt 0)
        }
        default {
            return $false
        }
    }
}

# Check application health
function Test-ApplicationHealth {
    return (Test-Path "lib\main.dart") -and 
           (Test-Path "pubspec.yaml") -and 
           (Test-Path "lib\features") -and 
           (Test-Path "lib\core")
}

# Check database integrity
function Test-DatabaseIntegrity {
    if (Test-Path "data\zambia_pay.db") {
        if (Get-Command sqlite3 -ErrorAction SilentlyContinue) {
            try {
                $result = sqlite3 "data\zambia_pay.db" "PRAGMA integrity_check;" 2>$null
                return $result -eq "ok"
            } catch {
                return $true  # Assume OK if can't check
            }
        }
        return $true
    }
    return $false
}

# Check filesystem health
function Test-FilesystemHealth {
    # Check disk space (fail if > 95% usage)
    $drive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $usagePercent = [math]::Round((($drive.Size - $drive.FreeSpace) / $drive.Size) * 100, 2)
    return $usagePercent -lt 95
}

# Preserve error logs
function Save-ErrorLogs {
    Write-Recovery "Preserving error logs and crash reports..."
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $crashReportFile = "$CrashReportsDir\crash_report_$timestamp.log"
    
    # Create comprehensive crash report
    $crashReport = @"
Zambia Pay Crash Report
Generated: $(Get-Date)
Restore Point: $RestorePoint
Failure Count: $($script:FailureCount)

=== SYSTEM INFORMATION ===
OS: $($env:OS) $([System.Environment]::OSVersion.VersionString)
PowerShell Version: $($PSVersionTable.PSVersion)
Flutter Version: $(try { flutter --version 2>$null | Select-Object -First 1 } catch { "Not available" })
Git Commit: $(try { git rev-parse HEAD 2>$null } catch { "Not available" })
Git Branch: $(try { git branch --show-current 2>$null } catch { "Not available" })

=== FAILURE ANALYSIS ===
=== SERVICE STATUS ===
"@
    
    # Add service status
    foreach ($service in $script:CriticalServices) {
        if (Test-ServiceHealth $service) {
            $crashReport += "`n$service`: HEALTHY"
        } else {
            $crashReport += "`n$service`: FAILED"
        }
    }
    
    # Add recent logs if available
    $crashReport += "`n`n=== RECENT APPLICATION LOGS ==="
    if (Test-Path "logs\app.log") {
        try {
            $recentLogs = Get-Content "logs\app.log" -Tail 50 -ErrorAction SilentlyContinue
            $crashReport += "`n$($recentLogs -join "`n")"
        } catch {
            $crashReport += "`nNo app logs available"
        }
    }
    
    # Save crash report
    Set-Content -Path $crashReportFile -Value $crashReport
    
    # Preserve additional log files
    if (Test-Path "logs") {
        $logBackupDir = "$CrashReportsDir\logs_$timestamp"
        Copy-Item -Path "logs" -Destination $logBackupDir -Recurse -ErrorAction SilentlyContinue
    }
    
    Write-Status "Crash report saved: $crashReportFile"
}

# Backup critical user data
function Backup-UserData {
    if (-not $PreserveUserData) {
        Write-Info "User data preservation disabled, skipping backup"
        return
    }
    
    Write-Recovery "Backing up critical user data..."
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupDir = "$BackupDir\user_data_backup_$timestamp"
    
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    # Backup user data paths
    foreach ($dataPath in $script:UserDataPaths) {
        if (Test-Path "data\$dataPath") {
            Copy-Item -Path "data\$dataPath" -Destination $backupDir -Recurse -ErrorAction SilentlyContinue
            Write-Info "Backed up: $dataPath"
        }
    }
    
    # Backup database
    if (Test-Path "data\zambia_pay.db") {
        Copy-Item -Path "data\zambia_pay.db" -Destination "$backupDir\zambia_pay_backup.db" -ErrorAction SilentlyContinue
        Write-Info "Backed up: database"
    }
    
    # Backup configuration files
    if (Test-Path "config\app_config.json") {
        Copy-Item -Path "config\app_config.json" -Destination $backupDir -ErrorAction SilentlyContinue
        Write-Info "Backed up: app configuration"
    }
    
    # Create backup manifest
    $manifest = @"
Zambia Pay User Data Backup
Created: $(Get-Date)
Backup Directory: $backupDir
Original Failure Count: $($script:FailureCount)

Backed up data:
$(Get-ChildItem $backupDir -ErrorAction SilentlyContinue | Format-Table -AutoSize | Out-String)
"@
    
    Set-Content -Path "$backupDir\backup_manifest.txt" -Value $manifest
    
    Write-Status "User data backed up to: $backupDir"
}

# Main execution function
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    # Initialize safety system
    Initialize-SafetySystem
    
    # Detect failures
    if (Test-SystemFailures) {
        Write-Status "No critical failures detected - system appears healthy"
        Write-Info "Safety override system ready for emergency activation"
        exit 0
    }
    
    Write-Critical "Critical failures detected - activating safety override procedures"
    
    # Step 1: Preserve error logs
    Save-ErrorLogs
    
    # Step 2: Backup critical user data
    Backup-UserData
    
    # Step 3: Send developer notifications (simplified for PowerShell)
    if (-not $NoNotifications) {
        Write-Recovery "Developer notifications would be sent here"
        # Note: Full notification implementation would require additional modules
    }
    
    # Step 4: Auto-revert to stable commit
    if (-not $NoAutoRevert) {
        Write-Recovery "Auto-reverting to stable commit..."
        
        $targetCommit = $RestorePoint
        if (-not $targetCommit) {
            # Try to detect latest stable commit
            $targetCommit = git tag -l "paymule_stable_*" | Sort-Object | Select-Object -Last 1
        }
        
        if ($targetCommit) {
            Write-Info "Reverting to: $targetCommit"
            
            try {
                # Create emergency branch
                git branch "emergency_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')" 2>$null
                
                # Perform revert
                git checkout $targetCommit 2>$null
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Status "Successfully reverted to stable commit: $targetCommit"
                    
                    # Rebuild application if needed
                    if (Test-Path "pubspec.yaml") {
                        Write-Info "Rebuilding application..."
                        flutter clean | Out-Null
                        flutter pub get | Out-Null
                        Write-Status "Application rebuilt"
                    }
                } else {
                    Write-Error "Failed to revert to stable commit: $targetCommit"
                }
            } catch {
                Write-Error "Error during revert: $($_.Exception.Message)"
            }
        } else {
            Write-Error "No stable commit found for auto-revert"
        }
    }
    
    # Verify recovery
    if (Test-SystemFailures) {
        Write-Critical "Zambia Pay SAFETY OVERRIDE SUCCESSFUL - System recovered"
        
        $recoveryDuration = ((Get-Date) - $script:RecoveryStartTime).TotalSeconds
        
        Write-Host ""
        Write-Status "Recovery Summary:"
        Write-Status "  Duration: $([math]::Round($recoveryDuration, 0)) seconds"
        Write-Status "  Data preserved: $(if ($PreserveUserData) { 'Yes' } else { 'No' })"
        Write-Status "  Auto-reverted: $(if (-not $NoAutoRevert) { 'Yes' } else { 'No' })"
        Write-Status "  Reports generated: Yes"
        Write-Host ""
        Write-Info "System ready for normal operations"
        Write-Info "Check crash reports in: $CrashReportsDir"
        
        exit 0
    } else {
        Write-Critical "Zambia Pay SAFETY OVERRIDE FAILED - Manual intervention required"
        
        Write-Host ""
        Write-Error "Recovery Failed:"
        Write-Error "  Some services still failing"
        Write-Error "  Check crash reports: $CrashReportsDir"
        Write-Error "  Contact development team immediately"
        Write-Host ""
        Write-Critical "DO NOT RESUME OPERATIONS - System unstable"
        
        exit 1
    }
}

# Execute main function
Main
