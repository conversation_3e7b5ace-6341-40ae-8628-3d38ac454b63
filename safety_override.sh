#!/bin/bash

# 🇿🇲 Zambia Pay - Safety Override System
# Automatic failure recovery with data preservation and developer notifications
# Usage: ./safety_override.sh --restore-point=paymule_stable_v2.1

set -e

# Default configuration
RESTORE_POINT=""
CRASH_REPORTS_DIR="/crash_reports"
BACKUP_DIR="/backups"
LOG_LEVEL="INFO"
AUTO_REVERT=true
SEND_NOTIFICATIONS=true
PRESERVE_USER_DATA=true
GENERATE_FIX_SUGGESTIONS=true
DEVELOPER_WEBHOOK=""
SLACK_WEBHOOK=""
EMAIL_RECIPIENTS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Safety metrics
FAILURE_COUNT=0
RECOVERY_START_TIME=$(date +%s)
CRITICAL_SERVICES=("database" "api" "payment_processor" "notification_service")
USER_DATA_PATHS=("user_profiles" "transaction_history" "offline_queue" "chilimba_groups")

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_critical() {
    echo -e "${CYAN}🚨 CRITICAL: $1${NC}"
}

print_recovery() {
    echo -e "${PURPLE}🔄 RECOVERY: $1${NC}"
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --restore-point=*)
                RESTORE_POINT="${1#*=}"
                shift
                ;;
            --crash-reports-dir=*)
                CRASH_REPORTS_DIR="${1#*=}"
                shift
                ;;
            --no-auto-revert)
                AUTO_REVERT=false
                shift
                ;;
            --no-notifications)
                SEND_NOTIFICATIONS=false
                shift
                ;;
            --preserve-user-data)
                PRESERVE_USER_DATA=true
                shift
                ;;
            --developer-webhook=*)
                DEVELOPER_WEBHOOK="${1#*=}"
                shift
                ;;
            --slack-webhook=*)
                SLACK_WEBHOOK="${1#*=}"
                shift
                ;;
            --email=*)
                EMAIL_RECIPIENTS="${1#*=}"
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Show help information
show_help() {
    cat << EOF
🇿🇲 Zambia Pay Safety Override System

USAGE:
    ./safety_override.sh [OPTIONS]

OPTIONS:
    --restore-point=POINT        Git commit/tag to restore to (e.g., paymule_stable_v2.1)
    --crash-reports-dir=DIR      Directory to store crash reports (default: /crash_reports)
    --no-auto-revert            Disable automatic revert to stable commit
    --no-notifications          Disable push notifications to developers
    --preserve-user-data        Preserve user data during recovery (default: true)
    --developer-webhook=URL     Webhook URL for developer notifications
    --slack-webhook=URL         Slack webhook for team notifications
    --email=ADDRESSES           Comma-separated email addresses for alerts
    --help                      Show this help message

SAFETY FEATURES:
    1. Auto-revert to last stable commit
    2. Preserve error logs in crash reports directory
    3. Send push notifications to developers
    4. Generate fix suggestion reports
    5. Backup critical user data
    6. Service health monitoring
    7. Automatic rollback procedures

EXAMPLES:
    # Basic recovery to stable version
    ./safety_override.sh --restore-point=paymule_stable_v2.1

    # Recovery with custom crash reports location
    ./safety_override.sh --restore-point=paymule_stable_v2.1 --crash-reports-dir=/var/log/zambia_pay/crashes

    # Recovery with notifications
    ./safety_override.sh --restore-point=paymule_stable_v2.1 --developer-webhook=https://hooks.slack.com/services/...

    # Manual recovery without auto-revert
    ./safety_override.sh --restore-point=paymule_stable_v2.1 --no-auto-revert

RESTORE POINTS:
    paymule_stable_v2.1         Latest stable release
    paymule_stable_v2.0         Previous stable release
    paymule_emergency_backup    Emergency fallback version
    paymule_minimal_core        Minimal core functionality only

EOF
}

# Initialize safety override system
initialize_safety_system() {
    print_critical "🇿🇲 Zambia Pay Safety Override System Activated"
    print_info "Restore Point: ${RESTORE_POINT:-'Auto-detect latest stable'}"
    print_info "Crash Reports: $CRASH_REPORTS_DIR"
    print_info "Auto-revert: $([ "$AUTO_REVERT" = true ] && echo "Enabled" || echo "Disabled")"
    print_info "Notifications: $([ "$SEND_NOTIFICATIONS" = true ] && echo "Enabled" || echo "Disabled")"
    echo ""
    
    # Create necessary directories
    mkdir -p "$CRASH_REPORTS_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "/var/log/zambia_pay"
    
    # Set permissions for crash reports
    chmod 755 "$CRASH_REPORTS_DIR"
    
    print_status "Safety system initialized"
}

# Detect system failures
detect_failures() {
    print_info "🔍 Detecting system failures..."
    
    local failures=()
    
    # Check critical services
    for service in "${CRITICAL_SERVICES[@]}"; do
        if ! check_service_health "$service"; then
            failures+=("$service")
            ((FAILURE_COUNT++))
        fi
    done
    
    # Check application health
    if ! check_application_health; then
        failures+=("application")
        ((FAILURE_COUNT++))
    fi
    
    # Check database integrity
    if ! check_database_integrity; then
        failures+=("database_integrity")
        ((FAILURE_COUNT++))
    fi
    
    # Check file system
    if ! check_filesystem_health; then
        failures+=("filesystem")
        ((FAILURE_COUNT++))
    fi
    
    if [ ${#failures[@]} -gt 0 ]; then
        print_error "Detected failures in: ${failures[*]}"
        return 1
    else
        print_status "No critical failures detected"
        return 0
    fi
}

# Check service health
check_service_health() {
    local service=$1
    
    case "$service" in
        "database")
            # Check if database is responding
            if command -v sqlite3 &> /dev/null; then
                sqlite3 "data/zambia_pay.db" "SELECT 1;" &> /dev/null
            else
                return 1
            fi
            ;;
        "api")
            # Check if API endpoints are responding
            if command -v curl &> /dev/null; then
                curl -f -s "http://localhost:8080/health" &> /dev/null
            else
                return 1
            fi
            ;;
        "payment_processor")
            # Check payment processor status
            [ -f "logs/payment_processor.log" ] && [ -s "logs/payment_processor.log" ]
            ;;
        "notification_service")
            # Check notification service
            [ -f "logs/notifications.log" ] && [ -s "logs/notifications.log" ]
            ;;
        *)
            return 1
            ;;
    esac
}

# Check application health
check_application_health() {
    # Check if main application files exist
    [ -f "lib/main.dart" ] && \
    [ -f "pubspec.yaml" ] && \
    [ -d "lib/features" ] && \
    [ -d "lib/core" ]
}

# Check database integrity
check_database_integrity() {
    if [ -f "data/zambia_pay.db" ]; then
        # Check database file integrity
        if command -v sqlite3 &> /dev/null; then
            sqlite3 "data/zambia_pay.db" "PRAGMA integrity_check;" | grep -q "ok"
        else
            return 0  # Assume OK if sqlite3 not available
        fi
    else
        return 1
    fi
}

# Check filesystem health
check_filesystem_health() {
    # Check disk space
    local disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    [ "$disk_usage" -lt 95 ]  # Fail if disk usage > 95%
}

# Preserve error logs
preserve_error_logs() {
    print_recovery "📋 Preserving error logs and crash reports..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local crash_report_file="$CRASH_REPORTS_DIR/crash_report_$timestamp.log"
    
    # Create comprehensive crash report
    cat > "$crash_report_file" << EOF
🇿🇲 Zambia Pay Crash Report
Generated: $(date)
Restore Point: $RESTORE_POINT
Failure Count: $FAILURE_COUNT

=== SYSTEM INFORMATION ===
OS: $(uname -a)
Flutter Version: $(flutter --version 2>/dev/null | head -1 || echo "Not available")
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "Not available")
Git Branch: $(git branch --show-current 2>/dev/null || echo "Not available")

=== FAILURE ANALYSIS ===
EOF
    
    # Add service status
    echo "=== SERVICE STATUS ===" >> "$crash_report_file"
    for service in "${CRITICAL_SERVICES[@]}"; do
        if check_service_health "$service"; then
            echo "$service: HEALTHY" >> "$crash_report_file"
        else
            echo "$service: FAILED" >> "$crash_report_file"
        fi
    done
    
    # Add recent logs
    echo "" >> "$crash_report_file"
    echo "=== RECENT APPLICATION LOGS ===" >> "$crash_report_file"
    if [ -f "logs/app.log" ]; then
        tail -50 "logs/app.log" >> "$crash_report_file" 2>/dev/null || echo "No app logs available" >> "$crash_report_file"
    fi
    
    # Add Flutter logs
    echo "" >> "$crash_report_file"
    echo "=== FLUTTER LOGS ===" >> "$crash_report_file"
    if [ -f "logs/flutter.log" ]; then
        tail -50 "logs/flutter.log" >> "$crash_report_file" 2>/dev/null || echo "No Flutter logs available" >> "$crash_report_file"
    fi
    
    # Add system logs
    echo "" >> "$crash_report_file"
    echo "=== SYSTEM LOGS ===" >> "$crash_report_file"
    journalctl --no-pager -n 20 >> "$crash_report_file" 2>/dev/null || echo "No system logs available" >> "$crash_report_file"
    
    # Preserve additional log files
    if [ -d "logs" ]; then
        cp -r "logs" "$CRASH_REPORTS_DIR/logs_$timestamp/" 2>/dev/null || true
    fi
    
    print_status "Crash report saved: $crash_report_file"
}

# Backup critical user data
backup_user_data() {
    if [ "$PRESERVE_USER_DATA" = false ]; then
        print_info "User data preservation disabled, skipping backup"
        return 0
    fi

    print_recovery "💾 Backing up critical user data..."

    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="$BACKUP_DIR/user_data_backup_$timestamp"

    mkdir -p "$backup_dir"

    # Backup user data paths
    for data_path in "${USER_DATA_PATHS[@]}"; do
        if [ -d "data/$data_path" ]; then
            cp -r "data/$data_path" "$backup_dir/" 2>/dev/null || true
            print_info "Backed up: $data_path"
        elif [ -f "data/$data_path" ]; then
            cp "data/$data_path" "$backup_dir/" 2>/dev/null || true
            print_info "Backed up: $data_path"
        fi
    done

    # Backup database
    if [ -f "data/zambia_pay.db" ]; then
        cp "data/zambia_pay.db" "$backup_dir/zambia_pay_backup.db" 2>/dev/null || true
        print_info "Backed up: database"
    fi

    # Backup configuration files
    if [ -f "config/app_config.json" ]; then
        cp "config/app_config.json" "$backup_dir/" 2>/dev/null || true
        print_info "Backed up: app configuration"
    fi

    # Create backup manifest
    cat > "$backup_dir/backup_manifest.txt" << EOF
Zambia Pay User Data Backup
Created: $(date)
Backup Directory: $backup_dir
Original Failure Count: $FAILURE_COUNT

Backed up data:
$(ls -la "$backup_dir" 2>/dev/null || echo "No files backed up")
EOF

    print_status "User data backed up to: $backup_dir"
}

# Auto-revert to stable commit
auto_revert_to_stable() {
    if [ "$AUTO_REVERT" = false ]; then
        print_info "Auto-revert disabled, skipping automatic rollback"
        return 0
    fi

    print_recovery "🔄 Auto-reverting to stable commit..."

    # Determine restore point
    local target_commit="$RESTORE_POINT"

    if [ -z "$target_commit" ]; then
        # Auto-detect latest stable commit
        target_commit=$(detect_latest_stable_commit)
    fi

    if [ -z "$target_commit" ]; then
        print_error "No stable commit found for auto-revert"
        return 1
    fi

    print_info "Reverting to: $target_commit"

    # Backup current state before revert
    local current_commit=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    print_info "Current commit: $current_commit"

    # Create emergency branch for current state
    git branch "emergency_backup_$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true

    # Perform the revert
    if git checkout "$target_commit" 2>/dev/null; then
        print_status "Successfully reverted to stable commit: $target_commit"

        # Rebuild application if needed
        if [ -f "pubspec.yaml" ]; then
            print_info "Rebuilding application..."
            flutter clean &>/dev/null || true
            flutter pub get &>/dev/null || true
            print_status "Application rebuilt"
        fi

        return 0
    else
        print_error "Failed to revert to stable commit: $target_commit"
        return 1
    fi
}

# Detect latest stable commit
detect_latest_stable_commit() {
    # Look for stable tags first
    local stable_tag=$(git tag -l "paymule_stable_*" | sort -V | tail -1 2>/dev/null)

    if [ -n "$stable_tag" ]; then
        echo "$stable_tag"
        return 0
    fi

    # Look for release tags
    local release_tag=$(git tag -l "v*" | sort -V | tail -1 2>/dev/null)

    if [ -n "$release_tag" ]; then
        echo "$release_tag"
        return 0
    fi

    # Look for main/master branch
    if git show-ref --verify --quiet refs/heads/main; then
        echo "main"
    elif git show-ref --verify --quiet refs/heads/master; then
        echo "master"
    else
        echo ""
    fi
}

# Send push notifications to developers
send_developer_notifications() {
    if [ "$SEND_NOTIFICATIONS" = false ]; then
        print_info "Notifications disabled, skipping alerts"
        return 0
    fi

    print_recovery "📱 Sending developer notifications..."

    local timestamp=$(date)
    local current_commit=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    local restore_point="${RESTORE_POINT:-'auto-detected'}"

    # Prepare notification message
    local message="🚨 Zambia Pay Safety Override Activated

⏰ Time: $timestamp
🔧 Restore Point: $restore_point
💥 Failure Count: $FAILURE_COUNT
📍 Current Commit: $current_commit
🔄 Auto-revert: $([ "$AUTO_REVERT" = true ] && echo "Enabled" || echo "Disabled")

Critical services affected:
$(for service in "${CRITICAL_SERVICES[@]}"; do
    if check_service_health "$service"; then
        echo "✅ $service: Healthy"
    else
        echo "❌ $service: Failed"
    fi
done)

Crash reports available in: $CRASH_REPORTS_DIR

Please investigate immediately!"

    # Send Slack notification
    if [ -n "$SLACK_WEBHOOK" ]; then
        send_slack_notification "$message"
    fi

    # Send webhook notification
    if [ -n "$DEVELOPER_WEBHOOK" ]; then
        send_webhook_notification "$message"
    fi

    # Send email notifications
    if [ -n "$EMAIL_RECIPIENTS" ]; then
        send_email_notifications "$message"
    fi

    # Send system notification (if desktop environment available)
    if command -v notify-send &> /dev/null; then
        notify-send "Zambia Pay Safety Override" "Critical failure detected - check logs immediately" --urgency=critical
    fi

    print_status "Developer notifications sent"
}

# Send Slack notification
send_slack_notification() {
    local message="$1"

    if command -v curl &> /dev/null; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$message\"}" \
            "$SLACK_WEBHOOK" &>/dev/null || true
        print_info "Slack notification sent"
    fi
}

# Send webhook notification
send_webhook_notification() {
    local message="$1"

    if command -v curl &> /dev/null; then
        curl -X POST -H 'Content-Type: application/json' \
            --data "{\"message\":\"$message\",\"severity\":\"critical\",\"service\":\"zambia_pay\",\"timestamp\":\"$(date -Iseconds)\"}" \
            "$DEVELOPER_WEBHOOK" &>/dev/null || true
        print_info "Webhook notification sent"
    fi
}

# Send email notifications
send_email_notifications() {
    local message="$1"

    if command -v mail &> /dev/null; then
        IFS=',' read -ra EMAILS <<< "$EMAIL_RECIPIENTS"
        for email in "${EMAILS[@]}"; do
            echo "$message" | mail -s "🚨 Zambia Pay Safety Override Activated" "$email" &>/dev/null || true
        done
        print_info "Email notifications sent"
    fi
}

# Generate fix suggestion report
generate_fix_suggestions() {
    if [ "$GENERATE_FIX_SUGGESTIONS" = false ]; then
        print_info "Fix suggestion generation disabled"
        return 0
    fi

    print_recovery "🔧 Generating fix suggestion report..."

    local timestamp=$(date +%Y%m%d_%H%M%S)
    local suggestions_file="$CRASH_REPORTS_DIR/fix_suggestions_$timestamp.md"

    cat > "$suggestions_file" << EOF
# 🇿🇲 Zambia Pay Fix Suggestions Report

**Generated:** $(date)
**Failure Count:** $FAILURE_COUNT
**Restore Point:** ${RESTORE_POINT:-'auto-detected'}

## 🔍 Failure Analysis

EOF

    # Analyze each failed service
    for service in "${CRITICAL_SERVICES[@]}"; do
        if ! check_service_health "$service"; then
            echo "### ❌ $service Service Failure" >> "$suggestions_file"
            generate_service_fix_suggestions "$service" >> "$suggestions_file"
            echo "" >> "$suggestions_file"
        fi
    done

    # General fix suggestions
    cat >> "$suggestions_file" << EOF
## 🛠️ General Recovery Steps

### Immediate Actions
1. **Verify System Resources**
   - Check disk space: \`df -h\`
   - Check memory usage: \`free -h\`
   - Check CPU load: \`top\`

2. **Review Recent Changes**
   - Check recent commits: \`git log --oneline -10\`
   - Review recent deployments
   - Check configuration changes

3. **Validate Dependencies**
   - Run: \`flutter doctor\`
   - Check: \`flutter pub deps\`
   - Verify: Database connectivity

### Recovery Options

#### Option 1: Quick Fix (Recommended)
\`\`\`bash
# Revert to stable version
./safety_override.sh --restore-point=paymule_stable_v2.1

# Verify services
./zambia_validation_suite.sh --critical-modules="momo,offline,notifications"
\`\`\`

#### Option 2: Manual Investigation
\`\`\`bash
# Check logs
tail -f logs/app.log

# Test individual components
flutter test test/core/
flutter test test/features/

# Rebuild from scratch
flutter clean && flutter pub get && flutter build apk
\`\`\`

#### Option 3: Emergency Rollback
\`\`\`bash
# Use minimal core functionality
./safety_override.sh --restore-point=paymule_minimal_core --preserve-user-data
\`\`\`

## 📋 Checklist for Resolution

- [ ] System resources adequate (disk, memory, CPU)
- [ ] All critical services responding
- [ ] Database integrity verified
- [ ] User data preserved and accessible
- [ ] Payment processing functional
- [ ] Notification system operational
- [ ] Mobile money APIs responding
- [ ] Offline sync working correctly

## 🚀 Post-Recovery Actions

1. **Run comprehensive validation**
   \`\`\`bash
   ./zambia_validation_suite.sh --coverage-threshold=95%
   \`\`\`

2. **Execute live testing**
   \`\`\`bash
   ./live_zambia_test.sh --user-phone=+260961234567 --scenarios="market_payment,zesco_bill"
   \`\`\`

3. **Monitor system health**
   - Watch logs for 30 minutes
   - Test critical user journeys
   - Verify data consistency

4. **Update monitoring**
   - Add alerts for detected failure patterns
   - Improve health checks
   - Update recovery procedures

## 📞 Escalation

If issues persist after following these suggestions:

1. **Contact Development Team**
   - Provide crash report: \`$CRASH_REPORTS_DIR/crash_report_$timestamp.log\`
   - Share fix suggestions: This file
   - Include system information

2. **Emergency Contacts**
   - Technical Lead: [Contact Information]
   - DevOps Team: [Contact Information]
   - Product Owner: [Contact Information]

---
*Generated by Zambia Pay Safety Override System*
EOF

    print_status "Fix suggestions generated: $suggestions_file"
}

# Generate service-specific fix suggestions
generate_service_fix_suggestions() {
    local service="$1"

    case "$service" in
        "database")
            cat << EOF
**Possible Causes:**
- Database file corruption
- Insufficient disk space
- Lock file issues
- Permission problems

**Fix Suggestions:**
1. Check database integrity: \`sqlite3 data/zambia_pay.db "PRAGMA integrity_check;"\`
2. Verify disk space: \`df -h\`
3. Check file permissions: \`ls -la data/\`
4. Remove lock files: \`rm -f data/*.lock\`
5. Restore from backup if corrupted
EOF
            ;;
        "api")
            cat << EOF
**Possible Causes:**
- Service not running
- Port conflicts
- Network connectivity issues
- Configuration errors

**Fix Suggestions:**
1. Check if service is running: \`ps aux | grep api\`
2. Verify port availability: \`netstat -tulpn | grep 8080\`
3. Test connectivity: \`curl -f http://localhost:8080/health\`
4. Check configuration: \`cat config/api_config.json\`
5. Restart service: \`systemctl restart zambia-pay-api\`
EOF
            ;;
        "payment_processor")
            cat << EOF
**Possible Causes:**
- Mobile money API failures
- Network connectivity issues
- Authentication problems
- Rate limiting

**Fix Suggestions:**
1. Check mobile money provider status
2. Verify API credentials
3. Test network connectivity to providers
4. Review rate limiting logs
5. Check offline transaction queue
EOF
            ;;
        "notification_service")
            cat << EOF
**Possible Causes:**
- SMS gateway issues
- Push notification service down
- Configuration problems
- Network connectivity

**Fix Suggestions:**
1. Test SMS gateway connectivity
2. Verify push notification certificates
3. Check notification queue
4. Review service configuration
5. Test with sample notifications
EOF
            ;;
        *)
            echo "**Generic service failure - check logs and configuration**"
            ;;
    esac
}

# Verify recovery success
verify_recovery() {
    print_recovery "✅ Verifying recovery success..."

    local recovery_successful=true

    # Re-check critical services
    for service in "${CRITICAL_SERVICES[@]}"; do
        if ! check_service_health "$service"; then
            print_error "Service still failing after recovery: $service"
            recovery_successful=false
        else
            print_status "Service recovered: $service"
        fi
    done

    # Check application health
    if ! check_application_health; then
        print_error "Application health check failed after recovery"
        recovery_successful=false
    else
        print_status "Application health check passed"
    fi

    # Check database integrity
    if ! check_database_integrity; then
        print_error "Database integrity check failed after recovery"
        recovery_successful=false
    else
        print_status "Database integrity verified"
    fi

    if [ "$recovery_successful" = true ]; then
        print_status "Recovery verification successful"
        return 0
    else
        print_error "Recovery verification failed"
        return 1
    fi
}

# Generate recovery report
generate_recovery_report() {
    print_recovery "📋 Generating recovery report..."

    local recovery_end_time=$(date +%s)
    local recovery_duration=$((recovery_end_time - RECOVERY_START_TIME))
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local report_file="$CRASH_REPORTS_DIR/recovery_report_$timestamp.md"

    cat > "$report_file" << EOF
# 🇿🇲 Zambia Pay Recovery Report

**Recovery Completed:** $(date)
**Recovery Duration:** ${recovery_duration} seconds
**Failure Count:** $FAILURE_COUNT
**Restore Point Used:** ${RESTORE_POINT:-'auto-detected'}

## 📊 Recovery Summary

### Actions Taken
- [$([ "$AUTO_REVERT" = true ] && echo "x" || echo " ")] Auto-revert to stable commit
- [x] Error logs preserved
- [$([ "$SEND_NOTIFICATIONS" = true ] && echo "x" || echo " ")] Developer notifications sent
- [$([ "$PRESERVE_USER_DATA" = true ] && echo "x" || echo " ")] User data backed up
- [$([ "$GENERATE_FIX_SUGGESTIONS" = true ] && echo "x" || echo " ")] Fix suggestions generated

### Service Status After Recovery
EOF

    # Add service status
    for service in "${CRITICAL_SERVICES[@]}"; do
        if check_service_health "$service"; then
            echo "- ✅ $service: Healthy" >> "$report_file"
        else
            echo "- ❌ $service: Still failing" >> "$report_file"
        fi
    done

    cat >> "$report_file" << EOF

### Files Generated
- Crash Report: \`$CRASH_REPORTS_DIR/crash_report_$timestamp.log\`
- Fix Suggestions: \`$CRASH_REPORTS_DIR/fix_suggestions_$timestamp.md\`
- Recovery Report: \`$report_file\`
$([ "$PRESERVE_USER_DATA" = true ] && echo "- User Data Backup: \`$BACKUP_DIR/user_data_backup_$timestamp/\`")

### Next Steps
1. Review crash report for root cause analysis
2. Implement fix suggestions to prevent recurrence
3. Update monitoring and alerting systems
4. Test system thoroughly before resuming normal operations

### System Information
- Git Commit After Recovery: \`$(git rev-parse HEAD 2>/dev/null || echo "unknown")\`
- Flutter Version: \`$(flutter --version 2>/dev/null | head -1 || echo "Not available")\`
- System Load: \`$(uptime 2>/dev/null || echo "Not available")\`
- Disk Usage: \`$(df -h . | tail -1 | awk '{print $5}' 2>/dev/null || echo "Not available")\`

---
*Generated by Zambia Pay Safety Override System*
EOF

    print_status "Recovery report generated: $report_file"
}

# Main execution function
main() {
    # Parse command line arguments
    parse_arguments "$@"

    # Initialize safety system
    initialize_safety_system

    # Detect failures
    if detect_failures; then
        print_status "No critical failures detected - system appears healthy"
        print_info "Safety override system ready for emergency activation"
        exit 0
    fi

    print_critical "Critical failures detected - activating safety override procedures"

    # Step 1: Preserve error logs
    preserve_error_logs

    # Step 2: Backup critical user data
    backup_user_data

    # Step 3: Send developer notifications
    send_developer_notifications

    # Step 4: Auto-revert to stable commit
    if auto_revert_to_stable; then
        print_status "Auto-revert completed successfully"
    else
        print_error "Auto-revert failed - manual intervention required"
    fi

    # Step 5: Generate fix suggestions
    generate_fix_suggestions

    # Step 6: Verify recovery
    if verify_recovery; then
        print_critical "🇿🇲 SAFETY OVERRIDE SUCCESSFUL - System recovered"

        # Generate recovery report
        generate_recovery_report

        echo ""
        print_status "Recovery Summary:"
        print_status "  ⏱️  Duration: $(($(date +%s) - RECOVERY_START_TIME)) seconds"
        print_status "  💾 Data preserved: $([ "$PRESERVE_USER_DATA" = true ] && echo "Yes" || echo "No")"
        print_status "  🔄 Auto-reverted: $([ "$AUTO_REVERT" = true ] && echo "Yes" || echo "No")"
        print_status "  📱 Notifications sent: $([ "$SEND_NOTIFICATIONS" = true ] && echo "Yes" || echo "No")"
        print_status "  📋 Reports generated: Yes"
        echo ""
        print_info "🚀 System ready for normal operations"
        print_info "📁 Check crash reports in: $CRASH_REPORTS_DIR"

        exit 0
    else
        print_critical "🇿🇲 SAFETY OVERRIDE FAILED - Manual intervention required"

        # Generate recovery report even if failed
        generate_recovery_report

        echo ""
        print_error "Recovery Failed:"
        print_error "  💥 Some services still failing"
        print_error "  📋 Check crash reports: $CRASH_REPORTS_DIR"
        print_error "  🔧 Review fix suggestions"
        print_error "  📞 Contact development team immediately"
        echo ""
        print_critical "🛑 DO NOT RESUME OPERATIONS - System unstable"

        exit 1
    fi
}

# Execute main function with all arguments
main "$@"
