#!/bin/bash

# Pay Mule Icon Generation Script
# Generates adaptive launcher icons for Android and iOS from source image
# Supports all required resolutions: 48dp, 72dp, 96dp, 144dp, 192dp

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SOURCE_IMAGE="$1"
ANDROID_RES_DIR="android/app/src/main/res"
IOS_ASSETS_DIR="ios/Runner/Assets.xcassets/AppIcon.appiconset"

# Zambia color scheme validation
ZAMBIA_GREEN="#228B22"
ZAMBIA_ORANGE="#FF8C00"
ZAMBIA_RED="#DC143C"
ZAMBIA_BLACK="#000000"

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if ImageMagick is installed
check_dependencies() {
    print_info "Checking dependencies..."
    
    if ! command -v convert &> /dev/null; then
        print_error "ImageMagick is not installed. Please install it first:"
        echo "  Ubuntu/Debian: sudo apt-get install imagemagick"
        echo "  macOS: brew install imagemagick"
        echo "  Windows: Download from https://imagemagick.org/script/download.php"
        exit 1
    fi
    
    if ! command -v identify &> /dev/null; then
        print_error "ImageMagick identify command not found"
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

# Validate source image
validate_source_image() {
    if [ -z "$SOURCE_IMAGE" ]; then
        print_error "Usage: $0 <source_image_path>"
        echo "Example: $0 pay_mule_logo.png"
        exit 1
    fi
    
    if [ ! -f "$SOURCE_IMAGE" ]; then
        print_error "Source image not found: $SOURCE_IMAGE"
        exit 1
    fi
    
    # Check image dimensions
    local dimensions=$(identify -format "%wx%h" "$SOURCE_IMAGE")
    local width=$(echo $dimensions | cut -d'x' -f1)
    local height=$(echo $dimensions | cut -d'x' -f2)
    
    print_info "Source image: $SOURCE_IMAGE ($dimensions)"
    
    if [ "$width" -lt 1024 ] || [ "$height" -lt 1024 ]; then
        print_warning "Source image should be at least 1024x1024 for best quality"
    fi
    
    if [ "$width" != "$height" ]; then
        print_warning "Source image is not square. It will be cropped to square."
    fi
}

# Validate Zambia color scheme
validate_color_scheme() {
    print_info "Validating Zambia color scheme..."
    
    # Extract dominant colors from image
    local colors=$(convert "$SOURCE_IMAGE" -resize 1x1 -format "%[pixel:u]" info:)
    print_info "Dominant color detected: $colors"
    
    # Check if image contains Zambia flag colors (basic check)
    local has_green=$(convert "$SOURCE_IMAGE" -fuzz 20% -fill white -opaque "#228B22" -format "%[fx:mean]" info:)
    local has_orange=$(convert "$SOURCE_IMAGE" -fuzz 20% -fill white -opaque "#FF8C00" -format "%[fx:mean]" info:)
    
    if (( $(echo "$has_green < 0.9" | bc -l) )) || (( $(echo "$has_orange < 0.9" | bc -l) )); then
        print_success "Zambia color scheme detected in image"
    else
        print_warning "Zambia flag colors not prominently detected. Ensure icon represents Zambian identity."
    fi
}

# Generate Android icons
generate_android_icons() {
    print_info "Generating Android icons..."
    
    # Android icon sizes (in pixels for different densities)
    declare -A android_sizes=(
        ["mdpi"]="48"      # 48dp
        ["hdpi"]="72"      # 72dp  
        ["xhdpi"]="96"     # 96dp
        ["xxhdpi"]="144"   # 144dp
        ["xxxhdpi"]="192"  # 192dp
    )
    
    for density in "${!android_sizes[@]}"; do
        local size="${android_sizes[$density]}"
        local output_dir="$ANDROID_RES_DIR/mipmap-$density"
        local output_file="$output_dir/ic_launcher.png"
        
        print_info "Generating ${density} icon (${size}x${size}px)..."
        
        # Create directory if it doesn't exist
        mkdir -p "$output_dir"
        
        # Generate icon with proper scaling and sharpening
        convert "$SOURCE_IMAGE" \
            -resize "${size}x${size}^" \
            -gravity center \
            -extent "${size}x${size}" \
            -unsharp 0x1 \
            "$output_file"
        
        print_success "Generated: $output_file"
    done
}

# Generate iOS icons
generate_ios_icons() {
    print_info "Generating iOS icons..."
    
    # iOS icon sizes
    declare -A ios_sizes=(
        ["Icon-App-20x20@1x"]="20"
        ["Icon-App-20x20@2x"]="40"
        ["Icon-App-20x20@3x"]="60"
        ["Icon-App-29x29@1x"]="29"
        ["Icon-App-29x29@2x"]="58"
        ["Icon-App-29x29@3x"]="87"
        ["Icon-App-40x40@1x"]="40"
        ["Icon-App-40x40@2x"]="80"
        ["Icon-App-40x40@3x"]="120"
        ["Icon-App-60x60@2x"]="120"
        ["Icon-App-60x60@3x"]="180"
        ["Icon-App-76x76@1x"]="76"
        ["Icon-App-76x76@2x"]="152"
        ["Icon-App-83.5x83.5@2x"]="167"
        ["Icon-App-1024x1024@1x"]="1024"
    )
    
    for icon_name in "${!ios_sizes[@]}"; do
        local size="${ios_sizes[$icon_name]}"
        local output_file="$IOS_ASSETS_DIR/${icon_name}.png"
        
        print_info "Generating ${icon_name} (${size}x${size}px)..."
        
        # Generate icon with proper scaling
        convert "$SOURCE_IMAGE" \
            -resize "${size}x${size}^" \
            -gravity center \
            -extent "${size}x${size}" \
            -unsharp 0x1 \
            "$output_file"
        
        print_success "Generated: $output_file"
    done
}

# Validate generated icons
validate_icons() {
    print_info "Validating generated icons..."
    
    local error_count=0
    
    # Check Android icons
    for density in mdpi hdpi xhdpi xxhdpi xxxhdpi; do
        local icon_file="$ANDROID_RES_DIR/mipmap-$density/ic_launcher.png"
        if [ ! -f "$icon_file" ]; then
            print_error "Missing Android icon: $icon_file"
            ((error_count++))
        else
            local size=$(identify -format "%wx%h" "$icon_file")
            print_success "Android $density: $size"
        fi
    done
    
    # Check iOS icons
    for icon_name in "Icon-App-20x20@1x" "Icon-App-60x60@3x" "Icon-App-1024x1024@1x"; do
        local icon_file="$IOS_ASSETS_DIR/${icon_name}.png"
        if [ ! -f "$icon_file" ]; then
            print_error "Missing iOS icon: $icon_file"
            ((error_count++))
        else
            local size=$(identify -format "%wx%h" "$icon_file")
            print_success "iOS $icon_name: $size"
        fi
    done
    
    if [ $error_count -eq 0 ]; then
        print_success "All icons validated successfully!"
        return 0
    else
        print_error "Icon validation failed with $error_count errors"
        return 1
    fi
}

# Main execution
main() {
    print_info "Pay Mule Icon Generation Script"
    print_info "==============================="
    
    check_dependencies
    validate_source_image
    validate_color_scheme
    
    print_info "Starting icon generation..."
    
    generate_android_icons
    generate_ios_icons
    
    if validate_icons; then
        print_success "Icon generation completed successfully!"
        print_info "Next steps:"
        echo "  1. Run: flutter clean"
        echo "  2. Run: flutter pub get"
        echo "  3. Test on device: flutter run"
        echo "  4. Verify icons appear correctly in app drawer"
    else
        print_error "Icon generation completed with errors"
        exit 1
    fi
}

# Run main function
main
