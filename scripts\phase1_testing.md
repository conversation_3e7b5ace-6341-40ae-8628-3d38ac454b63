# Zambia Pay - Phase 1 Testing Guide

## 🇿🇲 **Zambia-Centric Solutions Testing**

This guide covers testing of the key Zambia-specific features that address rural resilience, informal market support, utility pain relief, and financial inclusion.

## **🧪 Validation Protocol (Before Each Commit)**

**EXECUTE SAFELY**: Before any deployment or commit, run the comprehensive validation suite:

```bash
# Linux/macOS
./zambia_validation_suite.sh \
--critical-modules="momo,offline,notifications" \
--coverage-threshold=90% \
--max-failures=0 \
--report-file=validation_report.html

# Windows PowerShell
.\zambia_validation_suite.ps1 -CriticalModules "momo,offline,notifications" -CoverageThreshold 90 -MaxFailures 0 -ReportFile "validation_report.html"
```

**Critical Validation Areas**:
- 🏦 **Mobile Money API Stability**: MTN, Airtel, Zamtel provider tests
- 📱 **Offline Sync Integrity**: Transaction queue and conflict resolution
- 🔔 **Notification Delivery**: SMS tokens, push notifications, utility alerts
- 🌍 **Localization**: Nyanja, Bemba, English language support

## **Phase 1 Testing Command**

```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=eastern_province
```

### **Environment Variables**

| Variable | Options | Description |
|----------|---------|-------------|
| `ENV` | `development`, `sandbox`, `staging`, `production` | Testing environment |
| `REGION` | `lusaka`, `copperbelt`, `eastern_province`, `southern_province`, `western_province`, etc. | Zambian province for regional testing |
| `TEST_MODE` | `true`, `false` | Enable additional test features |

## **1. Rural Resilience Testing**

### **SMS Token System (Floods/Outages)**

**Test Scenario**: Network outage during rainy season in Eastern Province

```bash
# Start with poor connectivity simulation
flutter run --dart-define=ENV=sandbox --dart-define=REGION=eastern_province --dart-define=TEST_MODE=true
```

**Test Steps**:
1. **Create Transaction**: Send K100 to ************
2. **Simulate Outage**: Disable internet connection
3. **Generate SMS Token**: App should automatically create SMS token
4. **Verify SMS**: Check SMS with format: `TOKEN ABC123 456789`
5. **Redeem Token**: Recipient replies with `TOKEN ABC123 456789`
6. **Confirm Completion**: Transaction completes via SMS

**Expected Results**:
- ✅ SMS token generated within 5 seconds
- ✅ SMS sent in local language (Nyanja for Eastern Province)
- ✅ Token redeemable within 24 hours
- ✅ Transaction completes without internet

### **Voice-Guided UI (Bemba/Nyanja)**

**Test Scenario**: Low-literacy user in rural Chipata

```bash
# Test with Nyanja language preference
flutter run --dart-define=ENV=sandbox --dart-define=REGION=eastern_province
```

**Test Steps**:
1. **Set Language**: Select Nyanja in settings
2. **Navigate Dashboard**: Tap payment buttons
3. **Listen to Voice**: Verify voice prompts play
4. **Water Payment**: Tap water bill button
5. **Verify Audio**: Should play "madzi bill" in Nyanja

**Expected Results**:
- ✅ Voice prompts in correct local language
- ✅ Clear pronunciation and slow speech
- ✅ Audio plays without internet connection
- ✅ Large icons (56px) for easy tapping

## **2. Informal Market Support Testing**

### **Chilimba (Community Savings) Groups**

**Test Scenario**: Market vendors in Lusaka creating savings group

```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=lusaka
```

**Test Steps**:
1. **Create Group**: "Lusaka Market Vendors" - K50/week, 10 members
2. **Add Guarantors**: Select 2 community guarantors
3. **Join Members**: Add 9 more members with guarantor approval
4. **Make Contributions**: Each member contributes K50
5. **Process Payout**: First member receives K500 (10 × K50)
6. **Next Round**: Start round 2 automatically

**Expected Results**:
- ✅ Group created with community guarantors
- ✅ Round-robin payout system works
- ✅ All contributions tracked securely
- ✅ SMS notifications to all members

### **Merchant QR Codes (2G Networks)**

**Test Scenario**: Rural shop in Western Province with 2G only

```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=western_province
```

**Test Steps**:
1. **Generate QR**: Create merchant QR code
2. **Simulate 2G**: Limit connection to 2G speeds
3. **Scan Payment**: Customer scans QR for K25 purchase
4. **Process Offline**: Payment should work without internet
5. **Sync Later**: Transaction syncs when connection improves

**Expected Results**:
- ✅ QR code works on 2G networks
- ✅ Offline payment processing
- ✅ Automatic sync when online
- ✅ Receipt generated immediately

## **3. Utility Pain Relief Testing**

### **One-Tap ZESCO Payments**

**Test Scenario**: Electricity bill payment in Copperbelt

```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=copperbelt
```

**Test Steps**:
1. **Register Account**: Add ZESCO account **********
2. **Enable Auto-Alerts**: Set 7-day advance notice
3. **Simulate Bill**: Create overdue bill of K180
4. **Receive Alert**: SMS alert in Bemba language
5. **One-Tap Pay**: Pay directly from SMS token
6. **Offline Receipt**: Verify receipt stored locally

**Expected Results**:
- ✅ Bill inquiry works with ZESCO API
- ✅ SMS alert sent 7 days before due date
- ✅ One-tap payment from SMS
- ✅ Offline receipt accessible anytime

### **Auto-Alerts Before Disconnection**

**Test Scenario**: NWSC water bill in Lusaka

```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=lusaka
```

**Test Steps**:
1. **Register LWSC Account**: Add water account WTR123456
2. **Set Preferences**: SMS alerts in English, 3-day notice
3. **Simulate Due Bill**: K120 due in 2 days
4. **Receive Alert**: "FINAL NOTICE" SMS with payment link
5. **Quick Payment**: Pay via SMS token PAY ABC123
6. **Confirmation**: Receive payment confirmation

**Expected Results**:
- ✅ Alert sent 3 days before disconnection
- ✅ Escalating urgency (First → Final → Urgent)
- ✅ Payment link in SMS works
- ✅ Confirmation prevents disconnection

## **4. Financial Inclusion Testing**

### **Tiered KYC System**

**Test Scenario**: Rural user progression from Basic to Full KYC

```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=southern_province
```

**Test Steps**:
1. **Basic Registration**: Register with phone ************ only
2. **Verify Limits**: Daily limit K1,000, monthly K10,000
3. **Test Transaction**: Send K500 (should work)
4. **Test Limit**: Try sending K1,500 (should fail)
5. **Upload NRC**: Add National ID for Intermediate tier
6. **New Limits**: Daily K10,000, monthly K100,000
7. **Upload Selfie**: Complete biometric verification
8. **Full Features**: Access all payment features

**Expected Results**:
- ✅ Basic tier: Phone verification only
- ✅ Intermediate tier: NRC + selfie required
- ✅ Full tier: All documents required
- ✅ Limits enforced automatically
- ✅ Smooth tier progression

### **Emergency Airtime Advances**

**Test Scenario**: Drought response mode in Southern Province

```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=southern_province --dart-define=TEST_MODE=true
```

**Test Steps**:
1. **Enable Drought Mode**: Activate emergency features
2. **Low Balance**: User has K2 airtime remaining
3. **Request Advance**: Request K10 emergency airtime
4. **Community Verification**: 2 guarantors approve
5. **Instant Airtime**: K10 airtime credited immediately
6. **Repayment Plan**: K12 deducted from next mobile money receipt

**Expected Results**:
- ✅ Emergency mode activated for drought areas
- ✅ Community guarantor system works
- ✅ Instant airtime delivery
- ✅ Automatic repayment from future transactions

## **Testing Regions and Scenarios**

### **Eastern Province** (Primary Test Region)
- **Language**: Nyanja voice guidance
- **Challenges**: Poor connectivity, seasonal floods
- **Focus**: SMS tokens, offline functionality
- **Test Accounts**: ZESCO **********

### **Western Province** (Rural Resilience)
- **Language**: Lozi voice guidance  
- **Challenges**: Very poor connectivity, remote locations
- **Focus**: 2G network support, voice UI
- **Test Accounts**: ZESCO **********

### **Copperbelt** (Urban Testing)
- **Language**: Bemba voice guidance
- **Challenges**: High transaction volume
- **Focus**: Chilimba groups, merchant payments
- **Test Accounts**: ZESCO **********, NWASCO CB123456

### **Southern Province** (Drought Testing)
- **Language**: Tonga voice guidance
- **Challenges**: Drought-prone, agricultural dependency
- **Focus**: Emergency airtime, tiered KYC
- **Test Accounts**: ZESCO **********

## **Success Metrics**

### **Phase 1 Targets** (30-day testing period)

| Metric | Target | Measurement |
|--------|--------|-------------|
| User Registration Rate | 80% | Invited users who complete registration |
| Transaction Success Rate | 95% | Successful transactions / total attempts |
| Offline Sync Success Rate | 90% | Offline transactions that sync successfully |
| Voice Guidance Usage Rate | 60% | Users who enable voice guidance |
| SMS Token Redemption Rate | 85% | SMS tokens successfully redeemed |
| Utility Payment Success Rate | 92% | Successful utility payments |

### **Regional Performance Targets**

| Region | Connectivity | Target Success Rate |
|--------|-------------|-------------------|
| Lusaka | HIGH (4G/5G) | 98% |
| Copperbelt | HIGH (4G/5G) | 98% |
| Eastern Province | MEDIUM (3G/4G) | 95% |
| Southern Province | MEDIUM (3G/4G) | 95% |
| Western Province | LOW (2G/3G) | 90% |

## **Test Data and Accounts**

### **Test Phone Numbers by Region**
- **Lusaka**: ************, ************, ************
- **Copperbelt**: ************, ************, ************  
- **Eastern**: ************, ************
- **Southern**: ************, ************, ************
- **Western**: ************, ************

### **Test Utility Accounts**
- **ZESCO**: **********, **********, **********, **********, **********
- **LWSC**: WTR123456, WTR789012
- **NWASCO**: CB123456, CB789012

## **Running Specific Test Scenarios**

### **Flood Simulation** (Eastern Province)
```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=eastern_province --dart-define=FLOOD_MODE=true
```

### **Drought Response** (Southern Province)  
```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=southern_province --dart-define=DROUGHT_MODE=true
```

### **Low Connectivity** (Western Province)
```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=western_province --dart-define=CONNECTIVITY=2G
```

### **High Volume** (Copperbelt)
```bash
flutter run --dart-define=ENV=sandbox --dart-define=REGION=copperbelt --dart-define=LOAD_TEST=true
```

## **Monitoring and Reporting**

### **Real-Time Monitoring**
- Transaction success rates by region
- SMS token generation and redemption
- Voice guidance usage statistics
- Offline sync performance
- Utility payment completion rates

### **Daily Reports**
- Regional performance summary
- Feature adoption rates
- Error logs and resolution
- User feedback compilation
- Compliance status updates

### **Weekly Reviews**
- Success metric progress
- Regional challenge identification
- Feature improvement recommendations
- Next phase planning updates

## **Next Steps After Phase 1**

1. **Phase 2 Expansion**: Additional provinces and features
2. **Performance Optimization**: Based on Phase 1 results
3. **Regulatory Approval**: Bank of Zambia submission
4. **Production Deployment**: Gradual rollout strategy
5. **Community Training**: User education programs

---

**🇿🇲 Built for Zambia, tested in Zambia, by Zambians**
