# Simple verification of Zambia Pay components
Write-Host "Zambia Pay - System Verification" -ForegroundColor Green

$scripts = @(
    "zambia_validation_suite.ps1",
    "live_zambia_test.ps1",
    "safety_override.ps1",
    "launch_dashboard.ps1",
    "execute_zambia_sequence.ps1"
)

$found = 0
foreach ($script in $scripts) {
    if (Test-Path $script) {
        Write-Host "[PASS] $script exists" -ForegroundColor Green
        $found++
    } else {
        Write-Host "[FAIL] $script missing" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Components found: $found/5" -ForegroundColor $(if ($found -eq 5) { "Green" } else { "Yellow" })

if ($found -eq 5) {
    Write-Host "System is ready for execution!" -ForegroundColor Green
    Write-Host "Run: .\execute_zambia_sequence.ps1" -ForegroundColor Yellow
} else {
    Write-Host "Some components are missing" -ForegroundColor Yellow
}
