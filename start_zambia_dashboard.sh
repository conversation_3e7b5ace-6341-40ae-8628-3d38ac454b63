#!/bin/bash

# Zambia Pay Real-Time Dashboard System
# Monitors transaction success, notification delays, and agent accuracy
# Usage: ./start_zambia_dashboard.sh --metrics="tx_success,notification_delay,agent_accuracy" --thresholds="99.9%,<5s,>95%" --alert-number=+26096XXXXXXX

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Default configuration
DEFAULT_METRICS="tx_success,notification_delay,agent_accuracy"
DEFAULT_THRESHOLDS="99.9%,<5s,>95%"
DEFAULT_ALERT_NUMBER="+260961234567"
REFRESH_INTERVAL=5
DASHBOARD_PORT=8080
LOG_FILE="zambia_dashboard_$(date +%Y%m%d_%H%M%S).log"

# Configuration variables
METRICS="$DEFAULT_METRICS"
THRESHOLDS="$DEFAULT_THRESHOLDS"
ALERT_NUMBER="$DEFAULT_ALERT_NUMBER"
CONTINUOUS_MODE=true
DASHBOARD_TITLE="🇿🇲 ZAMBIA PAY LIVE DASHBOARD"

print_info() {
    echo -e "${BLUE}[DASHBOARD]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ALERT]${NC} $1" | tee -a "$LOG_FILE"
}

print_metric() {
    echo -e "${CYAN}[METRIC]${NC} $1" | tee -a "$LOG_FILE"
}

# Parse command line arguments
parse_arguments() {
    for arg in "$@"; do
        case $arg in
            --metrics=*)
                METRICS="${arg#*=}"
                shift
                ;;
            --thresholds=*)
                THRESHOLDS="${arg#*=}"
                shift
                ;;
            --alert-number=*)
                ALERT_NUMBER="${arg#*=}"
                shift
                ;;
            --interval=*)
                REFRESH_INTERVAL="${arg#*=}"
                shift
                ;;
            --port=*)
                DASHBOARD_PORT="${arg#*=}"
                shift
                ;;
            --once)
                CONTINUOUS_MODE=false
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown argument: $arg"
                show_help
                exit 1
                ;;
        esac
    done
}

show_help() {
    echo "Zambia Pay Dashboard System"
    echo "=========================="
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --metrics=LIST           Comma-separated metrics to monitor"
    echo "                          (default: tx_success,notification_delay,agent_accuracy)"
    echo "  --thresholds=LIST        Comma-separated thresholds for alerts"
    echo "                          (default: 99.9%,<5s,>95%)"
    echo "  --alert-number=PHONE     Phone number for SMS alerts"
    echo "                          (default: +260961234567)"
    echo "  --interval=SECONDS       Refresh interval in seconds (default: 5)"
    echo "  --port=PORT             Dashboard web port (default: 8080)"
    echo "  --once                  Run once instead of continuous monitoring"
    echo "  --help                  Show this help message"
    echo ""
    echo "Supported Metrics:"
    echo "  tx_success              Transaction success rate (%)"
    echo "  notification_delay      SMS/Push notification delay (seconds)"
    echo "  agent_accuracy          Agent location/data accuracy (%)"
    echo "  system_uptime           System uptime (%)"
    echo "  api_response_time       API response time (ms)"
    echo "  user_satisfaction       User satisfaction score (%)"
    echo ""
    echo "Threshold Format:"
    echo "  Percentage:             99.9%, >95%, <90%"
    echo "  Time:                   <5s, >10s, <500ms"
    echo "  Count:                  >100, <50"
    echo ""
    echo "Examples:"
    echo "  $0 --metrics=\"tx_success,notification_delay\" --thresholds=\"99.9%,<5s\""
    echo "  $0 --alert-number=\"+260971234567\" --interval=10"
}

# Initialize dashboard
initialize_dashboard() {
    print_info "Initializing Zambia Pay Dashboard..."
    
    echo "Zambia Pay Dashboard Started: $(date)" > "$LOG_FILE"
    echo "Metrics: $METRICS" >> "$LOG_FILE"
    echo "Thresholds: $THRESHOLDS" >> "$LOG_FILE"
    echo "Alert Number: $ALERT_NUMBER" >> "$LOG_FILE"
    echo "========================================" >> "$LOG_FILE"
    
    # Parse metrics and thresholds
    IFS=',' read -ra METRIC_ARRAY <<< "$METRICS"
    IFS=',' read -ra THRESHOLD_ARRAY <<< "$THRESHOLDS"
    
    # Validate configuration
    if [ ${#METRIC_ARRAY[@]} -ne ${#THRESHOLD_ARRAY[@]} ]; then
        print_error "Number of metrics (${#METRIC_ARRAY[@]}) doesn't match number of thresholds (${#THRESHOLD_ARRAY[@]})"
        exit 1
    fi
    
    print_success "Dashboard initialized successfully"
    print_info "Monitoring ${#METRIC_ARRAY[@]} metrics"
    print_info "Alert number: $ALERT_NUMBER"
    print_info "Refresh interval: ${REFRESH_INTERVAL}s"
}

# Get transaction success rate
get_tx_success_rate() {
    # In production, query actual database:
    # local total=$(sqlite3 payments.db "SELECT COUNT(*) FROM transactions WHERE created_at > datetime('now', '-5 minutes')")
    # local successful=$(sqlite3 payments.db "SELECT COUNT(*) FROM transactions WHERE created_at > datetime('now', '-5 minutes') AND status = 'COMPLETED'")
    
    # Simulate realistic data with some variance
    local base_rate=99.5
    local variance=$((RANDOM % 10 - 5))  # -5 to +5
    local rate=$(echo "$base_rate + $variance * 0.1" | awk '{print $1 + $2}')
    
    # Ensure rate is between 95 and 100
    if (( $(echo "$rate > 100" | awk '{print ($1 > 100)}') )); then
        rate=99.9
    elif (( $(echo "$rate < 95" | awk '{print ($1 < 95)}') )); then
        rate=95.5
    fi
    
    printf "%.1f" "$rate"
}

# Get notification delay
get_notification_delay() {
    # In production, query notification service:
    # local avg_delay=$(sqlite3 notifications.db "SELECT AVG(delivery_time - created_time) FROM notifications WHERE created_at > datetime('now', '-5 minutes')")
    
    # Simulate notification delays (in seconds)
    local base_delay=2.5
    local variance=$((RANDOM % 6))  # 0 to 5
    local delay=$(echo "$base_delay + $variance * 0.5" | awk '{print $1 + $2}')
    
    printf "%.1f" "$delay"
}

# Get agent accuracy
get_agent_accuracy() {
    # In production, query agent service:
    # local total_agents=$(sqlite3 agents.db "SELECT COUNT(*) FROM agent_locations WHERE updated_at > datetime('now', '-1 hour')")
    # local accurate_agents=$(sqlite3 agents.db "SELECT COUNT(*) FROM agent_locations WHERE updated_at > datetime('now', '-1 hour') AND accuracy_score > 0.95")
    
    # Simulate agent accuracy
    local base_accuracy=96.5
    local variance=$((RANDOM % 6 - 3))  # -3 to +3
    local accuracy=$(echo "$base_accuracy + $variance * 0.5" | awk '{print $1 + $2}')
    
    # Ensure accuracy is between 90 and 100
    if (( $(echo "$accuracy > 100" | awk '{print ($1 > 100)}') )); then
        accuracy=99.5
    elif (( $(echo "$accuracy < 90" | awk '{print ($1 < 90)}') )); then
        accuracy=92.0
    fi
    
    printf "%.1f" "$accuracy"
}

# Get system uptime
get_system_uptime() {
    # In production, check actual system uptime
    # uptime | awk '{print $3}' | sed 's/,//'
    
    # Simulate high uptime with occasional dips
    local base_uptime=99.8
    local variance=$((RANDOM % 4))  # 0 to 3
    local uptime=$(echo "$base_uptime - $variance * 0.1" | awk '{print $1 - $2}')
    
    printf "%.1f" "$uptime"
}

# Get API response time
get_api_response_time() {
    # In production, check actual API metrics
    # curl -w "%{time_total}" -s -o /dev/null https://api.zambiapay.com/health
    
    # Simulate API response times (in milliseconds)
    local base_time=150
    local variance=$((RANDOM % 100))  # 0 to 100
    local response_time=$((base_time + variance))
    
    echo "$response_time"
}

# Get user satisfaction
get_user_satisfaction() {
    # In production, query user feedback database
    # local avg_rating=$(sqlite3 feedback.db "SELECT AVG(rating) FROM user_feedback WHERE created_at > datetime('now', '-24 hours')")
    
    # Simulate user satisfaction
    local base_satisfaction=94.0
    local variance=$((RANDOM % 8 - 4))  # -4 to +4
    local satisfaction=$(echo "$base_satisfaction + $variance * 0.5" | awk '{print $1 + $2}')
    
    printf "%.1f" "$satisfaction"
}

# Get metric value
get_metric_value() {
    local metric="$1"
    
    case "$metric" in
        "tx_success")
            get_tx_success_rate
            ;;
        "notification_delay")
            get_notification_delay
            ;;
        "agent_accuracy")
            get_agent_accuracy
            ;;
        "system_uptime")
            get_system_uptime
            ;;
        "api_response_time")
            get_api_response_time
            ;;
        "user_satisfaction")
            get_user_satisfaction
            ;;
        *)
            echo "0"
            ;;
    esac
}

# Check if metric meets threshold
check_threshold() {
    local metric="$1"
    local value="$2"
    local threshold="$3"
    
    # Parse threshold
    local operator=""
    local threshold_value=""
    
    if [[ "$threshold" =~ ^([<>]=?|=)(.+)$ ]]; then
        operator="${BASH_REMATCH[1]}"
        threshold_value="${BASH_REMATCH[2]}"
    elif [[ "$threshold" =~ ^(.+)%$ ]]; then
        operator=">="
        threshold_value="${BASH_REMATCH[1]}"
    else
        operator=">="
        threshold_value="$threshold"
    fi
    
    # Remove units for comparison
    threshold_value=$(echo "$threshold_value" | sed 's/[%s]//g')
    local numeric_value=$(echo "$value" | sed 's/[%s]//g')
    
    # Perform comparison
    case "$operator" in
        ">")
            (( $(echo "$numeric_value > $threshold_value" | awk '{print ($1 > $2)}') ))
            ;;
        ">=")
            (( $(echo "$numeric_value >= $threshold_value" | awk '{print ($1 >= $2)}') ))
            ;;
        "<")
            (( $(echo "$numeric_value < $threshold_value" | awk '{print ($1 < $2)}') ))
            ;;
        "<=")
            (( $(echo "$numeric_value <= $threshold_value" | awk '{print ($1 <= $2)}') ))
            ;;
        "=")
            (( $(echo "$numeric_value == $threshold_value" | awk '{print ($1 == $2)}') ))
            ;;
        *)
            (( $(echo "$numeric_value >= $threshold_value" | awk '{print ($1 >= $2)}') ))
            ;;
    esac
}

# Send SMS alert
send_sms_alert() {
    local message="$1"
    local phone="$ALERT_NUMBER"
    
    # Remove + from phone number for processing
    local clean_phone="${phone#+}"
    
    # Determine network based on Zambian prefixes
    local network=""
    case "${clean_phone:3:2}" in
        "96"|"76"|"86")
            network="MTN"
            ;;
        "97"|"77"|"87")
            network="Airtel"
            ;;
        "95"|"75"|"85")
            network="Zamtel"
            ;;
        *)
            network="Unknown"
            ;;
    esac
    
    print_error "📱 Sending alert to $phone ($network): $message"
    
    # In production, integrate with actual SMS APIs
    # For demo, simulate SMS sending
    return 0
}

# Clear screen and show header
show_dashboard_header() {
    clear
    echo -e "${MAGENTA}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${MAGENTA}║${NC}                          ${DASHBOARD_TITLE}                           ${MAGENTA}║${NC}"
    echo -e "${MAGENTA}╠══════════════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${MAGENTA}║${NC} Last Updated: $(date '+%Y-%m-%d %H:%M:%S')                                        ${MAGENTA}║${NC}"
    echo -e "${MAGENTA}║${NC} Alert Number: $ALERT_NUMBER                                              ${MAGENTA}║${NC}"
    echo -e "${MAGENTA}║${NC} Refresh Rate: ${REFRESH_INTERVAL}s                                                      ${MAGENTA}║${NC}"
    echo -e "${MAGENTA}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# Display metric status
display_metric() {
    local metric="$1"
    local threshold="$2"
    local value
    local status_icon
    local status_color
    local unit=""

    value=$(get_metric_value "$metric")

    # Determine unit based on metric type
    case "$metric" in
        "tx_success"|"agent_accuracy"|"system_uptime"|"user_satisfaction")
            unit="%"
            ;;
        "notification_delay")
            unit="s"
            ;;
        "api_response_time")
            unit="ms"
            ;;
    esac

    # Check if threshold is met
    if check_threshold "$metric" "$value" "$threshold"; then
        status_icon="✅"
        status_color="$GREEN"
    else
        status_icon="🚨"
        status_color="$RED"

        # Send alert for failed threshold
        local alert_message="ALERT: $metric is ${value}${unit} (threshold: $threshold)"
        send_sms_alert "$alert_message"
    fi

    # Format metric name for display
    local display_name
    case "$metric" in
        "tx_success")
            display_name="Transaction Success Rate"
            ;;
        "notification_delay")
            display_name="Notification Delay"
            ;;
        "agent_accuracy")
            display_name="Agent Accuracy"
            ;;
        "system_uptime")
            display_name="System Uptime"
            ;;
        "api_response_time")
            display_name="API Response Time"
            ;;
        "user_satisfaction")
            display_name="User Satisfaction"
            ;;
        *)
            display_name="$metric"
            ;;
    esac

    printf "%-25s %s %s%8s%s%s (threshold: %s)\n" \
           "$display_name" "$status_icon" "$status_color" "$value" "$unit" "$NC" "$threshold"
}

# Display all metrics
display_metrics() {
    echo -e "${CYAN}📊 SYSTEM METRICS${NC}"
    echo "════════════════════════════════════════════════════════════════════════════"

    for i in "${!METRIC_ARRAY[@]}"; do
        display_metric "${METRIC_ARRAY[i]}" "${THRESHOLD_ARRAY[i]}"
    done

    echo
}

# Display system status summary
display_system_status() {
    local healthy_count=0
    local total_count=${#METRIC_ARRAY[@]}

    # Count healthy metrics
    for i in "${!METRIC_ARRAY[@]}"; do
        local value
        value=$(get_metric_value "${METRIC_ARRAY[i]}")
        if check_threshold "${METRIC_ARRAY[i]}" "$value" "${THRESHOLD_ARRAY[i]}"; then
            ((healthy_count++))
        fi
    done

    local health_percentage=$(( (healthy_count * 100) / total_count ))
    local status_color
    local status_text

    if [ "$health_percentage" -ge 90 ]; then
        status_color="$GREEN"
        status_text="🟢 HEALTHY"
    elif [ "$health_percentage" -ge 70 ]; then
        status_color="$YELLOW"
        status_text="🟡 WARNING"
    else
        status_color="$RED"
        status_text="🔴 CRITICAL"
    fi

    echo -e "${CYAN}🏥 SYSTEM HEALTH${NC}"
    echo "════════════════════════════════════════════════════════════════════════════"
    echo -e "Overall Status: ${status_color}${status_text}${NC}"
    echo -e "Healthy Metrics: ${healthy_count}/${total_count} (${health_percentage}%)"
    echo
}

# Display recent alerts
display_recent_alerts() {
    echo -e "${CYAN}🚨 RECENT ALERTS${NC}"
    echo "════════════════════════════════════════════════════════════════════════════"

    # Show last 5 alerts from log file
    if [ -f "$LOG_FILE" ]; then
        grep "\[ALERT\]" "$LOG_FILE" | tail -5 | while read -r line; do
            echo "$line"
        done
    else
        echo "No recent alerts"
    fi
    echo
}

# Display Zambian network status
display_network_status() {
    echo -e "${CYAN}📡 ZAMBIAN NETWORK STATUS${NC}"
    echo "════════════════════════════════════════════════════════════════════════════"

    # Simulate network status checks
    local networks=("MTN Zambia" "Airtel Zambia" "Zamtel")
    local statuses=("🟢 OPERATIONAL" "🟢 OPERATIONAL" "🟡 DEGRADED")

    for i in "${!networks[@]}"; do
        printf "%-15s %s\n" "${networks[i]}" "${statuses[i]}"
    done
    echo
}

# Display footer with controls
display_footer() {
    echo "════════════════════════════════════════════════════════════════════════════"
    echo -e "${BLUE}Controls:${NC} Press Ctrl+C to stop monitoring | Refresh every ${REFRESH_INTERVAL}s"
    echo -e "${BLUE}Log File:${NC} $LOG_FILE"
    echo
}

# Main dashboard display
show_dashboard() {
    show_dashboard_header
    display_metrics
    display_system_status
    display_recent_alerts
    display_network_status
    display_footer
}

# Generate HTML dashboard
generate_html_dashboard() {
    local html_file="zambia_dashboard.html"

    cat > "$html_file" << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zambia Pay Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #2E7D32; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; }
        .healthy { color: #4CAF50; }
        .warning { color: #FF9800; }
        .critical { color: #F44336; }
        .status { text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; }
    </style>
    <script>
        function refreshDashboard() {
            location.reload();
        }
        setInterval(refreshDashboard, ${REFRESH_INTERVAL}000);
    </script>
</head>
<body>
    <div class="header">
        <h1>🇿🇲 Zambia Pay Live Dashboard</h1>
        <p>Last Updated: $(date)</p>
    </div>

    <div class="metrics">
EOF

    # Add metric cards
    for i in "${!METRIC_ARRAY[@]}"; do
        local metric="${METRIC_ARRAY[i]}"
        local threshold="${THRESHOLD_ARRAY[i]}"
        local value
        value=$(get_metric_value "$metric")

        local status_class="healthy"
        if ! check_threshold "$metric" "$value" "$threshold"; then
            status_class="critical"
        fi

        cat >> "$html_file" << EOF
        <div class="metric-card">
            <h3>$metric</h3>
            <div class="metric-value $status_class">$value</div>
            <p>Threshold: $threshold</p>
        </div>
EOF
    done

    cat >> "$html_file" << EOF
    </div>

    <div class="footer">
        <p>Zambia Pay Dashboard - Auto-refresh every ${REFRESH_INTERVAL} seconds</p>
    </div>
</body>
</html>
EOF

    print_info "HTML dashboard generated: $html_file"
}

# Start web server for dashboard
start_web_dashboard() {
    print_info "Web dashboard generation enabled"

    # Generate initial HTML (skip web server for now)
    if generate_html_dashboard; then
        print_success "HTML dashboard generated successfully"
    else
        print_warning "HTML dashboard generation failed"
    fi
}

# Main monitoring loop
run_dashboard() {
    print_info "Starting Zambia Pay Dashboard monitoring..."

    # Start web dashboard
    start_web_dashboard

    if [ "$CONTINUOUS_MODE" = true ]; then
        print_info "Continuous monitoring mode - Press Ctrl+C to stop"

        # Set up signal handler for graceful shutdown
        trap 'cleanup_and_exit' INT TERM

        while true; do
            show_dashboard

            # Update HTML dashboard
            generate_html_dashboard

            sleep "$REFRESH_INTERVAL"
        done
    else
        print_info "Single run mode"
        show_dashboard
        generate_html_dashboard
    fi
}

# Cleanup function
cleanup_and_exit() {
    print_info "Shutting down dashboard..."

    # Stop web server
    if [ -f "dashboard_server.pid" ]; then
        local server_pid
        server_pid=$(cat dashboard_server.pid)
        kill "$server_pid" 2>/dev/null || true
        rm -f dashboard_server.pid
        print_info "Web server stopped"
    fi

    print_success "Dashboard shutdown complete"
    exit 0
}

# Main execution
main() {
    parse_arguments "$@"
    initialize_dashboard
    run_dashboard
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
