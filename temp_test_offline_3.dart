import 'dart:async';
import 'dart:math';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('offline Network Test 3', () {
    test('should simulate offline conditions', () async {
      // Network simulation
      final speed = 0;
      final latency = 9999;
      final packetLoss = 100;
      
      var successCount = 0;
      var failureCount = 0;
      
      // Simulate 5 network requests
      for (int i = 0; i < 5; i++) {
        try {
          // Simulate network delay
          await Future.delayed(Duration(milliseconds: latency ~/ 10));
          
          // Simulate packet loss
          if (Random().nextInt(100) < packetLoss) {
            throw Exception('Packet lost');
          }
          
          successCount++;
          print('Request \3 succeeded on offline');
          
        } catch (e) {
          failureCount++;
          print('Request \3 failed on offline - \');
        }
      }
      
      // Validate based on network type
      if (speed > 0) {
        expect(successCount, greaterThan(0), 
               reason: 'Should have some success on offline');
      } else {
        expect(failureCount, equals(5), 
               reason: 'Should fail all requests when offline');
      }
      
      print('offline test completed - Success: \, Failures: \');
    });
  });
}
