import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/mobile_money/mobile_money_service.dart';

void main() {
  group('Transaction Validation Tests', () {
    late MobileMoneyService service;

    setUp(() {
      service = MobileMoneyService();
    });

    test('should validate amount ranges', () {
      expect(service.validateAmount('MTN', 10.0), isTrue);
      expect(service.validateAmount('MTN', 50000.0), isTrue);
      expect(service.validateAmount('MTN', 5.0), isFalse);
      expect(service.validateAmount('MTN', 60000.0), isFalse);
    });

    test('should calculate Zambian mobile money levy', () {
      final fee = service.calculateFee('MTN', 1000.0);
      expect(fee, equals(2.1)); // 0.21% levy
    });
  });
}
