import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:zambia_pay/core/refresh/smart_refresh_controller.dart';
import 'package:zambia_pay/core/refresh/connection_aware_refresher.dart';
import 'package:zambia_pay/core/network/network_quality_detector.dart';
import 'package:zambia_pay/core/data_usage/data_usage_monitor.dart';

void main() {
  group('Smart Refresh System Tests', () {
    late SmartRefreshController smartController;
    late ConnectionAwareRefresher connectionRefresher;
    late NetworkQualityDetector networkDetector;
    late DataUsageMonitor dataMonitor;

    setUp(() {
      smartController = SmartRefreshController();
      connectionRefresher = ConnectionAwareRefresher();
      networkDetector = NetworkQualityDetector();
      dataMonitor = DataUsageMonitor();
    });

    tearDown(() {
      smartController.dispose();
      connectionRefresher.dispose();
    });

    group('SmartRefreshController Tests', () {
      test('should initialize successfully', () async {
        expect(() => smartController.initialize(), returnsNormally);
      });

      test('should handle exponential backoff correctly', () async {
        // Simulate multiple failures
        var failureCount = 0;
        
        final result = await smartController.smartRefresh(
          refreshFunction: () async {
            failureCount++;
            if (failureCount < 3) {
              throw Exception('Simulated failure $failureCount');
            }
            return 'success';
          },
          fallbackFunction: () async => 'fallback',
          operationName: 'test_operation',
        );

        // Should eventually succeed or fallback
        expect(result, isA<String>());
      });

      test('should respect data usage limits', () async {
        // Reset data usage
        await dataMonitor.resetAllUsage();
        
        // Simulate high data usage
        await dataMonitor.trackDataUsage(
          dataUsedMB: 1.9, // Close to 2MB limit
          category: 'test',
        );

        final result = await smartController.smartRefresh(
          refreshFunction: () async => 'fresh_data',
          fallbackFunction: () async => 'cached_data',
          operationName: 'high_data_operation',
        );

        // Should use fallback due to data limits
        expect(result, equals('cached_data'));
      });

      test('should handle network quality changes', () async {
        // Test with different network qualities
        final qualities = [
          NetworkQuality.excellent,
          NetworkQuality.good,
          NetworkQuality.fair,
          NetworkQuality.poor,
          NetworkQuality.offline,
        ];

        for (final quality in qualities) {
          // This would require mocking the network detector
          // For now, we test that the controller handles different scenarios
          expect(smartController.canRefresh, isA<bool>());
        }
      });
    });

    group('Connection-Aware Refresher Tests', () {
      test('should provide different strategies for network qualities', () {
        final excellentStrategy = connectionRefresher.getStrategyForQuality(NetworkQuality.excellent);
        final poorStrategy = connectionRefresher.getStrategyForQuality(NetworkQuality.poor);

        expect(excellentStrategy, isNotNull);
        expect(poorStrategy, isNotNull);
        
        // Excellent should have shorter refresh interval
        expect(excellentStrategy!.refreshInterval.inSeconds, 
               lessThan(poorStrategy!.refreshInterval.inSeconds));
        
        // Poor should have longer timeout
        expect(poorStrategy.requestTimeout.inSeconds,
               greaterThan(excellentStrategy.requestTimeout.inSeconds));
      });

      test('should adapt refresh behavior based on network quality', () async {
        final recommendations = connectionRefresher.getRefreshRecommendations();
        
        expect(recommendations, containsKey('networkQuality'));
        expect(recommendations, containsKey('strategy'));
        expect(recommendations, containsKey('recommendations'));
      });

      test('should handle retry logic correctly', () async {
        var attemptCount = 0;
        
        final result = await connectionRefresher.performRefresh(
          refreshFunction: () async {
            attemptCount++;
            if (attemptCount < 2) {
              throw Exception('Simulated failure');
            }
            return 'success_after_retry';
          },
          fallbackFunction: () async => 'fallback',
          operationName: 'retry_test',
        );

        expect(attemptCount, greaterThan(1));
        expect(result, isA<String>());
      });
    });

    group('Network Quality Detector Tests', () {
      test('should detect network quality', () async {
        expect(() => networkDetector.initialize(), returnsNormally);
        expect(networkDetector.networkQuality, isA<NetworkQuality>());
      });

      test('should provide appropriate settings for each quality', () {
        final qualities = NetworkQuality.values;
        
        for (final quality in qualities) {
          final settings = networkDetector.getRecommendedSettings();
          expect(settings, isNotNull);
          expect(settings.requestTimeout, isA<Duration>());
          expect(settings.maxConcurrentRequests, isA<int>());
        }
      });

      test('should determine if network is suitable for operations', () {
        final operations = ['critical', 'important', 'normal', 'background'];
        
        for (final operation in operations) {
          final suitable = networkDetector.isSuitableForOperation(operation);
          expect(suitable, isA<bool>());
        }
      });
    });

    group('Data Usage Monitor Tests', () {
      test('should track data usage correctly', () async {
        await dataMonitor.resetAllUsage();
        
        await dataMonitor.trackDataUsage(
          dataUsedMB: 0.5,
          category: 'test',
          operation: 'test_operation',
        );

        expect(dataMonitor.monthlyDataUsage, equals(0.5));
        expect(dataMonitor.canUseData, isTrue);
      });

      test('should enforce data limits', () async {
        await dataMonitor.resetAllUsage();
        
        // Use most of the monthly limit
        await dataMonitor.trackDataUsage(
          dataUsedMB: 1.95,
          category: 'test',
        );

        // Should not allow more data usage
        final canUse = dataMonitor.canUseDataForOperation(0.1);
        expect(canUse, isFalse);
      });

      test('should provide accurate usage estimates', () {
        final operations = [
          'balance_check',
          'transaction_list',
          'send_money',
          'full_refresh',
        ];

        for (final operation in operations) {
          final estimate = dataMonitor.getEstimatedDataUsage(operation);
          expect(estimate, greaterThan(0.0));
          expect(estimate, lessThan(1.0)); // Should be reasonable
        }
      });

      test('should handle monthly reset correctly', () async {
        await dataMonitor.resetAllUsage();
        
        // Add some usage
        await dataMonitor.trackDataUsage(
          dataUsedMB: 1.0,
          category: 'test',
        );

        expect(dataMonitor.monthlyDataUsage, equals(1.0));
        
        // Reset and verify
        await dataMonitor.resetAllUsage();
        expect(dataMonitor.monthlyDataUsage, equals(0.0));
      });
    });

    group('Integration Tests', () {
      test('should work together for 2G network simulation', () async {
        // Simulate 2G network conditions
        final result = await connectionRefresher.performRefresh(
          refreshFunction: () async {
            // Simulate slow network response
            await Future.delayed(const Duration(seconds: 2));
            return 'slow_network_data';
          },
          fallbackFunction: () async => 'cached_data',
          operationName: '2g_test',
        );

        expect(result, isA<String>());
      });

      test('should work together for 3G network simulation', () async {
        // Simulate 3G network conditions
        final result = await connectionRefresher.performRefresh(
          refreshFunction: () async {
            // Simulate moderate network response
            await Future.delayed(const Duration(milliseconds: 800));
            return 'moderate_network_data';
          },
          fallbackFunction: () async => 'cached_data',
          operationName: '3g_test',
        );

        expect(result, isA<String>());
      });

      test('should work together for 4G network simulation', () async {
        // Simulate 4G network conditions
        final result = await connectionRefresher.performRefresh(
          refreshFunction: () async {
            // Simulate fast network response
            await Future.delayed(const Duration(milliseconds: 200));
            return 'fast_network_data';
          },
          fallbackFunction: () async => 'cached_data',
          operationName: '4g_test',
        );

        expect(result, isA<String>());
      });

      test('should work together for WiFi network simulation', () async {
        // Simulate WiFi network conditions
        final result = await connectionRefresher.performRefresh(
          refreshFunction: () async {
            // Simulate very fast network response
            await Future.delayed(const Duration(milliseconds: 50));
            return 'wifi_network_data';
          },
          fallbackFunction: () async => 'cached_data',
          operationName: 'wifi_test',
        );

        expect(result, isA<String>());
      });

      test('should handle offline scenario correctly', () async {
        // Simulate offline conditions
        final result = await connectionRefresher.performRefresh(
          refreshFunction: () async {
            throw Exception('No internet connection');
          },
          fallbackFunction: () async => 'offline_cached_data',
          operationName: 'offline_test',
        );

        expect(result, equals('offline_cached_data'));
      });

      test('should respect data limits across all network types', () async {
        await dataMonitor.resetAllUsage();
        
        // Use up most of the data limit
        await dataMonitor.trackDataUsage(
          dataUsedMB: 1.98,
          category: 'test',
        );

        // Try to refresh on different network types
        final networkTypes = ['2g', '3g', '4g', 'wifi'];
        
        for (final networkType in networkTypes) {
          final result = await connectionRefresher.performRefresh(
            refreshFunction: () async => 'fresh_data',
            fallbackFunction: () async => 'cached_data',
            operationName: '${networkType}_data_limit_test',
          );

          // Should use cached data due to data limits
          expect(result, equals('cached_data'));
        }
      });
    });

    group('Performance Tests', () {
      test('should handle multiple concurrent refresh requests', () async {
        final futures = <Future>[];
        
        for (int i = 0; i < 5; i++) {
          futures.add(
            connectionRefresher.performRefresh(
              refreshFunction: () async {
                await Future.delayed(Duration(milliseconds: 100 * i));
                return 'concurrent_data_$i';
              },
              fallbackFunction: () async => 'fallback_$i',
              operationName: 'concurrent_test_$i',
            ),
          );
        }

        final results = await Future.wait(futures);
        expect(results.length, equals(5));
      });

      test('should maintain performance under stress', () async {
        final stopwatch = Stopwatch()..start();
        
        for (int i = 0; i < 10; i++) {
          await connectionRefresher.performRefresh(
            refreshFunction: () async => 'stress_data_$i',
            fallbackFunction: () async => 'fallback_$i',
            operationName: 'stress_test_$i',
          );
        }
        
        stopwatch.stop();
        
        // Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });
    });
  });
}
