import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/features/feature_lock.dart';
import '../../lib/wallet/zambia_wallets.dart';
import '../../lib/notifications/momo_alerts.dart';

/// Chilimba Flow Test
/// Tests complete Chilimba group savings flow functionality
void main() {
  group('🇿🇲 Chilimba Flow Tests', () {
    late MomoNotificationService notificationService;
    final networkProfile = Platform.environment['ZAMBIA_NETWORK_PROFILE'] ?? '4g';

    setUp(() async {
      await Features.initialize();
      Features.enable(Features.MOBILE_MONEY);
      Features.enable(Features.CHILIMBA);
      
      await ZambiaWallets.setupWalletOnlyFlow();
      
      notificationService = MomoNotificationService();
      await notificationService.initialize();
      notificationService.configureMomoNotifications();
    });

    test('should handle complete Chilimba flow', () async {
      // Test Chilimba request
      final requestData = {
        'group_name': 'Village Savings Group',
        'amount': 100.0,
        'requester': '<PERSON>',
        'members': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'due_date': '2024-01-15',
      };

      await notificationService.sendNotification(
        userId: 'user_123',
        phoneNumber: '+260961234567',
        type: NotificationType.CHILIMBA_REQUEST,
        data: requestData,
      );

      // Test Chilimba contribution
      final contributionData = {
        'group_name': 'Village Savings Group',
        'amount': 100.0,
        'contributor': 'John Doe',
        'transaction_id': 'chilimba_tx_123',
      };

      await notificationService.sendNotification(
        userId: 'user_456',
        phoneNumber: '+260971234567',
        type: NotificationType.CHILIMBA_CONTRIBUTION,
        data: contributionData,
        transactionId: 'chilimba_tx_123',
      );

      expect(notificationService.isInitialized, true);
    });

    test('should handle Chilimba flow on $networkProfile network', () async {
      final isSlowNetwork = ['2g', 'offline'].contains(networkProfile);
      
      if (isSlowNetwork) {
        // Enable SMS fallback for slow networks
        notificationService.enableDeliveryGuarantee(
          retryPolicy: ZambiaRetryPolicy(
            maxRetries: 3,
            baseDelay: const Duration(seconds: 5),
            maxDelay: const Duration(minutes: 5),
            backoffMultiplier: 2.0,
          ),
          fallback: SMSNotification(),
        );
      }

      final data = {
        'group_name': 'Rural Savings Circle',
        'amount': 50.0,
        'requester': 'Grace Phiri',
        'urgency': isSlowNetwork ? 'high' : 'normal',
      };

      await notificationService.sendNotification(
        userId: 'user_789',
        phoneNumber: '+260951234567', // Zamtel number
        type: NotificationType.CHILIMBA_REQUEST,
        data: data,
      );

      expect(notificationService.hasDeliveryGuarantee, isSlowNetwork);
    });

    test('should support Chilimba across all wallet providers', () async {
      final walletNumbers = {
        '+260961234567': MobileWallet.MTN_MONEY,
        '+260971234567': MobileWallet.AIRTEL_MONEY,
        '+260951234567': MobileWallet.ZAMTEL_KWACHA,
      };

      for (final entry in walletNumbers.entries) {
        final phoneNumber = entry.key;
        final expectedWallet = entry.value;
        
        // Verify wallet detection
        final detectedWallet = ZambiaWallets.getWalletByPhoneNumber(phoneNumber);
        expect(detectedWallet, expectedWallet);

        // Test Chilimba notification for each provider
        final data = {
          'group_name': 'Multi-Provider Group',
          'amount': 75.0,
          'provider': expectedWallet.code,
        };

        await notificationService.sendNotification(
          userId: 'user_${expectedWallet.code}',
          phoneNumber: phoneNumber,
          type: NotificationType.CHILIMBA_REQUEST,
          data: data,
        );
      }

      expect(notificationService.isInitialized, true);
    });

    test('should validate Chilimba feature is enabled', () {
      expect(Features.isEnabled(Features.CHILIMBA), true);
      expect(Features.isEnabled(Features.MOBILE_MONEY), true);
      
      // Banking features should be disabled
      expect(Features.areBankingFeaturesDisabled(), true);
    });

    test('should support Chilimba notification types', () {
      final chilimbaTypes = [
        NotificationType.CHILIMBA_REQUEST,
        NotificationType.CHILIMBA_CONTRIBUTION,
      ];

      notificationService.enableTypes(chilimbaTypes);
      
      for (final type in chilimbaTypes) {
        expect(notificationService.enabledTypes, contains(type));
      }
    });
  });
}
