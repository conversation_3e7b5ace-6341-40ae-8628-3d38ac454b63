import 'package:flutter_test/flutter_test.dart';
import '../../lib/features/feature_lock.dart';

/// Test suite for Pay Mule Zambia Mobile Money MVP Feature Lock System
/// Ensures banking features are disabled and mobile money core is enabled
void main() {
  group('🇿🇲 PAY MULE ZAMBIA MOBILE MONEY MVP - Feature Lock Tests', () {
    setUp(() async {
      // Reset feature states before each test
      await Features.initialize();
    });

    group('Feature Lock Initialization', () {
      test('should initialize feature lock system successfully', () async {
        await Features.initialize();
        
        final status = Features.getFeatureLockStatus();
        expect(status['initialized'], true);
        expect(status['mvp_mode'], 'mobile_money_only');
      });

      test('should disable all banking features on initialization', () async {
        await Features.initialize();
        
        expect(Features.isEnabled(Features.BANK_LINKING), false);
        expect(Features.isEnabled(Features.BANK_TRANSFERS), false);
        expect(Features.isEnabled(Features.BANK_ACCOUNT_MANAGEMENT), false);
        expect(Features.isEnabled(Features.BANK_STATEMENTS), false);
        expect(Features.isEnabled(Features.BANK_CARDS), false);
        
        expect(Features.areBankingFeaturesDisabled(), true);
      });

      test('should enable all mobile money core features on initialization', () async {
        await Features.initialize();
        
        expect(Features.isEnabled(Features.MOBILE_MONEY), true);
        expect(Features.isEnabled(Features.CHILIMBA), true);
        expect(Features.isEnabled(Features.UTILITY_PAYMENTS), true);
        expect(Features.isEnabled(Features.AGENT_LOCATOR), true);
        expect(Features.isEnabled(Features.OFFLINE_SYNC), true);
        expect(Features.isEnabled(Features.QR_PAYMENTS), true);
        expect(Features.isEnabled(Features.TRANSACTION_HISTORY), true);
        expect(Features.isEnabled(Features.BALANCE_INQUIRY), true);
        expect(Features.isEnabled(Features.SEND_MONEY), true);
        expect(Features.isEnabled(Features.RECEIVE_MONEY), true);
        expect(Features.isEnabled(Features.AIRTIME_PURCHASE), true);
        
        expect(Features.isMobileMoneyEnabled(), true);
      });
    });

    group('Banking Features Disabled', () {
      test('should confirm all banking features are disabled', () async {
        await Features.initialize();
        
        final disabledFeatures = Features.getDisabledFeatures();
        
        expect(disabledFeatures.contains(Features.BANK_LINKING), true);
        expect(disabledFeatures.contains(Features.BANK_TRANSFERS), true);
        expect(disabledFeatures.contains(Features.BANK_ACCOUNT_MANAGEMENT), true);
        expect(disabledFeatures.contains(Features.BANK_STATEMENTS), true);
        expect(disabledFeatures.contains(Features.BANK_CARDS), true);
      });

      test('should return false for banking feature checks', () async {
        await Features.initialize();
        
        expect(Features.isEnabled(Features.BANK_LINKING), false);
        expect(Features.isEnabled(Features.BANK_TRANSFERS), false);
        expect(Features.isEnabled(Features.BANK_ACCOUNT_MANAGEMENT), false);
        expect(Features.isEnabled(Features.BANK_STATEMENTS), false);
        expect(Features.isEnabled(Features.BANK_CARDS), false);
      });
    });

    group('Mobile Money Core Enabled', () {
      test('should confirm all mobile money features are enabled', () async {
        await Features.initialize();
        
        final enabledFeatures = Features.getEnabledFeatures();
        
        expect(enabledFeatures.contains(Features.MOBILE_MONEY), true);
        expect(enabledFeatures.contains(Features.CHILIMBA), true);
        expect(enabledFeatures.contains(Features.UTILITY_PAYMENTS), true);
        expect(enabledFeatures.contains(Features.AGENT_LOCATOR), true);
        expect(enabledFeatures.contains(Features.OFFLINE_SYNC), true);
        expect(enabledFeatures.contains(Features.QR_PAYMENTS), true);
        expect(enabledFeatures.contains(Features.TRANSACTION_HISTORY), true);
        expect(enabledFeatures.contains(Features.BALANCE_INQUIRY), true);
        expect(enabledFeatures.contains(Features.SEND_MONEY), true);
        expect(enabledFeatures.contains(Features.RECEIVE_MONEY), true);
        expect(enabledFeatures.contains(Features.AIRTIME_PURCHASE), true);
      });

      test('should return true for mobile money feature checks', () async {
        await Features.initialize();
        
        expect(Features.isEnabled(Features.MOBILE_MONEY), true);
        expect(Features.isEnabled(Features.CHILIMBA), true);
        expect(Features.isEnabled(Features.UTILITY_PAYMENTS), true);
        expect(Features.isEnabled(Features.AGENT_LOCATOR), true);
        expect(Features.isEnabled(Features.OFFLINE_SYNC), true);
        expect(Features.isEnabled(Features.QR_PAYMENTS), true);
        expect(Features.isEnabled(Features.TRANSACTION_HISTORY), true);
        expect(Features.isEnabled(Features.BALANCE_INQUIRY), true);
        expect(Features.isEnabled(Features.SEND_MONEY), true);
        expect(Features.isEnabled(Features.RECEIVE_MONEY), true);
        expect(Features.isEnabled(Features.AIRTIME_PURCHASE), true);
      });
    });

    group('Feature Lock Status', () {
      test('should provide comprehensive feature lock status', () async {
        await Features.initialize();
        
        final status = Features.getFeatureLockStatus();
        
        expect(status['mvp_mode'], 'mobile_money_only');
        expect(status['banking_features_disabled'], true);
        expect(status['mobile_money_enabled'], true);
        expect(status['initialized'], true);
        expect(status['enabled_features'], isA<List<String>>());
        expect(status['disabled_features'], isA<List<String>>());
        expect(status['total_features'], greaterThan(0));
      });

      test('should have more enabled features than disabled for MVP', () async {
        await Features.initialize();
        
        final enabledFeatures = Features.getEnabledFeatures();
        final disabledFeatures = Features.getDisabledFeatures();
        
        // Mobile money MVP should have more enabled features than disabled
        expect(enabledFeatures.length, greaterThan(disabledFeatures.length));
      });
    });

    group('UI Refactor', () {
      test('should remove banking tabs', () {
        // This test verifies that UIRefactor.removeBankingTabs() executes without error
        expect(() => UIRefactor.removeBankingTabs(), returnsNormally);
      });

      test('should simplify UI for mobile money MVP', () {
        // This test verifies that UIRefactor.simplifyForMobileMoneyMVP() executes without error
        expect(() => UIRefactor.simplifyForMobileMoneyMVP(), returnsNormally);
      });
    });

    group('MVP Compliance', () {
      test('CRITICAL: Banking features must be completely disabled', () async {
        await Features.initialize();
        
        // This is the critical test for MVP compliance
        expect(Features.areBankingFeaturesDisabled(), true, 
               reason: 'Banking features MUST be disabled for mobile money MVP');
      });

      test('CRITICAL: Mobile money core must be fully enabled', () async {
        await Features.initialize();
        
        // This is the critical test for MVP functionality
        expect(Features.isMobileMoneyEnabled(), true,
               reason: 'Mobile money core MUST be enabled for MVP');
      });

      test('CRITICAL: MVP mode must be mobile money only', () async {
        await Features.initialize();
        
        final status = Features.getFeatureLockStatus();
        expect(status['mvp_mode'], 'mobile_money_only',
               reason: 'MVP mode MUST be mobile money only');
      });
    });
  });
}
