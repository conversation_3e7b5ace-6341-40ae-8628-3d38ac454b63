import 'dart:async';
import 'package:flutter_test/flutter_test.dart';

import 'package:zambia_pay/features/mobile_money/data/services/momo_streaming_service.dart';
import 'package:zambia_pay/features/mobile_money/data/models/transaction_model.dart';
import 'package:zambia_pay/core/constants/app_constants.dart';
import 'package:zambia_pay/core/error_handling/momo_error_handler.dart';

void main() {
  group('Simple Mobile Money Streaming Tests', () {
    late MomoStreamingService streamingService;

    setUp(() {
      streamingService = MomoStreamingService();
    });

    tearDown(() {
      streamingService.dispose();
    });

    group('Service Initialization', () {
      test('should create streaming service instance', () {
        expect(streamingService, isNotNull);
        expect(streamingService, isA<MomoStreamingService>());
      });

      test('should initialize without throwing errors', () async {
        expect(() => streamingService.initialize(), returnsNormally);
      });
    });

    group('Stream Availability', () {
      test('should provide transaction stream', () async {
        await streamingService.initialize();
        final stream = streamingService.realtimeTransactions;
        expect(stream, isA<Stream<List<TransactionModel>>>());
      });

      test('should provide balance stream', () async {
        await streamingService.initialize();
        final stream = streamingService.realtimeBalances;
        expect(stream, isA<Stream<Map<String, double>>>());
      });
    });

    group('Error Handling', () {
      test('should handle transaction stream errors gracefully', () async {
        final error = Exception('Test error');
        final result = await MomoErrorHandler.handleTransactionStreamError(error);
        expect(result, isA<List>());
      });

      test('should handle balance stream errors gracefully', () async {
        final error = Exception('Test error');
        final result = await MomoErrorHandler.handleBalanceStreamError(error);
        expect(result, isA<Map<String, double>>());
      });

      test('should provide user-friendly error messages', () {
        final networkError = MomoErrorHandler.getUserFriendlyErrorMessage(
          Exception('Network unreachable')
        );
        final timeoutError = MomoErrorHandler.getUserFriendlyErrorMessage(
          Exception('Request timeout')
        );

        expect(networkError, isA<String>());
        expect(timeoutError, isA<String>());
        expect(networkError.isNotEmpty, isTrue);
        expect(timeoutError.isNotEmpty, isTrue);
      });
    });

    group('Provider Constants', () {
      test('should have valid provider constants', () {
        expect(AppConstants.providerMTN, equals('MTN'));
        expect(AppConstants.providerAirtel, equals('AIRTEL'));
        expect(AppConstants.providerZamtel, equals('ZAMTEL'));
      });

      test('should have valid status constants', () {
        expect(AppConstants.statusPending, equals('PENDING'));
        expect(AppConstants.statusCompleted, equals('COMPLETED'));
        expect(AppConstants.statusFailed, equals('FAILED'));
        expect(AppConstants.statusProcessing, equals('PROCESSING'));
      });
    });

    group('Transaction Model', () {
      test('should create valid transaction model', () {
        final transaction = TransactionModel(
          id: 'test_tx_001',
          userId: 'test_user',
          transactionType: AppConstants.transactionTypeSend,
          amount: 100.0,
          totalAmount: 102.0,
          provider: AppConstants.providerMTN,
          status: AppConstants.statusCompleted,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(transaction.id, equals('test_tx_001'));
        expect(transaction.provider, equals(AppConstants.providerMTN));
        expect(transaction.amount, equals(100.0));
        expect(transaction.status, equals(AppConstants.statusCompleted));
      });

      test('should handle transaction with metadata', () {
        final transaction = TransactionModel(
          id: 'test_tx_002',
          userId: 'test_user',
          transactionType: AppConstants.transactionTypeSend,
          amount: 50.0,
          totalAmount: 51.0,
          provider: AppConstants.providerAirtel,
          status: AppConstants.statusPending,
          metadata: {'verified': true, 'source': 'test'},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(transaction.metadata, isNotNull);
        expect(transaction.metadata!['verified'], isTrue);
        expect(transaction.metadata!['source'], equals('test'));
      });
    });

    group('Stream Timeout Handling', () {
      test('should handle stream timeout gracefully', () async {
        await streamingService.initialize();
        
        // Test that streams don't throw immediately
        expect(() => streamingService.realtimeTransactions, returnsNormally);
        expect(() => streamingService.realtimeBalances, returnsNormally);
      });
    });

    group('Resource Management', () {
      test('should dispose resources properly', () {
        expect(() => streamingService.dispose(), returnsNormally);
      });

      test('should handle multiple dispose calls', () {
        streamingService.dispose();
        expect(() => streamingService.dispose(), returnsNormally);
      });
    });
  });

  group('Error Handler Utility Tests', () {
    test('should identify recoverable errors', () {
      final networkError = Exception('Network unreachable');
      final authError = Exception('Authentication failed');
      final unknownError = Exception('Unknown error');

      // These tests verify the error classification logic
      expect(MomoErrorHandler.getUserFriendlyErrorMessage(networkError), isA<String>());
      expect(MomoErrorHandler.getUserFriendlyErrorMessage(authError), isA<String>());
      expect(MomoErrorHandler.getUserFriendlyErrorMessage(unknownError), isA<String>());
    });

    test('should handle provider-specific errors', () {
      final mtnError = Exception('MTN service unavailable');
      final airtelError = Exception('Airtel limit exceeded');
      final zamtelError = Exception('Zamtel maintenance');

      final mtnMessage = MomoErrorHandler.handleProviderError(AppConstants.providerMTN, mtnError);
      final airtelMessage = MomoErrorHandler.handleProviderError(AppConstants.providerAirtel, airtelError);
      final zamtelMessage = MomoErrorHandler.handleProviderError(AppConstants.providerZamtel, zamtelError);

      expect(mtnMessage, isA<String>());
      expect(airtelMessage, isA<String>());
      expect(zamtelMessage, isA<String>());
      expect(mtnMessage.isNotEmpty, isTrue);
      expect(airtelMessage.isNotEmpty, isTrue);
      expect(zamtelMessage.isNotEmpty, isTrue);
    });
  });
}

/// Helper function to create test transactions
TransactionModel createTestTransaction(
  String id,
  String status, {
  String provider = AppConstants.providerMTN,
  String? referenceNumber,
  double amount = 100.0,
}) {
  return TransactionModel(
    id: id,
    userId: 'test_user',
    transactionType: AppConstants.transactionTypeSend,
    amount: amount,
    totalAmount: amount + 2.0, // Add small fee
    provider: provider,
    status: status,
    referenceNumber: referenceNumber,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}
