import 'package:flutter_test/flutter_test.dart';
import '../../lib/notifications/momo_alerts.dart';
import '../../lib/features/feature_lock.dart';
import '../../lib/wallet/zambia_wallets.dart';
import '../../lib/offline/zambia_retry_policy.dart';

/// Test suite for Mobile Money Alert System
/// Ensures notification system works with feature lock and wallet systems
void main() {
  group('🔔 MOBILE MONEY ALERTS - MVP Tests', () {
    late MomoNotificationService notificationService;

    setUp(() async {
      // Initialize feature lock system first
      await Features.initialize();
      
      // Ensure mobile money is enabled before wallet setup
      Features.enable(Features.MOBILE_MONEY);
      Features.enable(Features.CHILIMBA);
      Features.enable(Features.UTILITY_PAYMENTS);
      Features.enable(Features.AGENT_LOCATOR);
      
      // Setup wallet-only flow
      await ZambiaWallets.setupWalletOnlyFlow();
      
      notificationService = MomoNotificationService();
      await notificationService.initialize();
    });

    group('Notification Service Initialization', () {
      test('should initialize notification service successfully', () async {
        final service = MomoNotificationService();
        await service.initialize();
        
        expect(service.isInitialized, true);
        expect(service.enabledTypes, isEmpty);
        expect(service.hasDeliveryGuarantee, false);
      });

      test('should work with feature lock system', () async {
        await Features.initialize();
        final service = MomoNotificationService();
        await service.initialize();
        
        // Should only work when mobile money is enabled
        expect(Features.isEnabled(Features.MOBILE_MONEY), true);
        expect(service.isInitialized, true);
      });
    });

    group('Notification Types', () {
      test('should support all mobile money notification types', () {
        final types = NotificationType.values;
        
        expect(types, contains(NotificationType.MONEY_RECEIVED));
        expect(types, contains(NotificationType.MONEY_SENT));
        expect(types, contains(NotificationType.CHILIMBA_REQUEST));
        expect(types, contains(NotificationType.CHILIMBA_CONTRIBUTION));
        expect(types, contains(NotificationType.UTILITY_CONFIRMATION));
        expect(types, contains(NotificationType.UTILITY_REMINDER));
        expect(types, contains(NotificationType.BALANCE_LOW));
        expect(types, contains(NotificationType.TRANSACTION_FAILED));
        expect(types, contains(NotificationType.AGENT_NEARBY));
        expect(types, contains(NotificationType.AIRTIME_PURCHASED));
      });

      test('should have correct notification type properties', () {
        final moneyReceived = NotificationType.MONEY_RECEIVED;
        expect(moneyReceived.code, 'money_received');
        expect(moneyReceived.displayName, 'Money Received');
        expect(moneyReceived.icon, isNotNull);

        final chilimbaRequest = NotificationType.CHILIMBA_REQUEST;
        expect(chilimbaRequest.code, 'chilimba_request');
        expect(chilimbaRequest.displayName, 'Chilimba Request');
        expect(chilimbaRequest.icon, isNotNull);

        final utilityConfirmation = NotificationType.UTILITY_CONFIRMATION;
        expect(utilityConfirmation.code, 'utility_confirmation');
        expect(utilityConfirmation.displayName, 'Utility Payment');
        expect(utilityConfirmation.icon, isNotNull);
      });
    });

    group('Notification Configuration', () {
      test('should configure mobile money notifications successfully', () {
        expect(() => notificationService.configureMomoNotifications(), returnsNormally);
      });

      test('should configure notifications with global function', () {
        expect(() => configureMomoNotifications(), returnsNormally);
      });

      test('should enable specific notification types', () {
        final types = [
          NotificationType.MONEY_RECEIVED,
          NotificationType.CHILIMBA_REQUEST,
          NotificationType.UTILITY_CONFIRMATION,
        ];

        notificationService.enableTypes(types);
        
        expect(notificationService.enabledTypes.length, 3);
        expect(notificationService.enabledTypes, contains(NotificationType.MONEY_RECEIVED));
        expect(notificationService.enabledTypes, contains(NotificationType.CHILIMBA_REQUEST));
        expect(notificationService.enabledTypes, contains(NotificationType.UTILITY_CONFIRMATION));
      });

      test('should disable specific notification types', () {
        // First enable some types
        notificationService.enableTypes([
          NotificationType.MONEY_RECEIVED,
          NotificationType.MONEY_SENT,
          NotificationType.CHILIMBA_REQUEST,
        ]);

        // Then disable some
        notificationService.disableTypes([NotificationType.MONEY_SENT]);
        
        expect(notificationService.enabledTypes.length, 2);
        expect(notificationService.enabledTypes, contains(NotificationType.MONEY_RECEIVED));
        expect(notificationService.enabledTypes, contains(NotificationType.CHILIMBA_REQUEST));
        expect(notificationService.enabledTypes, isNot(contains(NotificationType.MONEY_SENT)));
      });
    });

    group('Delivery Guarantee', () {
      test('should enable delivery guarantee with retry policy', () {
        final retryPolicy = ZambiaRetryPolicy(
          maxRetries: 3,
          baseDelay: const Duration(seconds: 5),
          maxDelay: const Duration(minutes: 5),
          backoffMultiplier: 2.0,
        );
        final fallback = SMSNotification();

        notificationService.enableDeliveryGuarantee(
          retryPolicy: retryPolicy,
          fallback: fallback,
        );

        expect(notificationService.hasDeliveryGuarantee, true);
      });

      test('should create delivery guarantee with correct configuration', () {
        final retryPolicy = ZambiaRetryPolicy(
          maxRetries: 3,
          baseDelay: const Duration(seconds: 5),
          maxDelay: const Duration(minutes: 5),
          backoffMultiplier: 2.0,
        );
        final fallback = SMSNotification();

        final deliveryGuarantee = DeliveryGuarantee(
          retryPolicy: retryPolicy,
          fallback: fallback,
        );

        expect(deliveryGuarantee.retryPolicy, retryPolicy);
        expect(deliveryGuarantee.fallback, fallback);
      });
    });

    group('SMS Fallback', () {
      test('should create SMS notification fallback', () {
        final smsNotification = SMSNotification();
        expect(smsNotification, isNotNull);
      });

      test('should send SMS fallback notification', () async {
        final smsNotification = SMSNotification();
        
        expect(() async => await smsNotification.send('+************', 'Test message'), returnsNormally);
      });
    });

    group('Notification Sending', () {
      test('should send money received notification', () async {
        notificationService.enableTypes([NotificationType.MONEY_RECEIVED]);
        
        final data = {
          'amount': 100.0,
          'sender': 'John Doe',
          'transaction_id': 'tx_123',
        };

        expect(() async => await notificationService.sendNotification(
          userId: 'user_123',
          phoneNumber: '+************',
          type: NotificationType.MONEY_RECEIVED,
          data: data,
          transactionId: 'tx_123',
        ), returnsNormally);
      });

      test('should send Chilimba request notification', () async {
        notificationService.enableTypes([NotificationType.CHILIMBA_REQUEST]);
        
        final data = {
          'group_name': 'Village Savings Group',
          'amount': 50.0,
          'requester': 'Mary Banda',
        };

        expect(() async => await notificationService.sendNotification(
          userId: 'user_123',
          phoneNumber: '+************',
          type: NotificationType.CHILIMBA_REQUEST,
          data: data,
        ), returnsNormally);
      });

      test('should send utility confirmation notification', () async {
        notificationService.enableTypes([NotificationType.UTILITY_CONFIRMATION]);
        
        final data = {
          'provider': 'ZESCO',
          'amount': 75.0,
          'account_number': '********',
        };

        expect(() async => await notificationService.sendNotification(
          userId: 'user_123',
          phoneNumber: '+************',
          type: NotificationType.UTILITY_CONFIRMATION,
          data: data,
        ), returnsNormally);
      });

      test('should skip disabled notification types', () async {
        // Don't enable any types
        
        final data = {
          'amount': 100.0,
          'sender': 'John Doe',
        };

        // Should complete without error but not send notification
        expect(() async => await notificationService.sendNotification(
          userId: 'user_123',
          phoneNumber: '+************',
          type: NotificationType.MONEY_RECEIVED,
          data: data,
        ), returnsNormally);
      });
    });

    group('Feature Lock Integration', () {
      test('should only work when mobile money features are enabled', () async {
        await Features.initialize();
        await notificationService.initialize();
        
        // Mobile money should be enabled for MVP
        expect(Features.isEnabled(Features.MOBILE_MONEY), true);
        expect(notificationService.isInitialized, true);
      });

      test('should skip notifications when mobile money features are disabled', () async {
        // Disable mobile money features
        Features.disable(Features.MOBILE_MONEY);
        
        notificationService.enableTypes([NotificationType.MONEY_RECEIVED]);
        
        final data = {'amount': 100.0, 'sender': 'John Doe'};

        // Should complete without error but not send notification
        expect(() async => await notificationService.sendNotification(
          userId: 'user_123',
          phoneNumber: '+************',
          type: NotificationType.MONEY_RECEIVED,
          data: data,
        ), returnsNormally);
      });
    });

    group('MVP Compliance', () {
      test('CRITICAL: Notification system must work with feature lock', () async {
        await Features.initialize();
        await notificationService.initialize();
        
        // Banking features must be disabled
        expect(Features.areBankingFeaturesDisabled(), true,
               reason: 'Banking features MUST be disabled for MVP');
        
        // Mobile money must be enabled
        expect(Features.isMobileMoneyEnabled(), true,
               reason: 'Mobile money MUST be enabled for MVP');
        
        // Notifications should work with mobile money enabled
        expect(notificationService.isInitialized, true,
               reason: 'Notifications MUST work when mobile money is enabled');
      });

      test('CRITICAL: Only mobile money notification types should be supported', () {
        final types = NotificationType.values;
        
        // Should support mobile money notifications
        expect(types, contains(NotificationType.MONEY_RECEIVED));
        expect(types, contains(NotificationType.MONEY_SENT));
        expect(types, contains(NotificationType.CHILIMBA_REQUEST));
        expect(types, contains(NotificationType.UTILITY_CONFIRMATION));
        expect(types, contains(NotificationType.AIRTIME_PURCHASED));
        
        // Should not support banking notifications (none exist - good!)
        expect(types.where((t) => t.code.contains('bank')), isEmpty,
               reason: 'No banking notification types should exist for MVP');
      });

      test('CRITICAL: Notifications must have Zambian delivery guarantee', () {
        final retryPolicy = ZambiaRetryPolicy(
          maxRetries: 3,
          baseDelay: const Duration(seconds: 5),
          maxDelay: const Duration(minutes: 5),
          backoffMultiplier: 2.0,
        );
        final fallback = SMSNotification();

        notificationService.enableDeliveryGuarantee(
          retryPolicy: retryPolicy,
          fallback: fallback,
        );

        expect(notificationService.hasDeliveryGuarantee, true,
               reason: 'Notifications MUST have Zambian delivery guarantee');
      });
    });
  });
}
