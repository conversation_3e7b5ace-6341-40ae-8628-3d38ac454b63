import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/features/feature_lock.dart';
import '../../lib/wallet/zambia_wallets.dart';
import '../../lib/notifications/momo_alerts.dart';

/// Airtel Transfer Notification Test
/// Tests Airtel money transfer notification functionality
void main() {
  group('🇿🇲 Airtel Transfer Notification Tests', () {
    late MomoNotificationService notificationService;
    final networkProfile = Platform.environment['ZAMBIA_NETWORK_PROFILE'] ?? '4g';

    setUp(() async {
      await Features.initialize();
      Features.enable(Features.MOBILE_MONEY);
      
      await ZambiaWallets.setupWalletOnlyFlow();
      
      notificationService = MomoNotificationService();
      await notificationService.initialize();
      notificationService.configureMomoNotifications();
    });

    test('should send Airtel transfer notification', () async {
      final data = {
        'amount': 100.0,
        'recipient': '<PERSON>',
        'transaction_id': 'airtel_tx_123',
        'provider': 'AIRTEL',
      };

      await notificationService.sendNotification(
        userId: 'user_123',
        phoneNumber: '+260971234567', // Airtel number
        type: NotificationType.MONEY_SENT,
        data: data,
        transactionId: 'airtel_tx_123',
      );

      expect(notificationService.isInitialized, true);
    });

    test('should handle Airtel notifications on $networkProfile network', () async {
      final isSlowNetwork = ['2g', 'offline'].contains(networkProfile);
      
      final data = {
        'amount': 50.0,
        'sender': 'Mary Banda',
        'transaction_id': 'airtel_rx_456',
        'provider': 'AIRTEL',
      };

      if (isSlowNetwork) {
        // Test SMS fallback for slow networks
        notificationService.enableDeliveryGuarantee(
          retryPolicy: ZambiaRetryPolicy(
            maxRetries: 3,
            baseDelay: const Duration(seconds: 5),
            maxDelay: const Duration(minutes: 5),
            backoffMultiplier: 2.0,
          ),
          fallback: SMSNotification(),
        );
      }

      await notificationService.sendNotification(
        userId: 'user_456',
        phoneNumber: '+260971234567',
        type: NotificationType.MONEY_RECEIVED,
        data: data,
        transactionId: 'airtel_rx_456',
      );

      expect(notificationService.hasDeliveryGuarantee, isSlowNetwork);
    });

    test('should detect Airtel wallet from phone number', () {
      final airtelNumbers = ['+260971234567', '260971234567', '971234567'];
      
      for (final number in airtelNumbers) {
        final wallet = ZambiaWallets.getWalletByPhoneNumber(number);
        expect(wallet, MobileWallet.AIRTEL_MONEY);
      }
    });

    test('should support Airtel-specific notification types', () {
      final airtelTypes = [
        NotificationType.MONEY_RECEIVED,
        NotificationType.MONEY_SENT,
        NotificationType.UTILITY_CONFIRMATION,
        NotificationType.AIRTIME_PURCHASED,
      ];

      notificationService.enableTypes(airtelTypes);
      
      for (final type in airtelTypes) {
        expect(notificationService.enabledTypes, contains(type));
      }
    });
  });
}
