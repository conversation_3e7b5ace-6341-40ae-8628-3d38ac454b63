import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/features/feature_lock.dart';
import '../../lib/wallet/zambia_wallets.dart';
import '../../lib/refresh/momo_refresh.dart';

/// MTN Balance Refresh Test
/// Tests MTN mobile money balance refresh functionality across network conditions
void main() {
  group('🇿🇲 MTN Balance Refresh Tests', () {
    late MomoRefreshController refreshController;
    final networkProfile = Platform.environment['ZAMBIA_NETWORK_PROFILE'] ?? '4g';

    setUp(() async {
      await Features.initialize();
      Features.enable(Features.MOBILE_MONEY);
      
      await ZambiaWallets.setupWalletOnlyFlow();
      
      refreshController = MomoRefreshController();
      await refreshController.initialize();
    });

    test('should refresh MTN balance successfully', () async {
      // Test MTN balance refresh
      await refreshController.triggerRefresh(
        actions: [RefreshAction.updateBalances],
      );
      
      expect(refreshController.isRefreshing, false);
    });

    test('should handle MTN balance refresh on $networkProfile network', () async {
      // Network-specific test based on environment variable
      final isSlowNetwork = ['2g', 'offline'].contains(networkProfile);
      
      if (isSlowNetwork) {
        // Test with longer timeout for slow networks
        await refreshController.triggerRefresh(
          actions: [RefreshAction.updateBalances],
        );
      } else {
        // Test with normal timeout for fast networks
        await refreshController.triggerRefresh(
          actions: [RefreshAction.updateBalances],
        );
      }
      
      expect(refreshController.isRefreshing, false);
    });

    test('should detect MTN wallet from phone number', () {
      final mtnNumbers = ['+260961234567', '260961234567', '961234567'];
      
      for (final number in mtnNumbers) {
        final wallet = ZambiaWallets.getWalletByPhoneNumber(number);
        expect(wallet, MobileWallet.MTN_MONEY);
      }
    });
  });
}
