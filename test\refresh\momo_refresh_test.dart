import 'package:flutter_test/flutter_test.dart';
import '../../lib/refresh/momo_refresh.dart';
import '../../lib/features/feature_lock.dart';
import '../../lib/wallet/zambia_wallets.dart';

/// Test suite for Mobile Money Refresh System
/// Ensures 2G-optimized refresh functionality works with feature lock and wallet systems
void main() {
  group('🔄 MOBILE MONEY REFRESH SYSTEM - MVP Tests', () {
    late MomoRefreshController refreshController;

    setUp(() async {
      // Initialize feature lock system first
      await Features.initialize();

      // Ensure mobile money is enabled before wallet setup
      Features.enable(Features.MOBILE_MONEY);
      Features.enable(Features.CHILIMBA);
      Features.enable(Features.UTILITY_PAYMENTS);
      Features.enable(Features.AGENT_LOCATOR);

      // Now setup wallet-only flow
      await ZambiaWallets.setupWalletOnlyFlow();

      refreshController = MomoRefreshController();
      await refreshController.initialize();
    });

    group('Refresh Controller Initialization', () {
      test('should initialize refresh controller successfully', () async {
        final controller = MomoRefreshController();
        await controller.initialize();
        
        expect(controller, isNotNull);
        expect(controller.isRefreshing, false);
        expect(controller.lastRefreshTime, null);
      });

      test('should work with feature lock system', () async {
        await Features.initialize();
        final controller = MomoRefreshController();
        await controller.initialize();
        
        // Should only work when mobile money is enabled
        expect(Features.isEnabled(Features.MOBILE_MONEY), true);
        expect(controller.canRefresh, true);
      });
    });

    group('Refresh Triggers', () {
      test('should support all refresh trigger types', () {
        final triggers = RefreshTrigger.values;
        
        expect(triggers, contains(RefreshTrigger.manual));
        expect(triggers, contains(RefreshTrigger.automatic));
        expect(triggers, contains(RefreshTrigger.connectivity));
        expect(triggers, contains(RefreshTrigger.transaction));
        expect(triggers, contains(RefreshTrigger.startup));
      });

      test('should support all refresh action types', () {
        final actions = RefreshAction.values;
        
        expect(actions, contains(RefreshAction.updateBalances));
        expect(actions, contains(RefreshAction.syncTransactions));
        expect(actions, contains(RefreshAction.updateRates));
        expect(actions, contains(RefreshAction.syncAgents));
        expect(actions, contains(RefreshAction.updateUtilities));
        expect(actions, contains(RefreshAction.fullSync));
      });
    });

    group('Balance Refresh', () {
      test('should enable balance refresh successfully', () {
        expect(() => refreshController.enableBalanceRefresh(), returnsNormally);
      });

      test('should enable balance refresh with global function', () {
        expect(() => enableBalanceRefresh(), returnsNormally);
      });

      test('should configure balance refresh with correct settings', () {
        refreshController.enableBalanceRefresh();
        
        // Verify that balance refresh is configured
        // (Implementation would check internal state)
        expect(refreshController, isNotNull);
      });
    });

    group('Refresh Listeners', () {
      test('should add and remove refresh listeners', () {
        final listener = RefreshListener(
          trigger: RefreshTrigger.manual,
          actions: [RefreshAction.updateBalances],
          constraints: null, // Optional constraints
        );

        expect(() => refreshController.addRefreshListener(listener), returnsNormally);
        expect(() => refreshController.removeRefreshListener(listener), returnsNormally);
      });

      test('should create refresh listener with correct configuration', () {
        final listener = RefreshListener(
          trigger: RefreshTrigger.manual,
          actions: [
            RefreshAction.updateBalances,
            RefreshAction.syncTransactions,
          ],
          constraints: null, // Optional constraints
          priority: RefreshPriority.high,
        );

        expect(listener.trigger, RefreshTrigger.manual);
        expect(listener.actions.length, 2);
        expect(listener.actions, contains(RefreshAction.updateBalances));
        expect(listener.actions, contains(RefreshAction.syncTransactions));
        expect(listener.constraints, null);
        expect(listener.priority, RefreshPriority.high);
      });
    });

    group('Manual Refresh', () {
      test('should trigger manual refresh successfully', () async {
        expect(() async => await refreshController.triggerRefresh(), returnsNormally);
      });

      test('should trigger manual refresh with specific actions', () async {
        final actions = [
          RefreshAction.updateBalances,
          RefreshAction.syncTransactions,
        ];

        expect(() async => await refreshController.triggerRefresh(actions: actions), returnsNormally);
      });

      test('should support force refresh', () async {
        expect(() async => await refreshController.triggerRefresh(forceRefresh: true), returnsNormally);
      });
    });

    group('Refresh Priorities', () {
      test('should support all refresh priority levels', () {
        final priorities = RefreshPriority.values;
        
        expect(priorities, contains(RefreshPriority.low));
        expect(priorities, contains(RefreshPriority.normal));
        expect(priorities, contains(RefreshPriority.high));
        expect(priorities, contains(RefreshPriority.critical));
      });

      test('should create listeners with different priorities', () {
        final lowPriorityListener = RefreshListener(
          trigger: RefreshTrigger.automatic,
          actions: [RefreshAction.updateRates],
          constraints: null,
          priority: RefreshPriority.low,
        );

        final highPriorityListener = RefreshListener(
          trigger: RefreshTrigger.manual,
          actions: [RefreshAction.updateBalances],
          constraints: null,
          priority: RefreshPriority.high,
        );

        expect(lowPriorityListener.priority, RefreshPriority.low);
        expect(highPriorityListener.priority, RefreshPriority.high);
      });
    });

    group('Data Optimization', () {
      test('should support 2G optimization through constraints', () {
        final listener = RefreshListener(
          trigger: RefreshTrigger.manual,
          actions: [RefreshAction.updateBalances],
          constraints: null, // Uses default optimization
        );

        expect(listener.constraints, null);
      });

      test('should respect data constraints', () {
        // Test that refresh respects data saving constraints
        expect(refreshController.canRefresh, isA<bool>());
      });
    });

    group('Wallet Integration', () {
      test('should work with all supported Zambian wallets', () {
        final supportedWallets = ZambiaWallets.supportedWallets;
        
        expect(supportedWallets.length, 3);
        expect(supportedWallets.map((w) => w.code), contains('MTN'));
        expect(supportedWallets.map((w) => w.code), contains('AIRTEL'));
        expect(supportedWallets.map((w) => w.code), contains('ZAMTEL'));
      });

      test('should refresh balances for all wallet types', () async {
        // Enable balance refresh for all supported wallets
        refreshController.enableBalanceRefresh();
        
        // Trigger refresh for balance updates
        await refreshController.triggerRefresh(
          actions: [RefreshAction.updateBalances],
        );
        
        // Should complete without errors
        expect(refreshController, isNotNull);
      });
    });

    group('Feature Lock Integration', () {
      test('should only work when mobile money features are enabled', () async {
        await Features.initialize();
        
        // Mobile money should be enabled for MVP
        expect(Features.isEnabled(Features.MOBILE_MONEY), true);
        expect(refreshController.canRefresh, true);
      });

      test('should not work when mobile money features are disabled', () async {
        // Disable mobile money features
        Features.disable(Features.MOBILE_MONEY);
        
        expect(Features.isEnabled(Features.MOBILE_MONEY), false);
        // Refresh should be blocked when mobile money is disabled
      });
    });

    group('Network Optimization', () {
      test('should handle different network conditions', () async {
        // Test manual refresh (should work on any network)
        await refreshController.triggerRefresh(
          actions: [RefreshAction.updateBalances],
        );
        
        // Test automatic refresh (should respect network conditions)
        // Implementation would test network-aware behavior
        expect(refreshController, isNotNull);
      });

      test('should optimize for 2G networks', () {
        final listener = RefreshListener(
          trigger: RefreshTrigger.manual,
          actions: [RefreshAction.updateBalances, RefreshAction.syncTransactions],
          constraints: null, // Uses default 2G optimization
        );

        expect(listener.constraints, null);
      });
    });

    group('MVP Compliance', () {
      test('CRITICAL: Refresh system must work with feature lock', () async {
        await Features.initialize();
        await refreshController.initialize();
        
        // Banking features must be disabled
        expect(Features.areBankingFeaturesDisabled(), true,
               reason: 'Banking features MUST be disabled for MVP');
        
        // Mobile money must be enabled
        expect(Features.isMobileMoneyEnabled(), true,
               reason: 'Mobile money MUST be enabled for MVP');
        
        // Refresh should work with mobile money enabled
        expect(refreshController.canRefresh, true,
               reason: 'Refresh MUST work when mobile money is enabled');
      });

      test('CRITICAL: Only mobile money refresh actions should be supported', () {
        final actions = RefreshAction.values;
        
        // Should support mobile money actions
        expect(actions, contains(RefreshAction.updateBalances));
        expect(actions, contains(RefreshAction.syncTransactions));
        expect(actions, contains(RefreshAction.updateRates));
        expect(actions, contains(RefreshAction.syncAgents));
        expect(actions, contains(RefreshAction.updateUtilities));
        expect(actions, contains(RefreshAction.fullSync));
        
        // Should not support banking actions (none exist - good!)
        expect(actions.where((a) => a.toString().contains('bank')), isEmpty,
               reason: 'No banking refresh actions should exist for MVP');
      });

      test('CRITICAL: Refresh must be optimized for Zambian networks', () {
        final listener = RefreshListener(
          trigger: RefreshTrigger.manual,
          actions: [RefreshAction.updateBalances, RefreshAction.syncTransactions],
          constraints: null, // Uses default 2G optimization
        );

        // Refresh system should support 2G optimization
        expect(refreshController, isNotNull,
               reason: 'Refresh MUST support 2G optimization for Zambian networks');
      });
    });
  });
}
