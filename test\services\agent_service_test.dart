import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:geolocator/geolocator.dart';
import '../../lib/services/agent_service.dart';
import '../../lib/features/financial_inclusion/zambia_financial_registry.dart';

/// Test suite for Agent Service
/// Validates Zambian agent network and offline-first functionality
void main() {
  group('🇿🇲 Agent Service Tests', () {
    late AgentService agentService;

    setUp(() {
      agentService = AgentService();
    });

    tearDown(() {
      agentService.dispose();
    });

    group('Agent Service Initialization', () {
      test('should initialize agent service correctly', () {
        expect(agentService, isNotNull);
      });

      test('should load Zambian agents', () async {
        // Test that loadZambianAgents doesn't throw
        expect(() => agentService.loadZambianAgents(), returnsNormally);
      });
    });

    group('Zambian Agent Model', () {
      test('should create agent from map correctly', () {
        final agentMap = {
          'agent_id': 'ZM_AGENT_001',
          'name': 'Test Agent',
          'phone_number': '+260211234567',
          'location_name': 'Test Location, Lusaka',
          'latitude': -15.3875,
          'longitude': 28.3228,
          'services': '["cash_in", "cash_out"]',
          'operating_hours': '{"monday": "08:00-17:00"}',
          'commission_rates': '{"cash_in": 0.01}',
          'cash_limit': 50000.0,
          'status': 'active',
          'boz_license': 'BOZ/FA/2024/001',
          'last_updated': DateTime.now().millisecondsSinceEpoch,
        };

        final agent = ZambianAgent.fromMap(agentMap);

        expect(agent.agentId, 'ZM_AGENT_001');
        expect(agent.name, 'Test Agent');
        expect(agent.phoneNumber, '+260211234567');
        expect(agent.latitude, -15.3875);
        expect(agent.longitude, 28.3228);
        expect(agent.services, ['cash_in', 'cash_out']);
        expect(agent.status, 'active');
        expect(agent.bozLicense, 'BOZ/FA/2024/001');
      });

      test('should convert agent to map correctly', () {
        final agent = ZambianAgent(
          agentId: 'ZM_AGENT_001',
          name: 'Test Agent',
          phoneNumber: '+260211234567',
          locationName: 'Test Location, Lusaka',
          latitude: -15.3875,
          longitude: 28.3228,
          services: ['cash_in', 'cash_out'],
          operatingHours: {'monday': '08:00-17:00'},
          commissionRates: {'cash_in': 0.01},
          cashLimit: 50000.0,
          status: 'active',
          bozLicense: 'BOZ/FA/2024/001',
          lastUpdated: DateTime.now(),
        );

        final map = agent.toMap();

        expect(map['agent_id'], 'ZM_AGENT_001');
        expect(map['name'], 'Test Agent');
        expect(map['phone_number'], '+260211234567');
        expect(map['latitude'], -15.3875);
        expect(map['longitude'], 28.3228);
        expect(map['status'], 'active');
        expect(map['boz_license'], 'BOZ/FA/2024/001');
      });

      test('should check if agent provides service', () {
        final agent = ZambianAgent(
          agentId: 'ZM_AGENT_001',
          name: 'Test Agent',
          phoneNumber: '+260211234567',
          locationName: 'Test Location',
          latitude: -15.3875,
          longitude: 28.3228,
          services: ['cash_in', 'cash_out', 'money_transfer'],
          operatingHours: {},
          commissionRates: {},
          cashLimit: 50000.0,
          status: 'active',
          bozLicense: 'BOZ/FA/2024/001',
          lastUpdated: DateTime.now(),
        );

        expect(agent.providesService('cash_in'), true);
        expect(agent.providesService('cash_out'), true);
        expect(agent.providesService('money_transfer'), true);
        expect(agent.providesService('bill_payment'), false);
      });

      test('should get commission rate for service', () {
        final agent = ZambianAgent(
          agentId: 'ZM_AGENT_001',
          name: 'Test Agent',
          phoneNumber: '+260211234567',
          locationName: 'Test Location',
          latitude: -15.3875,
          longitude: 28.3228,
          services: ['cash_in', 'cash_out'],
          operatingHours: {},
          commissionRates: {
            'cash_in': 0.01,
            'cash_out': 0.015,
          },
          cashLimit: 50000.0,
          status: 'active',
          bozLicense: 'BOZ/FA/2024/001',
          lastUpdated: DateTime.now(),
        );

        expect(agent.getCommissionRate('cash_in'), 0.01);
        expect(agent.getCommissionRate('cash_out'), 0.015);
        expect(agent.getCommissionRate('money_transfer'), null);
      });

      test('should format distance correctly', () {
        final agent = ZambianAgent(
          agentId: 'ZM_AGENT_001',
          name: 'Test Agent',
          phoneNumber: '+260211234567',
          locationName: 'Test Location',
          latitude: -15.3875,
          longitude: 28.3228,
          services: [],
          operatingHours: {},
          commissionRates: {},
          cashLimit: 50000.0,
          status: 'active',
          bozLicense: 'BOZ/FA/2024/001',
          lastUpdated: DateTime.now(),
        );

        // Test distance formatting
        agent.distanceKm = 0.5;
        expect(agent.formattedDistance, '500m away');

        agent.distanceKm = 1.2;
        expect(agent.formattedDistance, '1.2km away');

        agent.distanceKm = null;
        expect(agent.formattedDistance, 'Distance unknown');
      });
    });

    group('Agent Search and Filtering', () {
      test('should find nearby agents', () async {
        // Test nearby agent search
        final userLocation = Position(
          latitude: -15.3875,
          longitude: 28.3228,
          timestamp: DateTime.now(),
          accuracy: 10.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0,
        );

        final nearbyAgents = await agentService.findNearbyAgents(
          userLocation: userLocation,
          radiusKm: 5.0,
        );

        expect(nearbyAgents, isA<List<ZambianAgent>>());
      });

      test('should search agents by name', () async {
        final searchResults = await agentService.searchAgents(
          query: 'Shoprite',
          maxResults: 10,
        );

        expect(searchResults, isA<List<ZambianAgent>>());
      });

      test('should get agents by service type', () async {
        final serviceAgents = await agentService.getAgentsByService(
          serviceType: 'cash_in',
          maxResults: 10,
        );

        expect(serviceAgents, isA<List<ZambianAgent>>());
      });

      test('should get agent by ID', () async {
        final agent = await agentService.getAgentById('ZM_AGENT_001');
        expect(agent, anyOf(isNull, isA<ZambianAgent>()));
      });
    });

    group('Agent Statistics', () {
      test('should get agent statistics', () async {
        final stats = await agentService.getAgentStatistics();

        expect(stats, isA<AgentStatistics>());
        expect(stats.totalAgents, isA<int>());
        expect(stats.activeAgents, isA<int>());
        expect(stats.availableServices, isA<List<String>>());
        expect(stats.cacheSize, isA<int>());
      });

      test('should format sync time correctly', () {
        final stats = AgentStatistics(
          totalAgents: 10,
          activeAgents: 8,
          availableServices: ['cash_in', 'cash_out'],
          lastSyncTime: DateTime.now().subtract(Duration(minutes: 5)),
          cacheSize: 10,
        );

        expect(stats.formattedLastSync, contains('minutes ago'));
      });

      test('should determine sync status', () {
        // Recent sync
        final recentStats = AgentStatistics(
          totalAgents: 10,
          activeAgents: 8,
          availableServices: ['cash_in', 'cash_out'],
          lastSyncTime: DateTime.now().subtract(Duration(hours: 1)),
          cacheSize: 10,
        );

        expect(recentStats.syncStatus, 'Up to date');

        // Old sync
        final oldStats = AgentStatistics(
          totalAgents: 10,
          activeAgents: 8,
          availableServices: ['cash_in', 'cash_out'],
          lastSyncTime: DateTime.now().subtract(Duration(days: 2)),
          cacheSize: 10,
        );

        expect(oldStats.syncStatus, 'Outdated');

        // Never synced
        final neverSyncedStats = AgentStatistics(
          totalAgents: 0,
          activeAgents: 0,
          availableServices: [],
          lastSyncTime: null,
          cacheSize: 0,
        );

        expect(neverSyncedStats.syncStatus, 'Not synced');
      });
    });

    group('Offline-First Functionality', () {
      test('should handle offline mode', () async {
        // Test offline functionality
        expect(agentService, isNotNull);
        // In a real implementation, this would test offline data access
      });

      test('should sync when connectivity is restored', () async {
        // Test connectivity restoration sync
        expect(agentService, isNotNull);
        // In a real implementation, this would test sync triggers
      });

      test('should cache agent data', () async {
        // Test agent data caching
        expect(agentService, isNotNull);
        // In a real implementation, this would test cache operations
      });
    });

    group('Zambian Geographic Features', () {
      test('should support major Zambian cities', () {
        // Test that agents are available in major cities
        final cities = [
          'Lusaka',
          'Kitwe',
          'Ndola',
          'Livingstone',
          'Kabwe',
          'Chingola',
          'Mufulira',
          'Luanshya',
        ];

        expect(cities.length, 8);
        expect(cities.contains('Lusaka'), true);
        expect(cities.contains('Kitwe'), true);
        expect(cities.contains('Livingstone'), true);
      });

      test('should handle Zambian coordinates', () {
        // Test Zambian coordinate ranges
        const zambiaMinLat = -18.0;
        const zambiaMaxLat = -8.0;
        const zambiaMinLng = 22.0;
        const zambiaMaxLng = 34.0;

        // Lusaka coordinates
        const lusakaLat = -15.3875;
        const lusakaLng = 28.3228;

        expect(lusakaLat >= zambiaMinLat && lusakaLat <= zambiaMaxLat, true);
        expect(lusakaLng >= zambiaMinLng && lusakaLng <= zambiaMaxLng, true);
      });
    });

    group('BoZ Compliance', () {
      test('should validate BoZ license format', () {
        final validLicense = 'BOZ/FA/2024/001';
        final invalidLicense = 'INVALID_LICENSE';

        expect(validLicense.startsWith('BOZ'), true);
        expect(invalidLicense.startsWith('BOZ'), false);
      });

      test('should track agent compliance status', () {
        final agent = ZambianAgent(
          agentId: 'ZM_AGENT_001',
          name: 'Test Agent',
          phoneNumber: '+260211234567',
          locationName: 'Test Location',
          latitude: -15.3875,
          longitude: 28.3228,
          services: ['cash_in', 'cash_out'],
          operatingHours: {},
          commissionRates: {},
          cashLimit: 50000.0,
          status: 'active',
          bozLicense: 'BOZ/FA/2024/001',
          lastUpdated: DateTime.now(),
        );

        expect(agent.status, 'active');
        expect(agent.bozLicense.startsWith('BOZ'), true);
      });
    });
  });

  group('🏛️ Zambia Financial Registry Tests', () {
    group('Registry Integration', () {
      test('should get approved agents', () async {
        final agents = await ZambiaFinancialRegistry.getApprovedAgents();
        expect(agents, isA<List<ZambianAgent>>());
      });

      test('should verify agent licenses', () async {
        final validLicense = await ZambiaFinancialRegistry.verifyAgentLicense('BOZ/FA/2024/001');
        final invalidLicense = await ZambiaFinancialRegistry.verifyAgentLicense('INVALID');

        expect(validLicense, true);
        expect(invalidLicense, false);
      });

      test('should fetch latest agents', () async {
        final agents = await ZambiaFinancialRegistry.fetchLatestAgents();
        expect(agents, isA<List<ZambianAgent>>());
      });
    });
  });
}
