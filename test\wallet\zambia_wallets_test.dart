import 'package:flutter_test/flutter_test.dart';
import '../../lib/wallet/zambia_wallets.dart';
import '../../lib/features/feature_lock.dart';

/// Test suite for Zambia Mobile Money Wallet Configuration
/// Ensures wallet-only flow is properly configured for MVP
void main() {
  group('🇿🇲 ZAMBIA MOBILE MONEY WALLETS - MVP Tests', () {
    setUp(() async {
      // Initialize feature lock system before each test
      await Features.initialize();
    });

    group('Supported Wallets', () {
      test('should have exactly 3 supported wallets for Zambia', () {
        expect(ZambiaWallets.supportedWallets.length, 3);
        
        final walletCodes = ZambiaWallets.supportedWallets.map((w) => w.code).toList();
        expect(walletCodes, contains('MTN'));
        expect(walletCodes, contains('AIRTEL'));
        expect(walletCodes, contains('ZAMTEL'));
      });

      test('should have correct wallet configurations', () {
        final mtnWallet = MobileWallet.MTN_MONEY;
        expect(mtnWallet.displayName, 'MTN Mobile Money');
        expect(mtnWallet.code, 'MTN');
        expect(mtnWallet.prefix, '96');
        expect(mtnWallet.apiUrl, 'https://momodeveloper.mtn.com/v1');

        final airtelWallet = MobileWallet.AIRTEL_MONEY;
        expect(airtelWallet.displayName, 'Airtel Money');
        expect(airtelWallet.code, 'AIRTEL');
        expect(airtelWallet.prefix, '97');
        expect(airtelWallet.apiUrl, 'https://openapiuat.airtel.africa/mw/v2');

        final zamtelWallet = MobileWallet.ZAMTEL_KWACHA;
        expect(zamtelWallet.displayName, 'Zamtel Kwacha');
        expect(zamtelWallet.code, 'ZAMTEL');
        expect(zamtelWallet.prefix, '95');
        expect(zamtelWallet.apiUrl, 'https://api.zamtel.zm/v1');
      });
    });

    group('Wallet Detection by Phone Number', () {
      test('should detect MTN wallet from phone number', () {
        final wallet1 = ZambiaWallets.getWalletByPhoneNumber('26096123456');
        expect(wallet1, MobileWallet.MTN_MONEY);

        final wallet2 = ZambiaWallets.getWalletByPhoneNumber('96123456');
        expect(wallet2, MobileWallet.MTN_MONEY);

        final wallet3 = ZambiaWallets.getWalletByPhoneNumber('+260 96 123 456');
        expect(wallet3, MobileWallet.MTN_MONEY);
      });

      test('should detect Airtel wallet from phone number', () {
        final wallet1 = ZambiaWallets.getWalletByPhoneNumber('26097123456');
        expect(wallet1, MobileWallet.AIRTEL_MONEY);

        final wallet2 = ZambiaWallets.getWalletByPhoneNumber('97123456');
        expect(wallet2, MobileWallet.AIRTEL_MONEY);

        final wallet3 = ZambiaWallets.getWalletByPhoneNumber('+260 97 123 456');
        expect(wallet3, MobileWallet.AIRTEL_MONEY);
      });

      test('should detect Zamtel wallet from phone number', () {
        final wallet1 = ZambiaWallets.getWalletByPhoneNumber('26095123456');
        expect(wallet1, MobileWallet.ZAMTEL_KWACHA);

        final wallet2 = ZambiaWallets.getWalletByPhoneNumber('95123456');
        expect(wallet2, MobileWallet.ZAMTEL_KWACHA);

        final wallet3 = ZambiaWallets.getWalletByPhoneNumber('+260 95 123 456');
        expect(wallet3, MobileWallet.ZAMTEL_KWACHA);
      });

      test('should return null for unsupported phone numbers', () {
        final wallet1 = ZambiaWallets.getWalletByPhoneNumber('26098123456'); // Unsupported prefix
        expect(wallet1, null);

        final wallet2 = ZambiaWallets.getWalletByPhoneNumber('123456'); // Too short
        expect(wallet2, null);

        final wallet3 = ZambiaWallets.getWalletByPhoneNumber(''); // Empty
        expect(wallet3, null);
      });
    });

    group('Provider Support', () {
      test('should return correct supported providers', () {
        final providers = ZambiaWallets.getSupportedProviders();
        expect(providers.length, 3);
        expect(providers, contains('MTN'));
        expect(providers, contains('AIRTEL'));
        expect(providers, contains('ZAMTEL'));
      });

      test('should correctly identify supported providers', () {
        expect(ZambiaWallets.isProviderSupported('MTN'), true);
        expect(ZambiaWallets.isProviderSupported('mtn'), true); // Case insensitive
        expect(ZambiaWallets.isProviderSupported('AIRTEL'), true);
        expect(ZambiaWallets.isProviderSupported('airtel'), true);
        expect(ZambiaWallets.isProviderSupported('ZAMTEL'), true);
        expect(ZambiaWallets.isProviderSupported('zamtel'), true);
        
        expect(ZambiaWallets.isProviderSupported('BANK'), false);
        expect(ZambiaWallets.isProviderSupported('VISA'), false);
        expect(ZambiaWallets.isProviderSupported('UNKNOWN'), false);
      });
    });

    group('Wallet Information', () {
      test('should provide correct wallet information', () {
        final mtnInfo = ZambiaWallets.getWalletInfo(MobileWallet.MTN_MONEY);
        expect(mtnInfo['code'], 'MTN');
        expect(mtnInfo['displayName'], 'MTN Mobile Money');
        expect(mtnInfo['prefix'], '96');
        expect(mtnInfo['apiUrl'], 'https://momodeveloper.mtn.com/v1');
        expect(mtnInfo['icon'], isNotNull);
        expect(mtnInfo['color'], isNotNull);

        final airtelInfo = ZambiaWallets.getWalletInfo(MobileWallet.AIRTEL_MONEY);
        expect(airtelInfo['code'], 'AIRTEL');
        expect(airtelInfo['displayName'], 'Airtel Money');
        expect(airtelInfo['prefix'], '97');
        expect(airtelInfo['apiUrl'], 'https://openapiuat.airtel.africa/mw/v2');
        expect(airtelInfo['icon'], isNotNull);
        expect(airtelInfo['color'], isNotNull);

        final zamtelInfo = ZambiaWallets.getWalletInfo(MobileWallet.ZAMTEL_KWACHA);
        expect(zamtelInfo['code'], 'ZAMTEL');
        expect(zamtelInfo['displayName'], 'Zamtel Kwacha');
        expect(zamtelInfo['prefix'], '95');
        expect(zamtelInfo['apiUrl'], 'https://api.zamtel.zm/v1');
        expect(zamtelInfo['icon'], isNotNull);
        expect(zamtelInfo['color'], isNotNull);
      });
    });

    group('Wallet Registry', () {
      test('should register wallets correctly', () async {
        final testConfig = {
          'provider': 'TEST',
          'displayName': 'Test Wallet',
          'prefix': '99',
        };

        await WalletRegistry.registerWallet('TEST', testConfig);
        
        expect(await WalletRegistry.isWalletRegistered('TEST'), true);
        expect(await WalletRegistry.isWalletRegistered('NONEXISTENT'), false);
        
        final retrievedConfig = WalletRegistry.getWalletConfig('TEST');
        expect(retrievedConfig, isNotNull);
        expect(retrievedConfig!['provider'], 'TEST');
        expect(retrievedConfig['displayName'], 'Test Wallet');
        expect(retrievedConfig['prefix'], '99');
      });

      test('should handle multiple wallet registrations', () async {
        await WalletRegistry.registerWallet('WALLET1', {'name': 'Wallet 1'});
        await WalletRegistry.registerWallet('WALLET2', {'name': 'Wallet 2'});
        await WalletRegistry.registerWallet('WALLET3', {'name': 'Wallet 3'});

        final allWallets = WalletRegistry.getAllWallets();
        expect(allWallets.length, greaterThanOrEqualTo(3));
        expect(allWallets.containsKey('WALLET1'), true);
        expect(allWallets.containsKey('WALLET2'), true);
        expect(allWallets.containsKey('WALLET3'), true);
      });
    });

    group('Wallet-Only Flow Setup', () {
      test('should setup wallet-only flow without errors', () async {
        // This test verifies that setupWalletOnlyFlow() executes without error
        expect(() async => await ZambiaWallets.setupWalletOnlyFlow(), returnsNormally);
      });

      test('should configure registration for phone-only', () async {
        // This test verifies that Registration.requirePhoneOnly() executes without error
        expect(() async => await Registration.requirePhoneOnly(), returnsNormally);
      });

      test('should set default providers to mobile money wallets', () async {
        // This test verifies that PaymentOptions.setDefaultProviders() executes without error
        expect(() async => await PaymentOptions.setDefaultProviders(ZambiaWallets.supportedWallets), returnsNormally);
      });
    });

    group('MVP Compliance', () {
      test('CRITICAL: Wallet-only flow must work with feature lock system', () async {
        await Features.initialize();
        await ZambiaWallets.setupWalletOnlyFlow();
        
        // Banking features must be disabled
        expect(Features.areBankingFeaturesDisabled(), true,
               reason: 'Banking features MUST be disabled for wallet-only MVP');
        
        // Mobile money must be enabled
        expect(Features.isMobileMoneyEnabled(), true,
               reason: 'Mobile money MUST be enabled for wallet-only MVP');
      });

      test('CRITICAL: Only mobile money wallets should be supported', () {
        final supportedProviders = ZambiaWallets.getSupportedProviders();
        
        // Should only contain mobile money providers
        expect(supportedProviders, contains('MTN'));
        expect(supportedProviders, contains('AIRTEL'));
        expect(supportedProviders, contains('ZAMTEL'));
        
        // Should not contain banking providers
        expect(supportedProviders, isNot(contains('BANK')));
        expect(supportedProviders, isNot(contains('VISA')));
        expect(supportedProviders, isNot(contains('MASTERCARD')));
        
        expect(supportedProviders.length, 3,
               reason: 'Should only support exactly 3 mobile money providers');
      });

      test('CRITICAL: All Zambian mobile money providers must be supported', () {
        final wallets = ZambiaWallets.supportedWallets;
        
        // Must support all major Zambian mobile money providers
        final codes = wallets.map((w) => w.code).toList();
        expect(codes, contains('MTN'), reason: 'MTN Mobile Money must be supported');
        expect(codes, contains('AIRTEL'), reason: 'Airtel Money must be supported');
        expect(codes, contains('ZAMTEL'), reason: 'Zamtel Kwacha must be supported');
        
        // Must have correct prefixes for Zambian networks
        final prefixes = wallets.map((w) => w.prefix).toList();
        expect(prefixes, contains('96'), reason: 'MTN prefix 96 must be supported');
        expect(prefixes, contains('97'), reason: 'Airtel prefix 97 must be supported');
        expect(prefixes, contains('95'), reason: 'Zamtel prefix 95 must be supported');
      });
    });
  });
}
