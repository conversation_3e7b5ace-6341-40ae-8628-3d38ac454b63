# Comprehensive Refresh Testing Script for Zambia Pay Mobile Money (PowerShell)
# Tests refresh functionality across different network profiles with data consistency validation
# Usage: .\test_refresh.ps1 -NetworkProfiles "2g,3g,4g,offline" -Iterations 25 -ValidateDataConsistency

param(
    [string]$NetworkProfiles = "2g,3g,4g,offline",
    [int]$Iterations = 25,
    [switch]$ValidateDataConsistency,
    [switch]$Verbose,
    [string]$OutputDir = "test_results",
    [int]$Timeout = 60,
    [switch]$Parallel,
    [switch]$Help
)

# Show help if requested
if ($Help) {
    Write-Host "Usage: .\test_refresh.ps1 [OPTIONS]"
    Write-Host "Options:"
    Write-Host "  -NetworkProfiles PROFILES    Comma-separated network profiles (2g,3g,4g,wifi,offline)"
    Write-Host "  -Iterations COUNT           Number of test iterations per profile (default: 25)"
    Write-Host "  -ValidateDataConsistency    Enable data consistency validation"
    Write-Host "  -Verbose                    Enable verbose output"
    Write-Host "  -OutputDir DIR              Output directory for test results"
    Write-Host "  -Timeout SECONDS            Timeout for each test (default: 60)"
    Write-Host "  -Parallel                   Run tests in parallel"
    Write-Host "  -Help                       Show this help message"
    exit 0
}

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Purple = "Magenta"

# Network profile configurations
$NetworkConfigs = @{
    "2g" = @{
        speed = 0.1
        latency = 1000
        packetLoss = 5
        jitter = 200
    }
    "3g" = @{
        speed = 1.0
        latency = 300
        packetLoss = 2
        jitter = 100
    }
    "4g" = @{
        speed = 10.0
        latency = 100
        packetLoss = 1
        jitter = 50
    }
    "wifi" = @{
        speed = 50.0
        latency = 20
        packetLoss = 0
        jitter = 10
    }
    "offline" = @{
        speed = 0
        latency = 9999
        packetLoss = 100
        jitter = 0
    }
}

# Test results tracking
$TestResults = @{}
$ConsistencyResults = @{}
$TotalTests = 0
$PassedTests = 0
$FailedTests = 0
$StartTime = Get-Date

# Logging function
function Write-Log {
    param(
        [string]$Level,
        [string]$Message
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$Level] $timestamp - $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor $Blue }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor $Green }
        "WARNING" { Write-Host $logMessage -ForegroundColor $Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor $Red }
        "DEBUG" { 
            if ($Verbose) { 
                Write-Host $logMessage -ForegroundColor $Purple 
            }
        }
    }
    
    # Save to log file
    if (-not (Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir | Out-Null
    }
    Add-Content -Path "$OutputDir\test.log" -Value $logMessage
}

# Initialize test environment
function Initialize-TestEnvironment {
    Write-Log "INFO" "Initializing test environment..."
    
    # Create output directory
    if (-not (Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir | Out-Null
    }
    
    # Check Flutter installation
    try {
        $flutterVersion = flutter --version 2>$null | Select-Object -First 1
        Write-Log "INFO" "Flutter version: $flutterVersion"
    }
    catch {
        Write-Log "ERROR" "Flutter is not installed or not in PATH"
        exit 1
    }
    
    # Check project dependencies
    if (-not (Test-Path "pubspec.yaml")) {
        Write-Log "ERROR" "pubspec.yaml not found. Are you in the project root?"
        exit 1
    }
    
    try {
        flutter pub get | Out-Null
        Write-Log "SUCCESS" "Dependencies are up to date"
    }
    catch {
        Write-Log "ERROR" "Failed to get dependencies"
        exit 1
    }
    
    Write-Log "SUCCESS" "Test environment initialized"
}

# Create network-specific test
function New-NetworkTest {
    param(
        [string]$NetworkProfile,
        [int]$Iteration
    )
    
    $testFile = "$OutputDir\test_${NetworkProfile}_${Iteration}.dart"
    $config = $NetworkConfigs[$NetworkProfile]
    
    $testContent = @"
import 'dart:async';
import 'dart:math';
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/refresh/smart_refresh_controller.dart';
import 'package:zambia_pay/core/refresh/connection_aware_refresher.dart';
import 'package:zambia_pay/core/network/network_quality_detector.dart';
import 'package:zambia_pay/core/data_usage/data_usage_monitor.dart';

void main() {
  group('${NetworkProfile.ToUpper()} Network Refresh Test - Iteration $Iteration', () {
    late SmartRefreshController refreshController;
    late ConnectionAwareRefresher connectionRefresher;
    late DataUsageMonitor dataMonitor;
    
    // Network simulation parameters
    final networkSpeed = $($config.speed); // Mbps
    final networkLatency = $($config.latency); // ms
    final packetLoss = $($config.packetLoss); // %
    final jitter = $($config.jitter); // ms
    
    setUp(() async {
      refreshController = SmartRefreshController();
      connectionRefresher = ConnectionAwareRefresher();
      dataMonitor = DataUsageMonitor();
      
      // Initialize services
      try {
        await refreshController.initialize();
        await connectionRefresher.initialize();
        await dataMonitor.initialize();
        await dataMonitor.resetAllUsage();
      } catch (e) {
        print('Setup warning: `$e');
      }
    });
    
    tearDown(() {
      refreshController.dispose();
      connectionRefresher.dispose();
    });
    
    test('should handle $NetworkProfile network conditions', () async {
      var refreshCount = 0;
      var fallbackCount = 0;
      
      // Simulate network conditions
      Future<String> simulateNetworkRequest() async {
        // Simulate network latency
        await Future.delayed(Duration(milliseconds: networkLatency));
        
        // Simulate packet loss
        if (Random().nextInt(100) < packetLoss) {
          throw Exception('Network packet lost');
        }
        
        // Simulate jitter
        final jitterDelay = Random().nextInt(jitter);
        await Future.delayed(Duration(milliseconds: jitterDelay));
        
        refreshCount++;
        return 'network_data_`${refreshCount}_$NetworkProfile';
      }
      
      Future<String> fallbackOperation() async {
        fallbackCount++;
        return 'cached_data_`${fallbackCount}_$NetworkProfile';
      }
      
      // Test multiple refresh operations
      final results = <String>[];
      for (int i = 0; i < 5; i++) {
        try {
          final result = await connectionRefresher.performRefresh(
            refreshFunction: simulateNetworkRequest,
            fallbackFunction: fallbackOperation,
            operationName: '${NetworkProfile}_test_`$i',
          ).timeout(Duration(seconds: $Timeout));
          
          results.add(result);
          
          // Track data usage
          await dataMonitor.trackDataUsage(
            dataUsedMB: networkSpeed > 0 ? 0.01 : 0.0,
            category: 'test',
            operation: '${NetworkProfile}_refresh',
          );
          
        } catch (e) {
          print('Refresh `$i failed: `$e');
          results.add('error_`$i');
        }
        
        // Small delay between requests
        await Future.delayed(Duration(milliseconds: 100));
      }
      
      // Validate results
      expect(results.length, equals(5));
      
      // Network-specific validations
      if (networkSpeed > 0) {
        // Online network - should have some successful refreshes
        final successfulRefreshes = results.where((r) => r.startsWith('network_data')).length;
        expect(successfulRefreshes, greaterThan(0), 
               reason: 'Should have at least one successful refresh on $NetworkProfile');
      } else {
        // Offline network - should use fallbacks
        final fallbacks = results.where((r) => r.startsWith('cached_data')).length;
        expect(fallbacks, greaterThan(0), 
               reason: 'Should use fallbacks when offline');
      }
      
      // Data consistency validation
      if ($($ValidateDataConsistency.ToString().ToLower())) {
        final usage = dataMonitor.getUsageSummary();
        final monthlyUsage = usage['monthly']['used'] as double;
        expect(monthlyUsage, lessThanOrEqualTo(2.0), 
               reason: 'Should not exceed 2MB monthly limit');
      }
      
      print('Test completed - Refreshes: `$refreshCount, Fallbacks: `$fallbackCount');
    });
    
    test('should maintain data consistency on $NetworkProfile', () async {
      if (!$($ValidateDataConsistency.ToString().ToLower())) {
        return;
      }
      
      final initialUsage = dataMonitor.monthlyDataUsage;
      var operationCount = 0;
      
      // Perform multiple operations
      for (int i = 0; i < 10; i++) {
        try {
          await connectionRefresher.performRefresh(
            refreshFunction: () async {
              await Future.delayed(Duration(milliseconds: networkLatency ~/ 2));
              operationCount++;
              return 'consistent_data_`$operationCount';
            },
            fallbackFunction: () async => 'consistent_fallback_`$operationCount',
            operationName: 'consistency_test_`$i',
          );
          
          // Track usage
          if (networkSpeed > 0) {
            await dataMonitor.trackDataUsage(
              dataUsedMB: 0.005, // 5KB per operation
              category: 'consistency',
              operation: 'test_`$i',
            );
          }
          
        } catch (e) {
          print('Consistency test `$i failed: `$e');
        }
      }
      
      final finalUsage = dataMonitor.monthlyDataUsage;
      final usageIncrease = finalUsage - initialUsage;
      
      // Validate data consistency
      if (networkSpeed > 0) {
        expect(usageIncrease, greaterThan(0), 
               reason: 'Data usage should increase with network operations');
        expect(usageIncrease, lessThanOrEqualTo(0.1), 
               reason: 'Data usage should be reasonable (max 100KB for 10 operations)');
      } else {
        expect(usageIncrease, equals(0), 
               reason: 'No data usage when offline');
      }
      
      print('Data consistency validated - Usage increase: `${usageIncrease.toStringAsFixed(3)}MB');
    });
  });
}
"@
    
    Set-Content -Path $testFile -Value $testContent
    return $testFile
}

# Run single network test
function Invoke-NetworkTest {
    param(
        [string]$NetworkProfile,
        [int]$Iteration
    )
    
    Write-Log "DEBUG" "Running $NetworkProfile test iteration $Iteration"
    
    $testFile = New-NetworkTest -NetworkProfile $NetworkProfile -Iteration $Iteration
    $resultFile = "$OutputDir\result_${NetworkProfile}_${Iteration}.json"
    
    # Run the test
    $startTime = Get-Date
    try {
        flutter test $testFile --reporter=json > $resultFile 2>&1
        $testResult = $LASTEXITCODE -eq 0
    }
    catch {
        $testResult = $false
    }
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds
    
    # Clean up test file
    Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
    
    # Update results
    $script:TotalTests++
    if ($testResult) {
        $script:PassedTests++
        $TestResults["${NetworkProfile}_${Iteration}"] = "PASSED"
        Write-Log "SUCCESS" "$NetworkProfile iteration $Iteration passed ($([math]::Round($duration, 1))s)"
    }
    else {
        $script:FailedTests++
        $TestResults["${NetworkProfile}_${Iteration}"] = "FAILED"
        Write-Log "ERROR" "$NetworkProfile iteration $Iteration failed ($([math]::Round($duration, 1))s)"
    }
    
    return $testResult
}

# Run tests for a network profile
function Invoke-ProfileTests {
    param([string]$NetworkProfile)
    
    Write-Log "INFO" "Testing $NetworkProfile network profile ($Iterations iterations)"
    
    $profilePassed = 0
    $profileFailed = 0
    
    if ($Parallel) {
        # Run tests in parallel using PowerShell jobs
        $jobs = @()
        for ($i = 1; $i -le $Iterations; $i++) {
            $job = Start-Job -ScriptBlock {
                param($NetworkProfile, $Iteration, $OutputDir, $Timeout, $ValidateDataConsistency)
                
                # Import functions and run test
                # Note: This is simplified - in practice, you'd need to import the full script context
                return Invoke-NetworkTest -NetworkProfile $NetworkProfile -Iteration $Iteration
            } -ArgumentList $NetworkProfile, $i, $OutputDir, $Timeout, $ValidateDataConsistency
            
            $jobs += $job
        }
        
        # Wait for all jobs to complete
        $jobs | Wait-Job | Receive-Job
        $jobs | Remove-Job
    }
    else {
        # Run tests sequentially
        for ($i = 1; $i -le $Iterations; $i++) {
            if (Invoke-NetworkTest -NetworkProfile $NetworkProfile -Iteration $i) {
                $profilePassed++
            }
            else {
                $profileFailed++
            }
        }
    }
    
    $successRate = if ($Iterations -gt 0) { [math]::Round(($profilePassed * 100) / $Iterations, 1) } else { 0 }
    Write-Log "INFO" "$NetworkProfile profile completed: $profilePassed/$Iterations passed ($successRate%)"
}

# Validate data consistency across all tests
function Test-DataConsistency {
    if (-not $ValidateDataConsistency) {
        return
    }
    
    Write-Log "INFO" "Validating data consistency across all tests..."
    
    $consistencyTest = "$OutputDir\consistency_validation.dart"
    
    $consistencyContent = @"
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/data_usage/data_usage_monitor.dart';

void main() {
  test('Data Consistency Validation', () async {
    final monitor = DataUsageMonitor();
    await monitor.initialize();
    
    final usage = monitor.getUsageSummary();
    final monthlyUsage = usage['monthly']['used'] as double;
    final dailyUsage = usage['daily']['used'] as double;
    
    // Validate monthly limit compliance
    expect(monthlyUsage, lessThanOrEqualTo(2.0), 
           reason: 'Monthly usage should not exceed 2MB limit');
    
    // Validate daily usage is reasonable
    expect(dailyUsage, lessThanOrEqualTo(0.2), 
           reason: 'Daily usage should be reasonable');
    
    // Validate usage tracking accuracy
    expect(monthlyUsage, greaterThanOrEqualTo(0), 
           reason: 'Usage should be non-negative');
    
    print('Consistency validation passed - Monthly: `${monthlyUsage.toStringAsFixed(3)}MB, Daily: `${dailyUsage.toStringAsFixed(3)}MB');
  });
}
"@
    
    Set-Content -Path $consistencyTest -Value $consistencyContent
    
    try {
        flutter test $consistencyTest --reporter=expanded
        $consistencyResult = $LASTEXITCODE -eq 0
    }
    catch {
        $consistencyResult = $false
    }
    
    Remove-Item -Path $consistencyTest -Force -ErrorAction SilentlyContinue
    
    if ($consistencyResult) {
        Write-Log "SUCCESS" "Data consistency validation passed"
        $ConsistencyResults["overall"] = "PASSED"
    }
    else {
        Write-Log "ERROR" "Data consistency validation failed"
        $ConsistencyResults["overall"] = "FAILED"
    }
}

# Generate comprehensive test report
function New-TestReport {
    Write-Log "INFO" "Generating comprehensive test report..."
    
    $reportFile = "$OutputDir\refresh_test_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    $successRate = if ($TotalTests -gt 0) { [math]::Round(($PassedTests * 100) / $TotalTests, 1) } else { 0 }
    
    $reportContent = @"
# Zambia Pay Refresh Testing Report

## Test Configuration
- **Network Profiles**: $NetworkProfiles
- **Iterations per Profile**: $Iterations
- **Data Consistency Validation**: $ValidateDataConsistency
- **Parallel Testing**: $Parallel
- **Test Date**: $(Get-Date)

## Overall Results
- **Total Tests**: $TotalTests
- **Passed**: $PassedTests
- **Failed**: $FailedTests
- **Success Rate**: $successRate%

## Network Profile Results

"@
    
    # Add profile-specific results
    $profiles = $NetworkProfiles -split ','
    foreach ($profile in $profiles) {
        $profilePassed = 0
        $profileTotal = 0
        
        foreach ($key in $TestResults.Keys) {
            if ($key -like "${profile}_*") {
                $profileTotal++
                if ($TestResults[$key] -eq "PASSED") {
                    $profilePassed++
                }
            }
        }
        
        $profileSuccessRate = if ($profileTotal -gt 0) { [math]::Round(($profilePassed * 100) / $profileTotal, 1) } else { 0 }
        $config = $NetworkConfigs[$profile]
        
        $reportContent += @"
### $($profile.ToUpper()) Network
- **Tests**: $profileTotal
- **Passed**: $profilePassed
- **Success Rate**: $profileSuccessRate%
- **Configuration**: Speed=$($config.speed)Mbps, Latency=$($config.latency)ms, Loss=$($config.packetLoss)%, Jitter=$($config.jitter)ms

"@
    }
    
    # Add consistency results
    if ($ValidateDataConsistency) {
        $consistencyStatus = if ($ConsistencyResults["overall"]) { $ConsistencyResults["overall"] } else { "NOT_RUN" }
        $reportContent += @"
## Data Consistency Validation
- **Overall Consistency**: $consistencyStatus

"@
    }
    
    # Add recommendations
    $reportContent += @"
## Recommendations

"@
    
    if ($successRate -ge 90) {
        $reportContent += "✅ **Excellent Performance** - Refresh system is working optimally across all network conditions.`n"
    }
    elseif ($successRate -ge 80) {
        $reportContent += "⚠️ **Good Performance** - Refresh system is working well with minor issues on some network profiles.`n"
    }
    elseif ($successRate -ge 70) {
        $reportContent += "⚠️ **Acceptable Performance** - Refresh system needs optimization for poor network conditions.`n"
    }
    else {
        $reportContent += "❌ **Poor Performance** - Refresh system requires significant improvements.`n"
    }
    
    $testDuration = (Get-Date) - $StartTime
    $reportContent += @"

## Test Environment
- **Flutter Version**: $(flutter --version | Select-Object -First 1)
- **Test Duration**: $($testDuration.ToString("hh\:mm\:ss"))
- **Output Directory**: $OutputDir

---
*Generated by Zambia Pay Refresh Testing Suite*
"@
    
    Set-Content -Path $reportFile -Value $reportContent
    Write-Log "SUCCESS" "Test report generated: $reportFile"
    
    if ($Verbose) {
        Get-Content $reportFile | Write-Host
    }
}

# Main execution
function Main {
    Write-Log "INFO" "Starting Zambia Pay Refresh Testing Suite"
    Write-Log "INFO" "Profiles: $NetworkProfiles, Iterations: $Iterations, Consistency: $ValidateDataConsistency"
    
    # Initialize
    Initialize-TestEnvironment
    
    # Parse and validate network profiles
    $profiles = $NetworkProfiles -split ','
    foreach ($profile in $profiles) {
        if (-not $NetworkConfigs.ContainsKey($profile)) {
            Write-Log "ERROR" "Unknown network profile: $profile"
            Write-Log "INFO" "Available profiles: $($NetworkConfigs.Keys -join ', ')"
            exit 1
        }
    }
    
    # Run tests for each profile
    foreach ($profile in $profiles) {
        Invoke-ProfileTests -NetworkProfile $profile
    }
    
    # Validate data consistency
    Test-DataConsistency
    
    # Generate report
    New-TestReport
    
    # Final summary
    $successRate = if ($TotalTests -gt 0) { [math]::Round(($PassedTests * 100) / $TotalTests, 1) } else { 0 }
    Write-Log "INFO" "=== TEST SUMMARY ==="
    Write-Log "INFO" "Total Tests: $TotalTests"
    Write-Log "SUCCESS" "Passed: $PassedTests"
    if ($FailedTests -gt 0) {
        Write-Log "ERROR" "Failed: $FailedTests"
    }
    else {
        Write-Log "SUCCESS" "Failed: $FailedTests"
    }
    
    if ($successRate -ge 80) {
        Write-Log "SUCCESS" "Success Rate: $successRate% - TESTS PASSED"
        exit 0
    }
    else {
        Write-Log "ERROR" "Success Rate: $successRate% - TESTS FAILED"
        exit 1
    }
}

# Run main function
Main
