#!/bin/bash

# Comprehensive Refresh Testing Script for Zambia Pay Mobile Money
# Tests refresh functionality across different network profiles with data consistency validation
# Usage: ./test_refresh.sh --network-profiles="2g,3g,4g,offline" --iterations=25 --validate-data-consistency

set -e

# Default values
NETWORK_PROFILES="2g,3g,4g,offline"
ITERATIONS=25
VALIDATE_DATA_CONSISTENCY=false
VERBOSE=false
OUTPUT_DIR="test_results"
TIMEOUT=60
PARALLEL_TESTS=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --network-profiles=*)
            NETWORK_PROFILES="${1#*=}"
            shift
            ;;
        --iterations=*)
            ITERATIONS="${1#*=}"
            shift
            ;;
        --validate-data-consistency)
            VALIDATE_DATA_CONSISTENCY=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --output-dir=*)
            OUTPUT_DIR="${1#*=}"
            shift
            ;;
        --timeout=*)
            TIMEOUT="${1#*=}"
            shift
            ;;
        --parallel)
            PARALLEL_TESTS=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --network-profiles=PROFILES  Comma-separated network profiles (2g,3g,4g,wifi,offline)"
            echo "  --iterations=COUNT           Number of test iterations per profile (default: 25)"
            echo "  --validate-data-consistency  Enable data consistency validation"
            echo "  --verbose                    Enable verbose output"
            echo "  --output-dir=DIR            Output directory for test results"
            echo "  --timeout=SECONDS           Timeout for each test (default: 60)"
            echo "  --parallel                  Run tests in parallel"
            echo "  --help                      Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} ${timestamp} - $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} ${timestamp} - $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
        "DEBUG")
            if [[ $VERBOSE == true ]]; then
                echo -e "${PURPLE}[DEBUG]${NC} ${timestamp} - $message"
            fi
            ;;
    esac
    
    # Save to log file
    echo "[$level] $timestamp - $message" >> "$OUTPUT_DIR/test.log"
}

# Network profile configurations
declare -A NETWORK_CONFIGS
NETWORK_CONFIGS["2g"]="speed=0.1,latency=1000,packet_loss=5,jitter=200"
NETWORK_CONFIGS["3g"]="speed=1.0,latency=300,packet_loss=2,jitter=100"
NETWORK_CONFIGS["4g"]="speed=10.0,latency=100,packet_loss=1,jitter=50"
NETWORK_CONFIGS["wifi"]="speed=50.0,latency=20,packet_loss=0,jitter=10"
NETWORK_CONFIGS["offline"]="speed=0,latency=9999,packet_loss=100,jitter=0"

# Test results tracking
declare -A TEST_RESULTS
declare -A CONSISTENCY_RESULTS
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Initialize test environment
initialize_test_env() {
    log "INFO" "Initializing test environment..."
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR"
    
    # Check Flutter installation
    if ! command -v flutter &> /dev/null; then
        log "ERROR" "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Check project dependencies
    if [[ ! -f "pubspec.yaml" ]]; then
        log "ERROR" "pubspec.yaml not found. Are you in the project root?"
        exit 1
    fi
    
    flutter pub get > /dev/null 2>&1
    if [[ $? -eq 0 ]]; then
        log "SUCCESS" "Dependencies are up to date"
    else
        log "ERROR" "Failed to get dependencies"
        exit 1
    fi
    
    log "SUCCESS" "Test environment initialized"
}

# Create network-specific test
create_network_test() {
    local network_profile=$1
    local iteration=$2
    local test_file="$OUTPUT_DIR/test_${network_profile}_${iteration}.dart"
    
    local config=${NETWORK_CONFIGS[$network_profile]}
    IFS=',' read -ra CONFIG_PARTS <<< "$config"
    
    local speed latency packet_loss jitter
    for part in "${CONFIG_PARTS[@]}"; do
        IFS='=' read -ra KV <<< "$part"
        case ${KV[0]} in
            "speed") speed=${KV[1]} ;;
            "latency") latency=${KV[1]} ;;
            "packet_loss") packet_loss=${KV[1]} ;;
            "jitter") jitter=${KV[1]} ;;
        esac
    done
    
    cat > "$test_file" << EOF
import 'dart:async';
import 'dart:math';
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/refresh/smart_refresh_controller.dart';
import 'package:zambia_pay/core/refresh/connection_aware_refresher.dart';
import 'package:zambia_pay/core/network/network_quality_detector.dart';
import 'package:zambia_pay/core/data_usage/data_usage_monitor.dart';

void main() {
  group('${network_profile^^} Network Refresh Test - Iteration $iteration', () {
    late SmartRefreshController refreshController;
    late ConnectionAwareRefresher connectionRefresher;
    late DataUsageMonitor dataMonitor;
    
    // Network simulation parameters
    final networkSpeed = $speed; // Mbps
    final networkLatency = $latency; // ms
    final packetLoss = $packet_loss; // %
    final jitter = $jitter; // ms
    
    setUp(() async {
      refreshController = SmartRefreshController();
      connectionRefresher = ConnectionAwareRefresher();
      dataMonitor = DataUsageMonitor();
      
      // Initialize services
      try {
        await refreshController.initialize();
        await connectionRefresher.initialize();
        await dataMonitor.initialize();
        await dataMonitor.resetAllUsage();
      } catch (e) {
        print('Setup warning: \$e');
      }
    });
    
    tearDown(() {
      refreshController.dispose();
      connectionRefresher.dispose();
    });
    
    test('should handle $network_profile network conditions', () async {
      final testData = <String, dynamic>{};
      var refreshCount = 0;
      var fallbackCount = 0;
      
      // Simulate network conditions
      Future<String> simulateNetworkRequest() async {
        // Simulate network latency
        await Future.delayed(Duration(milliseconds: networkLatency));
        
        // Simulate packet loss
        if (Random().nextInt(100) < packetLoss) {
          throw Exception('Network packet lost');
        }
        
        // Simulate jitter
        final jitterDelay = Random().nextInt(jitter);
        await Future.delayed(Duration(milliseconds: jitterDelay));
        
        refreshCount++;
        return 'network_data_\${refreshCount}_${network_profile}';
      }
      
      Future<String> fallbackOperation() async {
        fallbackCount++;
        return 'cached_data_\${fallbackCount}_${network_profile}';
      }
      
      // Test multiple refresh operations
      final results = <String>[];
      for (int i = 0; i < 5; i++) {
        try {
          final result = await connectionRefresher.performRefresh(
            refreshFunction: simulateNetworkRequest,
            fallbackFunction: fallbackOperation,
            operationName: '${network_profile}_test_\$i',
          ).timeout(Duration(seconds: $TIMEOUT));
          
          results.add(result);
          
          // Track data usage
          await dataMonitor.trackDataUsage(
            dataUsedMB: networkSpeed > 0 ? 0.01 : 0.0,
            category: 'test',
            operation: '${network_profile}_refresh',
          );
          
        } catch (e) {
          print('Refresh \$i failed: \$e');
          results.add('error_\$i');
        }
        
        // Small delay between requests
        await Future.delayed(Duration(milliseconds: 100));
      }
      
      // Validate results
      expect(results.length, equals(5));
      
      // Network-specific validations
      if (networkSpeed > 0) {
        // Online network - should have some successful refreshes
        final successfulRefreshes = results.where((r) => r.startsWith('network_data')).length;
        expect(successfulRefreshes, greaterThan(0), 
               reason: 'Should have at least one successful refresh on $network_profile');
      } else {
        // Offline network - should use fallbacks
        final fallbacks = results.where((r) => r.startsWith('cached_data')).length;
        expect(fallbacks, greaterThan(0), 
               reason: 'Should use fallbacks when offline');
      }
      
      // Data consistency validation
      if ($VALIDATE_DATA_CONSISTENCY) {
        final usage = dataMonitor.getUsageSummary();
        final monthlyUsage = usage['monthly']['used'] as double;
        expect(monthlyUsage, lessThanOrEqualTo(2.0), 
               reason: 'Should not exceed 2MB monthly limit');
      }
      
      print('Test completed - Refreshes: \$refreshCount, Fallbacks: \$fallbackCount');
    });
    
    test('should maintain data consistency on $network_profile', () async {
      if (!$VALIDATE_DATA_CONSISTENCY) {
        return;
      }
      
      final initialUsage = dataMonitor.monthlyDataUsage;
      var operationCount = 0;
      
      // Perform multiple operations
      for (int i = 0; i < 10; i++) {
        try {
          await connectionRefresher.performRefresh(
            refreshFunction: () async {
              await Future.delayed(Duration(milliseconds: networkLatency ~/ 2));
              operationCount++;
              return 'consistent_data_\$operationCount';
            },
            fallbackFunction: () async => 'consistent_fallback_\$operationCount',
            operationName: 'consistency_test_\$i',
          );
          
          // Track usage
          if (networkSpeed > 0) {
            await dataMonitor.trackDataUsage(
              dataUsedMB: 0.005, // 5KB per operation
              category: 'consistency',
              operation: 'test_\$i',
            );
          }
          
        } catch (e) {
          print('Consistency test \$i failed: \$e');
        }
      }
      
      final finalUsage = dataMonitor.monthlyDataUsage;
      final usageIncrease = finalUsage - initialUsage;
      
      // Validate data consistency
      if (networkSpeed > 0) {
        expect(usageIncrease, greaterThan(0), 
               reason: 'Data usage should increase with network operations');
        expect(usageIncrease, lessThanOrEqualTo(0.1), 
               reason: 'Data usage should be reasonable (max 100KB for 10 operations)');
      } else {
        expect(usageIncrease, equals(0), 
               reason: 'No data usage when offline');
      }
      
      print('Data consistency validated - Usage increase: \${usageIncrease.toStringAsFixed(3)}MB');
    });
  });
}
EOF
    
    echo "$test_file"
}

# Run single network test
run_network_test() {
    local network_profile=$1
    local iteration=$2
    
    log "DEBUG" "Running $network_profile test iteration $iteration"
    
    local test_file=$(create_network_test "$network_profile" "$iteration")
    local result_file="$OUTPUT_DIR/result_${network_profile}_${iteration}.json"
    
    # Run the test
    local start_time=$(date +%s)
    flutter test "$test_file" --reporter=json > "$result_file" 2>&1
    local test_result=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Clean up test file
    rm -f "$test_file"
    
    # Update results
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if [[ $test_result -eq 0 ]]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS["${network_profile}_${iteration}"]="PASSED"
        log "SUCCESS" "$network_profile iteration $iteration passed (${duration}s)"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS["${network_profile}_${iteration}"]="FAILED"
        log "ERROR" "$network_profile iteration $iteration failed (${duration}s)"
    fi
    
    return $test_result
}

# Run tests for a network profile
run_profile_tests() {
    local network_profile=$1
    
    log "INFO" "Testing $network_profile network profile ($ITERATIONS iterations)"
    
    local profile_passed=0
    local profile_failed=0
    
    if [[ $PARALLEL_TESTS == true ]]; then
        # Run tests in parallel
        local pids=()
        for ((i=1; i<=ITERATIONS; i++)); do
            run_network_test "$network_profile" "$i" &
            pids+=($!)
            
            # Limit concurrent processes
            if [[ ${#pids[@]} -ge 5 ]]; then
                wait ${pids[0]}
                pids=("${pids[@]:1}")
            fi
        done
        
        # Wait for remaining processes
        for pid in "${pids[@]}"; do
            wait $pid
        done
    else
        # Run tests sequentially
        for ((i=1; i<=ITERATIONS; i++)); do
            if run_network_test "$network_profile" "$i"; then
                profile_passed=$((profile_passed + 1))
            else
                profile_failed=$((profile_failed + 1))
            fi
        done
    fi
    
    local success_rate=$(( (profile_passed * 100) / ITERATIONS ))
    log "INFO" "$network_profile profile completed: $profile_passed/$ITERATIONS passed (${success_rate}%)"
}

# Validate data consistency across all tests
validate_data_consistency() {
    if [[ $VALIDATE_DATA_CONSISTENCY != true ]]; then
        return
    fi
    
    log "INFO" "Validating data consistency across all tests..."
    
    # Create consistency validation test
    local consistency_test="$OUTPUT_DIR/consistency_validation.dart"
    
    cat > "$consistency_test" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/data_usage/data_usage_monitor.dart';

void main() {
  test('Data Consistency Validation', () async {
    final monitor = DataUsageMonitor();
    await monitor.initialize();
    
    final usage = monitor.getUsageSummary();
    final monthlyUsage = usage['monthly']['used'] as double;
    final dailyUsage = usage['daily']['used'] as double;
    
    // Validate monthly limit compliance
    expect(monthlyUsage, lessThanOrEqualTo(2.0), 
           reason: 'Monthly usage should not exceed 2MB limit');
    
    // Validate daily usage is reasonable
    expect(dailyUsage, lessThanOrEqualTo(0.2), 
           reason: 'Daily usage should be reasonable');
    
    // Validate usage tracking accuracy
    expect(monthlyUsage, greaterThanOrEqualTo(0), 
           reason: 'Usage should be non-negative');
    
    print('Consistency validation passed - Monthly: \${monthlyUsage.toStringAsFixed(3)}MB, Daily: \${dailyUsage.toStringAsFixed(3)}MB');
  });
}
EOF
    
    flutter test "$consistency_test" --reporter=expanded
    local consistency_result=$?
    
    rm -f "$consistency_test"
    
    if [[ $consistency_result -eq 0 ]]; then
        log "SUCCESS" "Data consistency validation passed"
        CONSISTENCY_RESULTS["overall"]="PASSED"
    else
        log "ERROR" "Data consistency validation failed"
        CONSISTENCY_RESULTS["overall"]="FAILED"
    fi
}

# Generate comprehensive test report
generate_report() {
    log "INFO" "Generating comprehensive test report..."
    
    local report_file="$OUTPUT_DIR/refresh_test_report_$(date +%Y%m%d_%H%M%S).md"
    local success_rate=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
    
    cat > "$report_file" << EOF
# Zambia Pay Refresh Testing Report

## Test Configuration
- **Network Profiles**: $NETWORK_PROFILES
- **Iterations per Profile**: $ITERATIONS
- **Data Consistency Validation**: $VALIDATE_DATA_CONSISTENCY
- **Parallel Testing**: $PARALLEL_TESTS
- **Test Date**: $(date)

## Overall Results
- **Total Tests**: $TOTAL_TESTS
- **Passed**: $PASSED_TESTS
- **Failed**: $FAILED_TESTS
- **Success Rate**: ${success_rate}%

## Network Profile Results

EOF
    
    # Add profile-specific results
    IFS=',' read -ra PROFILES <<< "$NETWORK_PROFILES"
    for profile in "${PROFILES[@]}"; do
        local profile_passed=0
        local profile_total=0
        
        for key in "${!TEST_RESULTS[@]}"; do
            if [[ $key == ${profile}_* ]]; then
                profile_total=$((profile_total + 1))
                if [[ ${TEST_RESULTS[$key]} == "PASSED" ]]; then
                    profile_passed=$((profile_passed + 1))
                fi
            fi
        done
        
        local profile_success_rate=0
        if [[ $profile_total -gt 0 ]]; then
            profile_success_rate=$(( (profile_passed * 100) / profile_total ))
        fi
        
        cat >> "$report_file" << EOF
### ${profile^^} Network
- **Tests**: $profile_total
- **Passed**: $profile_passed
- **Success Rate**: ${profile_success_rate}%
- **Configuration**: ${NETWORK_CONFIGS[$profile]}

EOF
    done
    
    # Add consistency results
    if [[ $VALIDATE_DATA_CONSISTENCY == true ]]; then
        cat >> "$report_file" << EOF
## Data Consistency Validation
- **Overall Consistency**: ${CONSISTENCY_RESULTS["overall"]:-"NOT_RUN"}

EOF
    fi
    
    # Add recommendations
    cat >> "$report_file" << EOF
## Recommendations

EOF
    
    if [[ $success_rate -ge 90 ]]; then
        echo "✅ **Excellent Performance** - Refresh system is working optimally across all network conditions." >> "$report_file"
    elif [[ $success_rate -ge 80 ]]; then
        echo "⚠️ **Good Performance** - Refresh system is working well with minor issues on some network profiles." >> "$report_file"
    elif [[ $success_rate -ge 70 ]]; then
        echo "⚠️ **Acceptable Performance** - Refresh system needs optimization for poor network conditions." >> "$report_file"
    else
        echo "❌ **Poor Performance** - Refresh system requires significant improvements." >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Test Environment
- **Flutter Version**: $(flutter --version | head -n 1)
- **Test Duration**: $(date -d@$(($(date +%s) - START_TIME)) -u +%H:%M:%S)
- **Output Directory**: $OUTPUT_DIR

---
*Generated by Zambia Pay Refresh Testing Suite*
EOF
    
    log "SUCCESS" "Test report generated: $report_file"
    
    if [[ $VERBOSE == true ]]; then
        cat "$report_file"
    fi
}

# Main execution
main() {
    local START_TIME=$(date +%s)
    
    log "INFO" "Starting Zambia Pay Refresh Testing Suite"
    log "INFO" "Profiles: $NETWORK_PROFILES, Iterations: $ITERATIONS, Consistency: $VALIDATE_DATA_CONSISTENCY"
    
    # Initialize
    initialize_test_env
    
    # Parse network profiles
    IFS=',' read -ra PROFILES <<< "$NETWORK_PROFILES"
    
    # Validate profiles
    for profile in "${PROFILES[@]}"; do
        if [[ ! ${NETWORK_CONFIGS[$profile]+_} ]]; then
            log "ERROR" "Unknown network profile: $profile"
            log "INFO" "Available profiles: ${!NETWORK_CONFIGS[@]}"
            exit 1
        fi
    done
    
    # Run tests for each profile
    for profile in "${PROFILES[@]}"; do
        run_profile_tests "$profile"
    done
    
    # Validate data consistency
    validate_data_consistency
    
    # Generate report
    generate_report
    
    # Final summary
    local success_rate=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
    log "INFO" "=== TEST SUMMARY ==="
    log "INFO" "Total Tests: $TOTAL_TESTS"
    log "SUCCESS" "Passed: $PASSED_TESTS"
    if [[ $FAILED_TESTS -gt 0 ]]; then
        log "ERROR" "Failed: $FAILED_TESTS"
    else
        log "SUCCESS" "Failed: $FAILED_TESTS"
    fi
    
    if [[ $success_rate -ge 80 ]]; then
        log "SUCCESS" "Success Rate: ${success_rate}% - TESTS PASSED"
        exit 0
    else
        log "ERROR" "Success Rate: ${success_rate}% - TESTS FAILED"
        exit 1
    fi
}

# Run main function
main "$@"
