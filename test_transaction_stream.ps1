# Transaction Stream Testing Script for Zambia Pay Mobile Money (PowerShell)
# Tests real-time streaming functionality with MTN, Airtel, and Zamtel providers
# Usage: .\test_transaction_stream.ps1 -Provider MTN -TestCount 5 -Environment sandbox

param(
    [string]$Provider = "MTN",
    [int]$TestCount = 3,
    [string]$Environment = "sandbox",
    [switch]$Verbose,
    [string]$OutputFile = "",
    [int]$Timeout = 30,
    [switch]$Help
)

# Show help if requested
if ($Help) {
    Write-Host "Usage: .\test_transaction_stream.ps1 [OPTIONS]"
    Write-Host "Options:"
    Write-Host "  -Provider PROVIDER      Provider to test (MTN, AIRTEL, ZAMTEL, ALL)"
    Write-Host "  -TestCount COUNT        Number of test transactions (default: 3)"
    Write-Host "  -Environment ENV        Environment (sandbox, production)"
    Write-Host "  -Verbose               Enable verbose output"
    Write-Host "  -OutputFile FILE       Save results to file"
    Write-Host "  -Timeout SECONDS       Timeout for each test (default: 30)"
    Write-Host "  -Help                  Show this help message"
    exit 0
}

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Logging function
function Write-Log {
    param(
        [string]$Level,
        [string]$Message
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$Level] $timestamp - $Message"
    
    switch ($Level) {
        "INFO" { Write-Host $logMessage -ForegroundColor $Blue }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor $Green }
        "WARNING" { Write-Host $logMessage -ForegroundColor $Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor $Red }
    }
    
    # Save to output file if specified
    if ($OutputFile -ne "") {
        Add-Content -Path $OutputFile -Value $logMessage
    }
}

# Check if Flutter is installed
function Test-Flutter {
    try {
        $flutterVersion = flutter --version 2>$null | Select-Object -First 1
        Write-Log "INFO" "Flutter version: $flutterVersion"
        return $true
    }
    catch {
        Write-Log "ERROR" "Flutter is not installed or not in PATH"
        return $false
    }
}

# Check if project dependencies are installed
function Test-Dependencies {
    Write-Log "INFO" "Checking project dependencies..."
    
    if (-not (Test-Path "pubspec.yaml")) {
        Write-Log "ERROR" "pubspec.yaml not found. Are you in the project root?"
        return $false
    }
    
    try {
        flutter pub get | Out-Null
        Write-Log "SUCCESS" "Dependencies are up to date"
        return $true
    }
    catch {
        Write-Log "ERROR" "Failed to get dependencies"
        return $false
    }
}

# Validate provider
function Test-Provider {
    param([string]$ProviderName)
    
    $validProviders = @("MTN", "AIRTEL", "ZAMTEL", "ALL")
    if ($validProviders -contains $ProviderName) {
        Write-Log "INFO" "Testing provider: $ProviderName"
        return $true
    }
    else {
        Write-Log "ERROR" "Invalid provider: $ProviderName. Valid options: $($validProviders -join ', ')"
        return $false
    }
}

# Validate environment
function Test-Environment {
    param([string]$Env)
    
    $validEnvironments = @("sandbox", "production")
    if ($validEnvironments -contains $Env) {
        Write-Log "INFO" "Testing environment: $Env"
        
        if ($Env -eq "production") {
            Write-Log "WARNING" "Testing in PRODUCTION environment!"
            $response = Read-Host "Are you sure you want to continue? (y/N)"
            if ($response -ne "y" -and $response -ne "Y") {
                Write-Log "INFO" "Test cancelled by user"
                exit 0
            }
        }
        return $true
    }
    else {
        Write-Log "ERROR" "Invalid environment: $Env. Valid options: $($validEnvironments -join ', ')"
        return $false
    }
}

# Create test configuration
function New-TestConfig {
    Write-Log "INFO" "Creating test configuration..."
    
    if (-not (Test-Path "test")) {
        New-Item -ItemType Directory -Path "test" | Out-Null
    }
    
    $configFile = "test/test_config.json"
    $config = @{
        provider = $Provider
        environment = $Environment
        testCount = $TestCount
        timeout = $Timeout
        testPhoneNumbers = @{
            MTN = @{
                valid = "26096000001"
                success = "26096000001"
                failure = "26096000002"
                pending = "26096000003"
            }
            AIRTEL = @{
                valid = "26097000001"
                success = "26097000001"
                failure = "26097000002"
                pending = "26097000003"
            }
            ZAMTEL = @{
                valid = "26095000001"
                success = "26095000001"
                failure = "26095000002"
                pending = "26095000003"
            }
        }
        testAmounts = @(10.0, 50.0, 100.0, 500.0, 1000.0)
        expectedResults = @{
            success = @("COMPLETED", "SUCCESSFUL")
            failure = @("FAILED", "REJECTED")
            pending = @("PENDING", "PROCESSING")
        }
    }
    
    $config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile
    Write-Log "SUCCESS" "Test configuration created: $configFile"
}

# Run Flutter tests
function Invoke-FlutterTests {
    Write-Log "INFO" "Running Flutter unit tests..."
    
    try {
        flutter test test/features/mobile_money/momo_streaming_test.dart --reporter=expanded
        if ($LASTEXITCODE -eq 0) {
            Write-Log "SUCCESS" "Flutter unit tests passed"
            return $true
        }
        else {
            Write-Log "ERROR" "Flutter unit tests failed"
            return $false
        }
    }
    catch {
        Write-Log "ERROR" "Error running Flutter tests: $_"
        return $false
    }
}

# Test transaction streaming
function Test-TransactionStreaming {
    param(
        [string]$ProviderName,
        [int]$TestNumber
    )
    
    Write-Log "INFO" "Testing transaction streaming for $ProviderName (Test #$TestNumber)"
    
    # Create a temporary Dart test file
    $testFile = "test/temp_stream_test_${ProviderName}_${TestNumber}.dart"
    
    $testContent = @"
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/mobile_money/data/services/momo_streaming_service.dart';
import 'package:zambia_pay/core/config/mtn_sandbox_config.dart';
import 'package:zambia_pay/core/constants/app_constants.dart';

void main() {
  group('Transaction Stream Test - $ProviderName #$TestNumber', () {
    late MomoStreamingService streamingService;
    
    setUp(() async {
      streamingService = MomoStreamingService();
      await streamingService.initialize();
    });
    
    tearDown(() {
      streamingService.dispose();
    });
    
    test('should stream transactions for $ProviderName', () async {
      final completer = Completer<bool>();
      bool testPassed = false;
      
      // Listen to transaction stream
      final subscription = streamingService.realtimeTransactions.listen(
        (transactions) {
          print('Received `${transactions.length} transactions');

          // Validate transactions
          for (final tx in transactions) {
            if (tx.provider == AppConstants.provider$ProviderName) {
              print('Found $ProviderName transaction: `${tx.id}');
              testPassed = true;
              break;
            }
          }

          if (!completer.isCompleted) {
            completer.complete(testPassed);
          }
        },
        onError: (error) {
          print('Stream error: `$error');
          if (!completer.isCompleted) {
            completer.complete(false);
          }
        },
      );
      
      // Wait for result or timeout
      final result = await completer.future.timeout(
        Duration(seconds: $Timeout),
        onTimeout: () {
          print('Test timed out after $Timeout seconds');
          return false;
        },
      );
      
      await subscription.cancel();
      
      expect(result, isTrue, reason: 'Transaction stream test failed for $ProviderName');
    });
  });
}
"@
    
    Set-Content -Path $testFile -Value $testContent
    
    try {
        flutter test $testFile --reporter=expanded
        $testResult = $LASTEXITCODE -eq 0
        
        # Clean up
        Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
        
        if ($testResult) {
            Write-Log "SUCCESS" "Transaction streaming test passed for $ProviderName (Test #$TestNumber)"
            return $true
        }
        else {
            Write-Log "ERROR" "Transaction streaming test failed for $ProviderName (Test #$TestNumber)"
            return $false
        }
    }
    catch {
        Write-Log "ERROR" "Error running transaction streaming test: $_"
        Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
        return $false
    }
}

# Test balance streaming
function Test-BalanceStreaming {
    param([string]$ProviderName)
    
    Write-Log "INFO" "Testing balance streaming for $ProviderName"
    
    # Create a temporary Dart test file
    $testFile = "test/temp_balance_test_${ProviderName}.dart"
    
    $testContent = @"
import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/mobile_money/data/services/momo_streaming_service.dart';
import 'package:zambia_pay/core/constants/app_constants.dart';

void main() {
  group('Balance Stream Test - $ProviderName', () {
    late MomoStreamingService streamingService;
    
    setUp(() async {
      streamingService = MomoStreamingService();
      await streamingService.initialize();
    });
    
    tearDown(() {
      streamingService.dispose();
    });
    
    test('should stream balances for $ProviderName', () async {
      final completer = Completer<bool>();
      bool testPassed = false;
      
      // Listen to balance stream
      final subscription = streamingService.realtimeBalances.listen(
        (balances) {
          print('Received balances: `$balances');

          if (balances.containsKey(AppConstants.provider$ProviderName)) {
            final balance = balances[AppConstants.provider$ProviderName];
            print('$ProviderName balance: `$balance');
            testPassed = balance != null && balance >= 0;
          }
          
          if (!completer.isCompleted) {
            completer.complete(testPassed);
          }
        },
        onError: (error) {
          print('Balance stream error: `$error');
          if (!completer.isCompleted) {
            completer.complete(false);
          }
        },
      );
      
      // Wait for result or timeout
      final result = await completer.future.timeout(
        Duration(seconds: $Timeout),
        onTimeout: () {
          print('Balance test timed out after $Timeout seconds');
          return false;
        },
      );
      
      await subscription.cancel();
      
      expect(result, isTrue, reason: 'Balance stream test failed for $ProviderName');
    });
  });
}
"@
    
    Set-Content -Path $testFile -Value $testContent
    
    try {
        flutter test $testFile --reporter=expanded
        $testResult = $LASTEXITCODE -eq 0
        
        # Clean up
        Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
        
        if ($testResult) {
            Write-Log "SUCCESS" "Balance streaming test passed for $ProviderName"
            return $true
        }
        else {
            Write-Log "ERROR" "Balance streaming test failed for $ProviderName"
            return $false
        }
    }
    catch {
        Write-Log "ERROR" "Error running balance streaming test: $_"
        Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
        return $false
    }
}

# Generate test report
function New-TestReport {
    param(
        [int]$TotalTests,
        [int]$PassedTests,
        [int]$FailedTests
    )
    
    Write-Log "INFO" "Generating test report..."
    
    $reportFile = "test_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    $successRate = [math]::Round(($PassedTests * 100) / $TotalTests, 2)
    
    $reportContent = @"
=== ZAMBIA PAY MOBILE MONEY STREAMING TEST REPORT ===

Test Configuration:
- Provider: $Provider
- Environment: $Environment
- Test Count: $TestCount
- Timeout: $Timeout seconds
- Date: $(Get-Date)

Test Results:
- Total Tests: $TotalTests
- Passed: $PassedTests
- Failed: $FailedTests
- Success Rate: $successRate%

=== END OF REPORT ===
"@
    
    Set-Content -Path $reportFile -Value $reportContent
    Write-Log "SUCCESS" "Test report generated: $reportFile"
    
    if ($Verbose) {
        Get-Content $reportFile | Write-Host
    }
}

# Main execution
function Main {
    Write-Log "INFO" "Starting Zambia Pay Mobile Money Streaming Tests"
    Write-Log "INFO" "Provider: $Provider, Environment: $Environment, Test Count: $TestCount"
    
    # Initialize counters
    $totalTests = 0
    $passedTests = 0
    $failedTests = 0
    
    # Pre-flight checks
    if (-not (Test-Flutter)) { exit 1 }
    if (-not (Test-Dependencies)) { exit 1 }
    if (-not (Test-Provider $Provider)) { exit 1 }
    if (-not (Test-Environment $Environment)) { exit 1 }
    
    New-TestConfig
    
    # Run basic Flutter tests first
    if (Invoke-FlutterTests) {
        $passedTests++
    }
    else {
        $failedTests++
    }
    $totalTests++
    
    # Determine which providers to test
    $providersToTest = if ($Provider -eq "ALL") { @("MTN", "AIRTEL", "ZAMTEL") } else { @($Provider) }
    
    # Test each provider
    foreach ($providerName in $providersToTest) {
        Write-Log "INFO" "Testing provider: $providerName"
        
        # Test balance streaming
        if (Test-BalanceStreaming $providerName) {
            $passedTests++
        }
        else {
            $failedTests++
        }
        $totalTests++
        
        # Test transaction streaming multiple times
        for ($i = 1; $i -le $TestCount; $i++) {
            if (Test-TransactionStreaming $providerName $i) {
                $passedTests++
            }
            else {
                $failedTests++
            }
            $totalTests++
        }
    }
    
    # Generate final report
    New-TestReport $totalTests $passedTests $failedTests
    
    # Final summary
    Write-Log "INFO" "=== TEST SUMMARY ==="
    Write-Log "INFO" "Total Tests: $totalTests"
    Write-Log "SUCCESS" "Passed: $passedTests"
    if ($failedTests -gt 0) {
        Write-Log "ERROR" "Failed: $failedTests"
    }
    else {
        Write-Log "SUCCESS" "Failed: $failedTests"
    }
    
    $successRate = [math]::Round(($passedTests * 100) / $totalTests, 2)
    if ($successRate -ge 80) {
        Write-Log "SUCCESS" "Success Rate: $successRate% - TESTS PASSED"
        exit 0
    }
    else {
        Write-Log "ERROR" "Success Rate: $successRate% - TESTS FAILED"
        exit 1
    }
}

# Run main function
Main
