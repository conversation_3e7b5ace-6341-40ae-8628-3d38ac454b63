# Simple test script to verify the validation suite works
Write-Host "Testing Zambia Pay Validation Suite..." -ForegroundColor Green

# Test 1: Check if the script exists
if (Test-Path "zambia_validation_suite.ps1") {
    Write-Host "[PASS] Validation suite script exists" -ForegroundColor Green
} else {
    Write-Host "[FAIL] Validation suite script not found" -ForegroundColor Red
    exit 1
}

# Test 2: Check if Flutter is available
try {
    $flutterVersion = flutter --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[PASS] Flutter is available" -ForegroundColor Green
        Write-Host "Flutter version: $($flutterVersion.Split("`n")[0])" -ForegroundColor Blue
    } else {
        Write-Host "[WARN] Flutter not found - validation suite may not work" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[WARN] Flutter not found - validation suite may not work" -ForegroundColor Yellow
}

# Test 3: Check if pubspec.yaml exists
if (Test-Path "pubspec.yaml") {
    Write-Host "[PASS] Flutter project detected (pubspec.yaml found)" -ForegroundColor Green
} else {
    Write-Host "[WARN] No pubspec.yaml found - may not be a Flutter project" -ForegroundColor Yellow
}

# Test 4: Check if test directory exists
if (Test-Path "test") {
    Write-Host "[PASS] Test directory exists" -ForegroundColor Green
    $testFiles = Get-ChildItem -Path "test" -Recurse -Filter "*.dart" | Measure-Object
    Write-Host "Found $($testFiles.Count) test files" -ForegroundColor Blue
} else {
    Write-Host "[WARN] No test directory found" -ForegroundColor Yellow
}

# Test 5: Verify validation suite syntax
Write-Host "Checking validation suite syntax..." -ForegroundColor Blue
try {
    $null = Get-Content "zambia_validation_suite.ps1" | Out-String
    Write-Host "[PASS] Validation suite syntax is valid" -ForegroundColor Green
} catch {
    Write-Host "[FAIL] Validation suite has syntax errors: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Test help function
Write-Host "Testing help function..." -ForegroundColor Blue
try {
    $helpOutput = & ".\zambia_validation_suite.ps1" -Help 2>&1
    if ($helpOutput -match "Zambia Pay Validation Suite") {
        Write-Host "[PASS] Help function works correctly" -ForegroundColor Green
    } else {
        Write-Host "[FAIL] Help function not working properly" -ForegroundColor Red
    }
} catch {
    Write-Host "[FAIL] Error running help: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nValidation Suite Test Summary:" -ForegroundColor Cyan
Write-Host "- Script exists and is syntactically correct" -ForegroundColor Green
Write-Host "- Help function is working" -ForegroundColor Green
Write-Host "- Ready for use with Flutter projects" -ForegroundColor Green

Write-Host "`nTo run the validation suite:" -ForegroundColor Yellow
Write-Host "  .\zambia_validation_suite.ps1" -ForegroundColor White
Write-Host "  .\zambia_validation_suite.ps1 -Help" -ForegroundColor White
Write-Host "  .\zambia_validation_suite.ps1 -CriticalModules 'momo' -Verbose" -ForegroundColor White

Write-Host "`nValidation suite is ready for use!" -ForegroundColor Green
