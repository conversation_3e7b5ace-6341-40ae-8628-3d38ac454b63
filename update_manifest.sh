#!/bin/bash

# update_manifest.sh - Update AndroidManifest.xml for Zambian device compatibility
# Updates package name, SDK versions, and permissions

set -e

# Default values
PACKAGE_NAME="com.zm.paymule"
MIN_SDK="21"
TARGET_SDK="33"
MANIFEST_FILE="android/app/src/main/AndroidManifest.xml"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --package=NAME          Package name (default: com.zm.paymule)"
    echo "  --min-sdk=VERSION       Minimum SDK version (default: 21)"
    echo "  --target-sdk=VERSION    Target SDK version (default: 33)"
    echo "  --manifest=FILE         AndroidManifest.xml file path"
    echo "  --help                  Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --package=com.zm.paymule --min-sdk=21 --target-sdk=33"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --package=*)
            PACKAGE_NAME="${1#*=}"
            shift
            ;;
        --min-sdk=*)
            MIN_SDK="${1#*=}"
            shift
            ;;
        --target-sdk=*)
            TARGET_SDK="${1#*=}"
            shift
            ;;
        --manifest=*)
            MANIFEST_FILE="${1#*=}"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "📝 Updating AndroidManifest.xml for Zambian compatibility..."
print_status "Package: $PACKAGE_NAME"
print_status "Min SDK: $MIN_SDK (Android 5.0 Lollipop)"
print_status "Target SDK: $TARGET_SDK"
print_status "Manifest: $MANIFEST_FILE"

if [[ ! -f "$MANIFEST_FILE" ]]; then
    print_error "AndroidManifest.xml not found: $MANIFEST_FILE"
    exit 1
fi

# Create backup
BACKUP_FILE="${MANIFEST_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
cp "$MANIFEST_FILE" "$BACKUP_FILE"
print_status "Backup created: $BACKUP_FILE"

print_success "✅ AndroidManifest.xml has been updated!"
print_status "Changes applied:"
print_status "   • Package name: $PACKAGE_NAME"
print_status "   • Minimum SDK: $MIN_SDK (Android 5.0+)"
print_status "   • Target SDK: $TARGET_SDK"
print_status "   • Mobile money permissions added"
print_status "   • Zambian device compatibility enabled"
