#!/bin/bash

# Pay Mule App Name Validation Script
# Validates that app name appears correctly across all platforms and configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PLATFORMS=${1:-"android,ios"}
EXPECTED_APP_NAME="Pay Mule"
EXPECTED_PACKAGE_NAME="pay_mule"

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate Android app name
validate_android() {
    print_info "Validating Android app name..."
    
    local error_count=0
    
    # Check AndroidManifest.xml
    if [ -f "android/app/src/main/AndroidManifest.xml" ]; then
        if grep -q 'android:label="@string/app_name"' android/app/src/main/AndroidManifest.xml; then
            print_success "AndroidManifest.xml uses string resource"
        else
            print_error "AndroidManifest.xml does not use @string/app_name"
            ((error_count++))
        fi
    else
        print_error "AndroidManifest.xml not found"
        ((error_count++))
    fi
    
    # Check strings.xml
    if [ -f "android/app/src/main/res/values/strings.xml" ]; then
        if grep -q "<string name=\"app_name\">$EXPECTED_APP_NAME</string>" android/app/src/main/res/values/strings.xml; then
            print_success "strings.xml contains correct app name: $EXPECTED_APP_NAME"
        else
            print_error "strings.xml does not contain correct app name"
            ((error_count++))
        fi
    else
        print_error "strings.xml not found"
        ((error_count++))
    fi
    
    # Check build.gradle for applicationId
    if [ -f "android/app/build.gradle.kts" ]; then
        if grep -q "com.paymule" android/app/build.gradle.kts; then
            print_success "build.gradle.kts contains Pay Mule package identifier"
        else
            print_warning "build.gradle.kts may need package identifier update"
        fi
    fi
    
    return $error_count
}

# Validate iOS app name
validate_ios() {
    print_info "Validating iOS app name..."
    
    local error_count=0
    
    # Check Info.plist
    if [ -f "ios/Runner/Info.plist" ]; then
        if grep -A1 "CFBundleDisplayName" ios/Runner/Info.plist | grep -q "$EXPECTED_APP_NAME"; then
            print_success "Info.plist contains correct display name: $EXPECTED_APP_NAME"
        else
            print_error "Info.plist does not contain correct display name"
            ((error_count++))
        fi
        
        if grep -A1 "CFBundleName" ios/Runner/Info.plist | grep -q "pay_mule"; then
            print_success "Info.plist contains correct bundle name"
        else
            print_warning "Info.plist bundle name may need updating"
        fi
    else
        print_error "Info.plist not found"
        ((error_count++))
    fi
    
    # Check AppInfo.xcconfig
    if [ -f "macos/Runner/Configs/AppInfo.xcconfig" ]; then
        if grep -q "PRODUCT_NAME = $EXPECTED_APP_NAME" macos/Runner/Configs/AppInfo.xcconfig; then
            print_success "AppInfo.xcconfig contains correct product name"
        else
            print_error "AppInfo.xcconfig does not contain correct product name"
            ((error_count++))
        fi
    fi
    
    return $error_count
}

# Validate Flutter configuration
validate_flutter() {
    print_info "Validating Flutter configuration..."
    
    local error_count=0
    
    # Check pubspec.yaml
    if [ -f "pubspec.yaml" ]; then
        if grep -q "name: $EXPECTED_PACKAGE_NAME" pubspec.yaml; then
            print_success "pubspec.yaml contains correct package name: $EXPECTED_PACKAGE_NAME"
        else
            print_error "pubspec.yaml does not contain correct package name"
            ((error_count++))
        fi
        
        if grep -q "Pay Mule" pubspec.yaml; then
            print_success "pubspec.yaml description contains Pay Mule"
        else
            print_warning "pubspec.yaml description may need updating"
        fi
    else
        print_error "pubspec.yaml not found"
        ((error_count++))
    fi
    
    # Check app_config.dart
    if [ -f "lib/core/config/app_config.dart" ]; then
        if grep -q "appName = '$EXPECTED_APP_NAME'" lib/core/config/app_config.dart; then
            print_success "app_config.dart contains correct app name"
        else
            print_error "app_config.dart does not contain correct app name"
            ((error_count++))
        fi
    else
        print_error "app_config.dart not found"
        ((error_count++))
    fi
    
    return $error_count
}

# Validate desktop platforms
validate_desktop() {
    print_info "Validating desktop platform configurations..."
    
    local error_count=0
    
    # Check Linux CMakeLists.txt
    if [ -f "linux/CMakeLists.txt" ]; then
        if grep -q 'set(BINARY_NAME "pay_mule")' linux/CMakeLists.txt; then
            print_success "Linux CMakeLists.txt contains correct binary name"
        else
            print_error "Linux CMakeLists.txt does not contain correct binary name"
            ((error_count++))
        fi
    fi
    
    # Check Windows CMakeLists.txt
    if [ -f "windows/CMakeLists.txt" ]; then
        if grep -q 'project(pay_mule LANGUAGES CXX)' windows/CMakeLists.txt; then
            print_success "Windows CMakeLists.txt contains correct project name"
        else
            print_error "Windows CMakeLists.txt does not contain correct project name"
            ((error_count++))
        fi
    fi
    
    # Check Windows Runner.rc
    if [ -f "windows/runner/Runner.rc" ]; then
        if grep -q 'VALUE "ProductName", "Pay Mule"' windows/runner/Runner.rc; then
            print_success "Windows Runner.rc contains correct product name"
        else
            print_error "Windows Runner.rc does not contain correct product name"
            ((error_count++))
        fi
    fi
    
    return $error_count
}

# Test app name in transaction receipts (simulation)
test_transaction_receipts() {
    print_info "Testing app name in transaction contexts..."
    
    # This would typically involve running the app and checking UI elements
    # For now, we'll check that the app name is properly referenced in code
    
    local error_count=0
    
    # Search for hardcoded old app names
    local old_names=("Zambia Pay" "zambia_pay" "ZambiaPay")
    
    for old_name in "${old_names[@]}"; do
        local found_files=$(grep -r "$old_name" lib/ --include="*.dart" 2>/dev/null || true)
        if [ -n "$found_files" ]; then
            print_warning "Found references to old name '$old_name' in:"
            echo "$found_files" | while read -r line; do
                print_warning "  $line"
            done
            ((error_count++))
        fi
    done
    
    if [ $error_count -eq 0 ]; then
        print_success "No old app name references found in Dart code"
    fi
    
    return $error_count
}

# Validate Zambia naming registry compliance
validate_zambia_compliance() {
    print_info "Validating Zambia naming registry compliance..."
    
    # Check that the app name follows Zambian business naming conventions
    # This is a basic check - real compliance would require legal verification
    
    if [[ "$EXPECTED_APP_NAME" =~ ^[A-Za-z][A-Za-z0-9\ ]*$ ]]; then
        print_success "App name follows basic naming conventions"
    else
        print_error "App name contains invalid characters for Zambian registry"
        return 1
    fi
    
    # Check for trademark conflicts (basic check)
    print_info "Basic trademark conflict check..."
    if [[ "$EXPECTED_APP_NAME" == *"Bank"* ]] || [[ "$EXPECTED_APP_NAME" == *"Government"* ]]; then
        print_warning "App name may conflict with protected terms"
        return 1
    fi
    
    print_success "Basic Zambia compliance check passed"
    return 0
}

# Generate validation report
generate_report() {
    local total_errors=$1
    local report_file="app_name_validation_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
Pay Mule App Name Validation Report
===================================
Date: $(date)
Platforms Tested: $PLATFORMS
Expected App Name: $EXPECTED_APP_NAME
Expected Package Name: $EXPECTED_PACKAGE_NAME

Validation Results:
- Total Errors: $total_errors
- Status: $([ $total_errors -eq 0 ] && echo "PASSED" || echo "FAILED")

Platform-Specific Results:
$([ "$PLATFORMS" == *"android"* ] && echo "- Android: $(validate_android >/dev/null 2>&1 && echo "PASSED" || echo "FAILED")")
$([ "$PLATFORMS" == *"ios"* ] && echo "- iOS: $(validate_ios >/dev/null 2>&1 && echo "PASSED" || echo "FAILED")")
- Flutter: $(validate_flutter >/dev/null 2>&1 && echo "PASSED" || echo "FAILED")
- Desktop: $(validate_desktop >/dev/null 2>&1 && echo "PASSED" || echo "FAILED")

Compliance:
- Zambia Registry: $(validate_zambia_compliance >/dev/null 2>&1 && echo "PASSED" || echo "FAILED")

Recommendations:
$([ $total_errors -gt 0 ] && echo "- Fix the $total_errors validation errors before production deployment")
$([ $total_errors -eq 0 ] && echo "- App name validation passed. Ready for production deployment.")
- Test app name appearance on actual devices
- Verify app name in app store listings
- Confirm legal compliance with Zambian authorities

EOF
    
    print_success "Validation report generated: $report_file"
}

# Main execution
main() {
    print_info "Pay Mule App Name Validation"
    print_info "============================"
    print_info "Expected App Name: $EXPECTED_APP_NAME"
    print_info "Expected Package Name: $EXPECTED_PACKAGE_NAME"
    print_info "Platforms: $PLATFORMS"
    echo
    
    local total_errors=0
    
    # Run platform-specific validations
    if [[ "$PLATFORMS" == *"android"* ]]; then
        validate_android
        total_errors=$((total_errors + $?))
    fi
    
    if [[ "$PLATFORMS" == *"ios"* ]]; then
        validate_ios
        total_errors=$((total_errors + $?))
    fi
    
    # Always run Flutter and desktop validations
    validate_flutter
    total_errors=$((total_errors + $?))
    
    validate_desktop
    total_errors=$((total_errors + $?))
    
    test_transaction_receipts
    total_errors=$((total_errors + $?))
    
    validate_zambia_compliance
    total_errors=$((total_errors + $?))
    
    echo
    print_info "Validation Summary:"
    print_info "=================="
    
    if [ $total_errors -eq 0 ]; then
        print_success "All app name validations passed!"
        print_success "App name '$EXPECTED_APP_NAME' is correctly configured across all platforms"
    else
        print_error "App name validation failed with $total_errors errors"
        print_error "Please fix the issues before proceeding with production deployment"
    fi
    
    generate_report $total_errors
    
    return $total_errors
}

# Run main function
main
