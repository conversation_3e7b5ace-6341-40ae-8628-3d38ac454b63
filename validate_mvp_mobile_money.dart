#!/usr/bin/env dart

/// 🇿🇲 PAY MULE ZAMBIA MOBILE MONEY MVP VALIDATION
/// CORE MANDATE: Mobile money-only release • No banking features • Zero breakage
/// 
/// This script validates that the feature lock system is working correctly
/// and that the app is ready for mobile money-only MVP release.

import 'dart:io';

void main() async {
  print('🇿🇲 PAY MULE ZAMBIA MOBILE MONEY MVP VALIDATION');
  print('=' * 60);
  print('CORE MANDATE: Mobile money-only release • No banking features • Zero breakage');
  print('');

  final validator = MVPValidator();
  await validator.runValidation();
}

class MVPValidator {
  int _passedTests = 0;
  int _failedTests = 0;
  final List<String> _failures = [];

  Future<void> runValidation() async {
    print('🔍 Starting MVP validation...\n');

    // Phase 1: Feature Lock System Validation
    await _validateFeatureLockSystem();

    // Phase 2: Configuration Validation
    await _validateConfiguration();

    // Phase 3: Code Structure Validation
    await _validateCodeStructure();

    // Phase 4: Wallet-Only Flow Validation
    await _validateWalletOnlyFlow();

    // Phase 5: Mobile Money Refresh Validation
    await _validateMomoRefresh();

    // Phase 6: Mobile Money Notifications Validation
    await _validateMomoNotifications();

    // Phase 7: Test Suite Validation
    await _validateTestSuite();

    // Phase 6: Final MVP Compliance Check
    await _validateMVPCompliance();

    _printResults();
  }

  Future<void> _validateFeatureLockSystem() async {
    print('📋 PHASE 1: Feature Lock System Validation');
    print('-' * 40);

    _checkFile('lib/features/feature_lock.dart', 'Feature lock system file exists');
    _checkFileContains('lib/features/feature_lock.dart', 'class Features', 'Features class defined');
    _checkFileContains('lib/features/feature_lock.dart', 'BANK_LINKING', 'Banking features defined');
    _checkFileContains('lib/features/feature_lock.dart', 'MOBILE_MONEY', 'Mobile money features defined');
    _checkFileContains('lib/features/feature_lock.dart', 'disableBankingFeatures', 'Banking disable function exists');
    _checkFileContains('lib/features/feature_lock.dart', 'enableMobileMoneyCore', 'Mobile money enable function exists');
    _checkFileContains('lib/features/feature_lock.dart', 'mixin FeatureAware', 'FeatureAware mixin defined');

    print('');
  }

  Future<void> _validateConfiguration() async {
    print('⚙️ PHASE 2: Configuration Validation');
    print('-' * 40);

    _checkFileContains('lib/core/config/app_config.dart', 'bankLinking.*false', 'Banking features disabled in config');
    _checkFileContains('lib/core/config/app_config.dart', 'mobileMoneyTransfers.*true', 'Mobile money enabled in config');
    _checkFileContains('lib/core/constants/app_constants.dart', 'mobileMoneyProviderCodes', 'Mobile money provider codes defined');
    _checkFileContains('lib/core/constants/app_constants.dart', 'mvpFeatures', 'MVP features list defined');

    print('');
  }

  Future<void> _validateCodeStructure() async {
    print('🏗️ PHASE 3: Code Structure Validation');
    print('-' * 40);

    _checkFileContains('lib/main.dart', 'Features.initialize', 'Feature lock initialized in main');
    _checkFileContains('lib/presentation/dashboard/dashboard_screen.dart', 'FeatureAware', 'Dashboard uses FeatureAware mixin');
    _checkFileContains('lib/presentation/dashboard/dashboard_screen.dart', 'MOBILE MONEY MVP', 'Dashboard marked as MVP');

    print('');
  }

  Future<void> _validateWalletOnlyFlow() async {
    print('📱 PHASE 4: Wallet-Only Flow Validation');
    print('-' * 40);

    _checkFile('lib/wallet/zambia_wallets.dart', 'Zambia wallets configuration exists');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'enum MobileWallet', 'Mobile wallet enum defined');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'MTN_MONEY', 'MTN Mobile Money wallet defined');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'AIRTEL_MONEY', 'Airtel Money wallet defined');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'ZAMTEL_KWACHA', 'Zamtel Kwacha wallet defined');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'setupWalletOnlyFlow', 'Wallet-only flow setup function exists');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'requirePhoneOnly', 'Phone-only registration configured');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'setDefaultProviders', 'Default providers set to wallets');
    _checkFileContains('lib/main.dart', 'ZambiaWallets.setupWalletOnlyFlow', 'Wallet-only flow initialized in main');

    print('');
  }

  Future<void> _validateMomoRefresh() async {
    print('🔄 PHASE 5: Mobile Money Refresh Validation');
    print('-' * 40);

    _checkFile('lib/refresh/momo_refresh.dart', 'Mobile money refresh system exists');
    _checkFileContains('lib/refresh/momo_refresh.dart', 'class MomoRefreshController', 'Refresh controller defined');
    _checkFileContains('lib/refresh/momo_refresh.dart', 'enum RefreshTrigger', 'Refresh triggers defined');
    _checkFileContains('lib/refresh/momo_refresh.dart', 'enum RefreshAction', 'Refresh actions defined');
    _checkFileContains('lib/refresh/momo_refresh.dart', 'enableBalanceRefresh', 'Balance refresh function exists');
    _checkFileContains('lib/refresh/momo_refresh.dart', '2G.*optim', '2G optimization configured');
    _checkFileContains('lib/refresh/momo_refresh.dart', 'updateBalances.*syncTransactions', 'Core refresh actions defined');
    _checkFileContains('lib/main.dart', 'MomoRefreshController', 'Refresh system initialized in main');
    _checkFileContains('lib/main.dart', 'enableBalanceRefresh', 'Balance refresh enabled in main');

    print('');
  }

  Future<void> _validateMomoNotifications() async {
    print('🔔 PHASE 6: Mobile Money Notifications Validation');
    print('-' * 40);

    _checkFile('lib/notifications/momo_alerts.dart', 'Mobile money notifications system exists');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'class MomoNotificationService', 'Notification service defined');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'enum NotificationType', 'Notification types defined');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'MONEY_RECEIVED', 'Money received notification type defined');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'CHILIMBA_REQUEST', 'Chilimba request notification type defined');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'UTILITY_CONFIRMATION', 'Utility confirmation notification type defined');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'configureMomoNotifications', 'Configuration function exists');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'DeliveryGuarantee', 'Zambian delivery guarantee configured');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'ZambiaRetryPolicy', 'Retry policy configured');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'SMSNotification', 'SMS fallback configured');
    _checkFileContains('lib/main.dart', 'MomoNotificationService', 'Notification system initialized in main');
    _checkFileContains('lib/main.dart', 'configureMomoNotifications', 'Notifications configured in main');

    print('');
  }

  Future<void> _validateTestSuite() async {
    print('🧪 PHASE 7: Test Suite Validation');
    print('-' * 40);

    _checkFile('test/features/feature_lock_test.dart', 'Feature lock tests exist');
    _checkFileContains('test/features/feature_lock_test.dart', 'Banking features must be completely disabled', 'Critical banking disable test');
    _checkFileContains('test/features/feature_lock_test.dart', 'Mobile money core must be fully enabled', 'Critical mobile money enable test');
    _checkFileContains('test/features/feature_lock_test.dart', 'MVP mode must be mobile money only', 'Critical MVP mode test');

    _checkFile('test/wallet/zambia_wallets_test.dart', 'Wallet tests exist');
    _checkFileContains('test/wallet/zambia_wallets_test.dart', 'Wallet-only flow must work with feature lock system', 'Critical wallet-feature lock integration test');
    _checkFileContains('test/wallet/zambia_wallets_test.dart', 'Only mobile money wallets should be supported', 'Critical wallet-only test');
    _checkFileContains('test/wallet/zambia_wallets_test.dart', 'All Zambian mobile money providers must be supported', 'Critical provider support test');

    _checkFile('test/refresh/momo_refresh_test.dart', 'Refresh tests exist');
    _checkFileContains('test/refresh/momo_refresh_test.dart', 'Refresh system must work with feature lock', 'Critical refresh-feature lock integration test');
    _checkFileContains('test/refresh/momo_refresh_test.dart', 'Only mobile money refresh actions should be supported', 'Critical refresh actions test');
    _checkFileContains('test/refresh/momo_refresh_test.dart', 'Refresh must be optimized for Zambian networks', 'Critical 2G optimization test');

    _checkFile('test/notifications/momo_alerts_test.dart', 'Notification tests exist');
    _checkFileContains('test/notifications/momo_alerts_test.dart', 'Notification system must work with feature lock', 'Critical notification-feature lock integration test');
    _checkFileContains('test/notifications/momo_alerts_test.dart', 'Only mobile money notification types should be supported', 'Critical notification types test');
    _checkFileContains('test/notifications/momo_alerts_test.dart', 'Notifications must have Zambian delivery guarantee', 'Critical delivery guarantee test');

    print('');
  }

  Future<void> _validateMVPCompliance() async {
    print('✅ PHASE 8: Final MVP Compliance Check');
    print('-' * 40);

    // Check that no banking UI components exist
    _checkFileNotContains('lib/presentation/dashboard/dashboard_screen.dart', 'bank.*tab', 'No banking tabs in dashboard');
    _checkFileNotContains('lib/presentation/dashboard/dashboard_screen.dart', 'bank.*transfer', 'No bank transfer UI');
    _checkFileNotContains('lib/presentation/dashboard/dashboard_screen.dart', 'bank.*link', 'No bank linking UI');

    // Check mobile money features are prominent
    _checkFileContains('lib/presentation/dashboard/dashboard_screen.dart', 'mobile.*money|momo', 'Mobile money features present');

    // Check wallet-only flow is configured
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'MTN_MONEY', 'MTN wallet supported');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'AIRTEL_MONEY', 'Airtel wallet supported');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'ZAMTEL_KWACHA', 'Zamtel wallet supported');
    _checkFileContains('lib/wallet/zambia_wallets.dart', 'wallet_only_mode.*true', 'Wallet-only mode configured');

    // Check mobile money refresh is configured
    _checkFileContains('lib/refresh/momo_refresh.dart', 'enableBalanceRefresh', 'Balance refresh enabled');
    _checkFileContains('lib/refresh/momo_refresh.dart', '2G.*optim', '2G optimization active');

    // Check mobile money notifications are configured
    _checkFileContains('lib/notifications/momo_alerts.dart', 'configureMomoNotifications', 'Notifications configured');
    _checkFileContains('lib/notifications/momo_alerts.dart', 'enableDeliveryGuarantee', 'Delivery guarantee enabled');

    print('');
  }

  void _checkFile(String filePath, String description) {
    final file = File(filePath);
    if (file.existsSync()) {
      _pass(description);
    } else {
      _fail(description, 'File not found: $filePath');
    }
  }

  void _checkFileContains(String filePath, String pattern, String description) {
    final file = File(filePath);
    if (!file.existsSync()) {
      _fail(description, 'File not found: $filePath');
      return;
    }

    final content = file.readAsStringSync();
    final regex = RegExp(pattern, caseSensitive: false);
    
    if (regex.hasMatch(content)) {
      _pass(description);
    } else {
      _fail(description, 'Pattern not found in $filePath: $pattern');
    }
  }

  void _checkFileNotContains(String filePath, String pattern, String description) {
    final file = File(filePath);
    if (!file.existsSync()) {
      _pass(description); // If file doesn't exist, it can't contain the pattern
      return;
    }

    final content = file.readAsStringSync();
    final regex = RegExp(pattern, caseSensitive: false);
    
    if (!regex.hasMatch(content)) {
      _pass(description);
    } else {
      _fail(description, 'Unwanted pattern found in $filePath: $pattern');
    }
  }

  void _pass(String description) {
    print('  ✅ $description');
    _passedTests++;
  }

  void _fail(String description, String reason) {
    print('  ❌ $description');
    print('     Reason: $reason');
    _failedTests++;
    _failures.add('$description: $reason');
  }

  void _printResults() {
    print('🎯 MVP VALIDATION RESULTS');
    print('=' * 60);
    print('Total Tests: ${_passedTests + _failedTests}');
    print('Passed: $_passedTests');
    print('Failed: $_failedTests');
    print('');

    if (_failedTests == 0) {
      print('🎉 SUCCESS: Pay Mule Zambia Mobile Money MVP is ready!');
      print('✅ All banking features are disabled');
      print('✅ All mobile money features are enabled');
      print('✅ Feature lock system is working correctly');
      print('✅ Zero breakage confirmed');
      print('');
      print('🚀 READY FOR MOBILE MONEY-ONLY RELEASE 🇿🇲');
    } else {
      print('⚠️ VALIDATION FAILED: Issues found that must be fixed before release');
      print('');
      print('Failed Tests:');
      for (final failure in _failures) {
        print('  • $failure');
      }
      print('');
      print('❌ NOT READY FOR RELEASE - Fix issues above');
    }

    print('');
    print('CORE MANDATE: Mobile money-only release • No banking features • Zero breakage');
    
    // Exit with appropriate code
    exit(_failedTests == 0 ? 0 : 1);
  }
}
