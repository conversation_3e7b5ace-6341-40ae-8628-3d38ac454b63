#!/usr/bin/env python3
"""
Pay Mule Icon Validation Script
Validates that all required Pay Mule icons are properly installed
"""

import os
import json
from PIL import Image

def validate_android_icons():
    """Validate Android launcher icons"""
    print("🤖 Validating Android icons...")
    
    android_sizes = {
        'mdpi': 48,
        'hdpi': 72,
        'xhdpi': 96,
        'xxhdpi': 144,
        'xxxhdpi': 192
    }
    
    base_path = "android/app/src/main/res"
    errors = []
    
    for density, expected_size in android_sizes.items():
        icon_path = os.path.join(base_path, f"mipmap-{density}", "ic_launcher.png")
        
        if not os.path.exists(icon_path):
            errors.append(f"Missing Android {density} icon: {icon_path}")
            continue
        
        try:
            with Image.open(icon_path) as img:
                width, height = img.size
                if width != expected_size or height != expected_size:
                    errors.append(f"Android {density} icon wrong size: {width}x{height}, expected {expected_size}x{expected_size}")
                else:
                    print(f"  ✅ {density}: {icon_path} ({width}x{height}px)")
        except Exception as e:
            errors.append(f"Error reading Android {density} icon: {e}")
    
    return errors

def validate_ios_icons():
    """Validate iOS app icons"""
    print("🍎 Validating iOS icons...")
    
    ios_sizes = {
        'Icon-App-20x20@1x': 20,
        'Icon-App-20x20@2x': 40,
        'Icon-App-20x20@3x': 60,
        'Icon-App-29x29@1x': 29,
        'Icon-App-29x29@2x': 58,
        'Icon-App-29x29@3x': 87,
        'Icon-App-40x40@1x': 40,
        'Icon-App-40x40@2x': 80,
        'Icon-App-40x40@3x': 120,
        'Icon-App-60x60@2x': 120,
        'Icon-App-60x60@3x': 180,
        'Icon-App-76x76@1x': 76,
        'Icon-App-76x76@2x': 152,
        'Icon-App-83.5x83.5@2x': 167,
        'Icon-App-1024x1024@1x': 1024
    }
    
    base_path = "ios/Runner/Assets.xcassets/AppIcon.appiconset"
    errors = []
    
    for icon_name, expected_size in ios_sizes.items():
        icon_path = os.path.join(base_path, f"{icon_name}.png")
        
        if not os.path.exists(icon_path):
            errors.append(f"Missing iOS icon: {icon_path}")
            continue
        
        try:
            with Image.open(icon_path) as img:
                width, height = img.size
                if width != expected_size or height != expected_size:
                    errors.append(f"iOS {icon_name} wrong size: {width}x{height}, expected {expected_size}x{expected_size}")
                else:
                    print(f"  ✅ {icon_name}: {icon_path} ({width}x{height}px)")
        except Exception as e:
            errors.append(f"Error reading iOS {icon_name} icon: {e}")
    
    return errors

def validate_ios_contents_json():
    """Validate iOS Contents.json file"""
    print("📄 Validating iOS Contents.json...")
    
    contents_path = "ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json"
    
    if not os.path.exists(contents_path):
        return [f"Missing iOS Contents.json: {contents_path}"]
    
    try:
        with open(contents_path, 'r') as f:
            contents = json.load(f)
        
        if 'images' not in contents:
            return ["iOS Contents.json missing 'images' array"]
        
        if len(contents['images']) < 15:
            return [f"iOS Contents.json has only {len(contents['images'])} images, expected at least 15"]
        
        print(f"  ✅ Contents.json: {len(contents['images'])} image entries")
        return []
        
    except Exception as e:
        return [f"Error reading iOS Contents.json: {e}"]

def check_icon_content():
    """Check if icons contain the expected Pay Mule design elements"""
    print("🎨 Checking icon content...")
    
    # Check a sample icon to see if it has the expected design
    sample_icon = "android/app/src/main/res/mipmap-hdpi/ic_launcher.png"
    
    if not os.path.exists(sample_icon):
        return ["Cannot check icon content - sample icon missing"]
    
    try:
        with Image.open(sample_icon) as img:
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Check if the image has some expected colors
            pixels = list(img.getdata())
            
            # Look for black pixels (wallet body)
            black_pixels = sum(1 for r, g, b in pixels if r < 50 and g < 50 and b < 50)
            
            # Look for green pixels (Zambian flag)
            green_pixels = sum(1 for r, g, b in pixels if g > 100 and r < 100 and b < 100)
            
            if black_pixels > 100:  # Should have significant black content
                print(f"  ✅ Icon contains wallet design (black pixels: {black_pixels})")
            else:
                return ["Icon may not contain expected wallet design"]
            
            if green_pixels > 10:  # Should have some green from flag
                print(f"  ✅ Icon contains Zambian colors (green pixels: {green_pixels})")
            else:
                print(f"  ⚠️  Limited green content detected (green pixels: {green_pixels})")
        
        return []
        
    except Exception as e:
        return [f"Error checking icon content: {e}"]

def main():
    """Main validation function"""
    print("🇿🇲 Pay Mule Icon Validation")
    print("=" * 40)
    
    all_errors = []
    
    # Validate Android icons
    android_errors = validate_android_icons()
    all_errors.extend(android_errors)
    
    # Validate iOS icons
    ios_errors = validate_ios_icons()
    all_errors.extend(ios_errors)
    
    # Validate iOS Contents.json
    contents_errors = validate_ios_contents_json()
    all_errors.extend(contents_errors)
    
    # Check icon content
    content_errors = check_icon_content()
    all_errors.extend(content_errors)
    
    print("\n" + "=" * 40)
    
    if all_errors:
        print("❌ Validation failed with the following errors:")
        for error in all_errors:
            print(f"  • {error}")
        return False
    else:
        print("🎉 All Pay Mule icons validated successfully!")
        print("\nIcon installation summary:")
        print("  • 5 Android launcher icons (mdpi to xxxhdpi)")
        print("  • 15 iOS app icons (20x20 to 1024x1024)")
        print("  • iOS Contents.json properly configured")
        print("  • Icons contain expected Pay Mule wallet design")
        print("\nNext steps:")
        print("  1. Test on device: flutter run")
        print("  2. Build release: flutter build apk --release")
        print("  3. Check app icon appears correctly on device")
        return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
