#!/bin/bash

# 🇿🇲 PAY MULE FEATURE LOCK VERIFICATION SCRIPT
# Verifies banking features are disabled and mobile money features are enabled
# SAFETY PROTOCOL: Zero breakage guarantee

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
DISABLED_FEATURES=""
ENABLED_FEATURES=""
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --disabled-features=*)
            DISABLED_FEATURES="${1#*=}"
            shift
            ;;
        --enabled-features=*)
            ENABLED_FEATURES="${1#*=}"
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 --disabled-features=<features> --enabled-features=<features> [--verbose]"
            echo "Example: $0 --disabled-features=\"bank_linking,bank_transfers\" --enabled-features=\"mobile_money,chilimba\""
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

echo -e "${CYAN}🇿🇲 PAY MULE FEATURE LOCK VERIFICATION${NC}"
echo "=================================================================="
echo -e "${YELLOW}SAFETY PROTOCOL: Verifying banking removal & mobile money features${NC}"
echo ""

# Phase 1: Validate input parameters
echo -e "${BLUE}📋 PHASE 1: Input Validation${NC}"
echo "--------------------------------------------------"

if [ -z "$DISABLED_FEATURES" ]; then
    echo -e "${RED}❌ No disabled features specified${NC}"
    exit 1
fi

if [ -z "$ENABLED_FEATURES" ]; then
    echo -e "${RED}❌ No enabled features specified${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Disabled features: $DISABLED_FEATURES${NC}"
echo -e "${GREEN}✅ Enabled features: $ENABLED_FEATURES${NC}"
echo ""

# Phase 2: Feature lock system verification
echo -e "${BLUE}🔒 PHASE 2: Feature Lock System Verification${NC}"
echo "--------------------------------------------------"

# Check if feature lock file exists
if [ ! -f "lib/features/feature_lock.dart" ]; then
    echo -e "${RED}❌ Feature lock system not found${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Feature lock system found${NC}"

# Verify feature lock initialization
if ! grep -q "Features.initialize" lib/main.dart; then
    echo -e "${RED}❌ Feature lock not initialized in main.dart${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Feature lock initialization verified${NC}"

# Phase 3: Banking feature verification
echo -e "${BLUE}🚫 PHASE 3: Banking Feature Verification${NC}"
echo "--------------------------------------------------"

IFS=',' read -ra DISABLED_ARRAY <<< "$DISABLED_FEATURES"
for feature in "${DISABLED_ARRAY[@]}"; do
    feature=$(echo "$feature" | xargs) # Trim whitespace
    
    case $feature in
        "bank_linking")
            if grep -q "bankLinking.*true" lib/core/config/app_config.dart; then
                echo -e "${RED}❌ Bank linking is enabled in config${NC}"
                exit 1
            fi
            echo -e "${GREEN}✅ Bank linking disabled${NC}"
            ;;
        "bank_transfers")
            if grep -q "bankTransfers.*true" lib/core/config/app_config.dart; then
                echo -e "${RED}❌ Bank transfers enabled in config${NC}"
                exit 1
            fi
            echo -e "${GREEN}✅ Bank transfers disabled${NC}"
            ;;
        "bank_account_management")
            if grep -q "bankAccountManagement.*true" lib/core/config/app_config.dart; then
                echo -e "${RED}❌ Bank account management enabled in config${NC}"
                exit 1
            fi
            echo -e "${GREEN}✅ Bank account management disabled${NC}"
            ;;
        *)
            echo -e "${YELLOW}⚠️ Unknown disabled feature: $feature${NC}"
            ;;
    esac
done

# Phase 4: Mobile money feature verification
echo -e "${BLUE}📱 PHASE 4: Mobile Money Feature Verification${NC}"
echo "--------------------------------------------------"

IFS=',' read -ra ENABLED_ARRAY <<< "$ENABLED_FEATURES"
for feature in "${ENABLED_ARRAY[@]}"; do
    feature=$(echo "$feature" | xargs) # Trim whitespace
    
    case $feature in
        "mobile_money")
            if ! grep -q "mobileMoneyTransfers.*true" lib/core/config/app_config.dart; then
                echo -e "${RED}❌ Mobile money transfers not enabled in config${NC}"
                exit 1
            fi
            echo -e "${GREEN}✅ Mobile money transfers enabled${NC}"
            ;;
        "chilimba")
            if ! grep -q "chilimbaSupport.*true" lib/core/config/app_config.dart; then
                echo -e "${RED}❌ Chilimba support not enabled in config${NC}"
                exit 1
            fi
            echo -e "${GREEN}✅ Chilimba support enabled${NC}"
            ;;
        "utility_payments")
            if ! grep -q "utilityPayments.*true" lib/core/config/app_config.dart; then
                echo -e "${RED}❌ Utility payments not enabled in config${NC}"
                exit 1
            fi
            echo -e "${GREEN}✅ Utility payments enabled${NC}"
            ;;
        "agent_locator")
            if ! grep -q "agentLocator.*true" lib/core/config/app_config.dart; then
                echo -e "${RED}❌ Agent locator not enabled in config${NC}"
                exit 1
            fi
            echo -e "${GREEN}✅ Agent locator enabled${NC}"
            ;;
        *)
            echo -e "${YELLOW}⚠️ Unknown enabled feature: $feature${NC}"
            ;;
    esac
done

# Phase 5: Code integrity verification
echo -e "${BLUE}🔍 PHASE 5: Code Integrity Verification${NC}"
echo "--------------------------------------------------"

# Check for banking UI components
if grep -r "bank.*tab\|bank.*screen\|bank.*widget" lib/presentation/ 2>/dev/null | grep -v "test" | grep -v "comment"; then
    echo -e "${RED}❌ Banking UI components found in presentation layer${NC}"
    exit 1
fi

echo -e "${GREEN}✅ No banking UI components found${NC}"

# Check for banking services
if grep -r "bank.*service\|bank.*api" lib/features/ 2>/dev/null | grep -v "test" | grep -v "comment"; then
    echo -e "${RED}❌ Banking services found in features layer${NC}"
    exit 1
fi

echo -e "${GREEN}✅ No banking services found${NC}"

# Verify mobile money components exist
if [ ! -f "lib/wallet/zambia_wallets.dart" ]; then
    echo -e "${RED}❌ Zambia wallets configuration missing${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Zambia wallets configuration found${NC}"

if [ ! -f "lib/refresh/momo_refresh.dart" ]; then
    echo -e "${RED}❌ Mobile money refresh system missing${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Mobile money refresh system found${NC}"

if [ ! -f "lib/notifications/momo_alerts.dart" ]; then
    echo -e "${RED}❌ Mobile money notifications missing${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Mobile money notifications found${NC}"

# Phase 6: Final verification
echo -e "${BLUE}✅ PHASE 6: Final Verification${NC}"
echo "--------------------------------------------------"

# Run feature lock validation
echo "🔄 Running feature lock validation..."
if dart validate_mvp_mobile_money.dart > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Feature lock validation passed${NC}"
else
    echo -e "${RED}❌ Feature lock validation failed${NC}"
    exit 1
fi

# Summary
echo ""
echo -e "${GREEN}🎉 FEATURE LOCK VERIFICATION COMPLETE${NC}"
echo "=================================================================="
echo -e "${CYAN}✅ Banking features disabled: $DISABLED_FEATURES${NC}"
echo -e "${CYAN}✅ Mobile money features enabled: $ENABLED_FEATURES${NC}"
echo -e "${CYAN}✅ Code integrity verified${NC}"
echo -e "${CYAN}✅ Zero breakage confirmed${NC}"
echo ""
echo -e "${PURPLE}🚀 READY FOR APK BUILD PROCESS${NC}"

exit 0
