#!/bin/bash

# verify_real_device.sh - Comprehensive real device verification script
# Tests APK installation and functionality on connected Android devices

set -e

# Default values
APK_FILE=""
PACKAGE_NAME=""
AUTO_DETECT_PACKAGE="true"
VERIFICATION_RESULTS_DIR="device_verification_$(date +%Y%m%d_%H%M%S)"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${MAGENTA}[TEST]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "This script performs comprehensive verification on real Android devices:"
    echo "1. Device detection and compatibility check"
    echo "2. APK installation with proper flags"
    echo "3. App launch and functionality testing"
    echo "4. Performance and compatibility verification"
    echo ""
    echo "Options:"
    echo "  --apk=FILE              APK file to test (required)"
    echo "  --package=NAME          Package name (auto-detected if not provided)"
    echo "  --help                  Show this help message"
    echo ""
    echo "ADB Installation flags used:"
    echo "  -r    Replace existing application"
    echo "  -g    Grant all runtime permissions"
    echo "  -t    Allow test packages"
    echo ""
    echo "Example:"
    echo "  $0 --apk=paymule_compatible_v1.1.apk"
    echo ""
    echo "Prerequisites:"
    echo "  • Android device connected via USB"
    echo "  • USB debugging enabled"
    echo "  • ADB installed and in PATH"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --apk=*)
            APK_FILE="${1#*=}"
            shift
            ;;
        --package=*)
            PACKAGE_NAME="${1#*=}"
            AUTO_DETECT_PACKAGE="false"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$APK_FILE" ]]; then
    print_error "APK file is required. Use --apk=FILE"
    show_usage
    exit 1
fi

if [[ ! -f "$APK_FILE" ]]; then
    print_error "APK file not found: $APK_FILE"
    exit 1
fi

# Check if ADB is available
if ! command -v adb &> /dev/null; then
    print_error "ADB not found. Please install Android SDK platform-tools."
    exit 1
fi

# Create verification results directory
mkdir -p "$VERIFICATION_RESULTS_DIR"

print_header "📱 REAL DEVICE VERIFICATION"
print_status "APK File: $APK_FILE"
print_status "Results Directory: $VERIFICATION_RESULTS_DIR"
echo ""

# Function to detect package name from APK
detect_package_name() {
    if [[ "$AUTO_DETECT_PACKAGE" == "true" ]]; then
        if command -v aapt &> /dev/null; then
            PACKAGE_NAME=$(aapt dump badging "$APK_FILE" 2>/dev/null | grep "package:" | sed "s/.*name='\([^']*\)'.*/\1/" || echo "")
            if [[ -n "$PACKAGE_NAME" ]]; then
                print_success "Auto-detected package name: $PACKAGE_NAME"
            else
                print_warning "Could not auto-detect package name"
                PACKAGE_NAME="com.zm.paymule"  # fallback
            fi
        else
            print_warning "aapt not found. Using fallback package name."
            PACKAGE_NAME="com.zm.paymule"
        fi
    fi
}

# Function to check device connectivity
check_device_connectivity() {
    print_test "Checking device connectivity..."
    
    local devices=$(adb devices | grep -v "List of devices" | grep -v "^$" | wc -l)
    
    if [[ $devices -eq 0 ]]; then
        print_error "No devices connected. Please connect an Android device with USB debugging enabled."
        return 1
    elif [[ $devices -gt 1 ]]; then
        print_warning "Multiple devices connected. Using the first available device."
    fi
    
    # Get device info
    local device_model=$(adb shell getprop ro.product.model 2>/dev/null | tr -d '\r' || echo "Unknown")
    local android_version=$(adb shell getprop ro.build.version.release 2>/dev/null | tr -d '\r' || echo "Unknown")
    local api_level=$(adb shell getprop ro.build.version.sdk 2>/dev/null | tr -d '\r' || echo "Unknown")
    local manufacturer=$(adb shell getprop ro.product.manufacturer 2>/dev/null | tr -d '\r' || echo "Unknown")
    
    print_success "Device connected:"
    print_status "  • Model: $manufacturer $device_model"
    print_status "  • Android: $android_version (API $api_level)"
    
    # Save device info
    echo "=== Device Information ===" > "$VERIFICATION_RESULTS_DIR/device_info.txt"
    echo "Manufacturer: $manufacturer" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
    echo "Model: $device_model" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
    echo "Android Version: $android_version" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
    echo "API Level: $api_level" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
    echo "Test Date: $(date)" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
    
    return 0
}

# Function to check device compatibility
check_device_compatibility() {
    print_test "Checking device compatibility..."
    
    local api_level=$(adb shell getprop ro.build.version.sdk 2>/dev/null | tr -d '\r' || echo "0")
    local min_sdk="21"  # Android 5.0
    
    if command -v aapt &> /dev/null; then
        min_sdk=$(aapt dump badging "$APK_FILE" 2>/dev/null | grep "sdkVersion" | sed "s/.*sdkVersion:'\([0-9]*\)'.*/\1/" || echo "21")
    fi
    
    echo "=== Compatibility Check ===" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
    echo "Device API Level: $api_level" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
    echo "Required Min SDK: $min_sdk" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
    
    if [[ $api_level -ge $min_sdk ]]; then
        print_success "✅ Device is compatible (API $api_level >= $min_sdk)"
        echo "Compatibility: COMPATIBLE" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
        return 0
    else
        print_error "❌ Device is not compatible (API $api_level < $min_sdk)"
        echo "Compatibility: NOT COMPATIBLE" >> "$VERIFICATION_RESULTS_DIR/device_info.txt"
        return 1
    fi
}

# Function to perform APK installation
install_apk() {
    print_test "Installing APK with enhanced flags..."
    
    local install_log="$VERIFICATION_RESULTS_DIR/installation.log"
    
    echo "=== APK Installation ===" > "$install_log"
    echo "APK File: $APK_FILE" >> "$install_log"
    echo "Package Name: $PACKAGE_NAME" >> "$install_log"
    echo "Installation Command: adb install -r -g -t $APK_FILE" >> "$install_log"
    echo "Installation Start: $(date)" >> "$install_log"
    echo "" >> "$install_log"
    
    # Uninstall existing version first (if exists)
    print_status "Removing existing installation (if any)..."
    adb uninstall "$PACKAGE_NAME" >> "$install_log" 2>&1 || true
    
    # Install with flags: -r (replace), -g (grant permissions), -t (test packages)
    print_status "Installing APK..."
    if adb install -r -g -t "$APK_FILE" >> "$install_log" 2>&1; then
        print_success "✅ APK installation successful"
        echo "RESULT: INSTALLATION SUCCESS" >> "$install_log"
        return 0
    else
        print_error "❌ APK installation failed"
        echo "RESULT: INSTALLATION FAILED" >> "$install_log"
        
        # Show installation error
        print_status "Installation error details:"
        tail -5 "$install_log" | while read line; do
            print_error "  $line"
        done
        
        return 1
    fi
}

# Function to test app launch
test_app_launch() {
    print_test "Testing app launch..."
    
    local launch_log="$VERIFICATION_RESULTS_DIR/launch_test.log"
    
    echo "=== App Launch Test ===" > "$launch_log"
    echo "Package Name: $PACKAGE_NAME" >> "$launch_log"
    echo "Launch Start: $(date)" >> "$launch_log"
    echo "" >> "$launch_log"
    
    # Get main activity
    local main_activity=$(adb shell cmd package resolve-activity --brief "$PACKAGE_NAME" | tail -n 1 2>/dev/null || echo "")
    
    if [[ -n "$main_activity" ]]; then
        print_status "Main activity: $main_activity"
        echo "Main Activity: $main_activity" >> "$launch_log"
        
        # Launch app
        if adb shell am start -n "$main_activity" >> "$launch_log" 2>&1; then
            print_success "✅ App launched successfully"
            echo "RESULT: LAUNCH SUCCESS" >> "$launch_log"
            
            # Wait a moment for app to load
            sleep 3
            
            # Check if app is running
            if adb shell pidof "$PACKAGE_NAME" > /dev/null 2>&1; then
                print_success "✅ App is running"
                echo "App Status: RUNNING" >> "$launch_log"
            else
                print_warning "⚠️  App may have crashed after launch"
                echo "App Status: NOT RUNNING" >> "$launch_log"
            fi
            
            return 0
        else
            print_error "❌ App launch failed"
            echo "RESULT: LAUNCH FAILED" >> "$launch_log"
            return 1
        fi
    else
        print_warning "⚠️  Could not determine main activity"
        echo "RESULT: MAIN ACTIVITY NOT FOUND" >> "$launch_log"
        return 1
    fi
}

# Function to collect device performance info
collect_performance_info() {
    print_test "Collecting device performance information..."
    
    local perf_log="$VERIFICATION_RESULTS_DIR/performance.log"
    
    echo "=== Device Performance Information ===" > "$perf_log"
    echo "Collection Time: $(date)" >> "$perf_log"
    echo "" >> "$perf_log"
    
    # Memory information
    echo "=== Memory Information ===" >> "$perf_log"
    adb shell cat /proc/meminfo | head -10 >> "$perf_log" 2>/dev/null || echo "Memory info not available" >> "$perf_log"
    echo "" >> "$perf_log"
    
    # CPU information
    echo "=== CPU Information ===" >> "$perf_log"
    adb shell cat /proc/cpuinfo | grep -E "(processor|model name|cpu MHz)" >> "$perf_log" 2>/dev/null || echo "CPU info not available" >> "$perf_log"
    echo "" >> "$perf_log"
    
    # Storage information
    echo "=== Storage Information ===" >> "$perf_log"
    adb shell df /data >> "$perf_log" 2>/dev/null || echo "Storage info not available" >> "$perf_log"
    echo "" >> "$perf_log"
    
    # App memory usage (if app is installed)
    if adb shell pm list packages | grep -q "$PACKAGE_NAME"; then
        echo "=== App Memory Usage ===" >> "$perf_log"
        adb shell dumpsys meminfo "$PACKAGE_NAME" >> "$perf_log" 2>/dev/null || echo "App memory info not available" >> "$perf_log"
    fi
    
    print_success "Performance information collected"
}

# Function to run comprehensive tests
run_comprehensive_tests() {
    print_test "Running comprehensive functionality tests..."
    
    local test_log="$VERIFICATION_RESULTS_DIR/comprehensive_tests.log"
    
    echo "=== Comprehensive Tests ===" > "$test_log"
    echo "Test Start: $(date)" >> "$test_log"
    echo "" >> "$test_log"
    
    # Test 1: Permission verification
    echo "=== Permission Test ===" >> "$test_log"
    adb shell dumpsys package "$PACKAGE_NAME" | grep -A 20 "requested permissions:" >> "$test_log" 2>/dev/null || echo "Permission info not available" >> "$test_log"
    echo "" >> "$test_log"
    
    # Test 2: App info
    echo "=== App Information ===" >> "$test_log"
    adb shell dumpsys package "$PACKAGE_NAME" | grep -E "(versionCode|versionName|targetSdk|minSdk)" >> "$test_log" 2>/dev/null || echo "App info not available" >> "$test_log"
    echo "" >> "$test_log"
    
    # Test 3: Signature verification
    echo "=== Signature Verification ===" >> "$test_log"
    adb shell dumpsys package "$PACKAGE_NAME" | grep -A 5 "signatures:" >> "$test_log" 2>/dev/null || echo "Signature info not available" >> "$test_log"
    
    print_success "Comprehensive tests completed"
}

# Function to generate final report
generate_final_report() {
    print_test "Generating final verification report..."
    
    local report_file="$VERIFICATION_RESULTS_DIR/verification_report.txt"
    
    echo "=== REAL DEVICE VERIFICATION REPORT ===" > "$report_file"
    echo "APK File: $APK_FILE" >> "$report_file"
    echo "Package Name: $PACKAGE_NAME" >> "$report_file"
    echo "Verification Date: $(date)" >> "$report_file"
    echo "" >> "$report_file"
    
    # Device information
    if [[ -f "$VERIFICATION_RESULTS_DIR/device_info.txt" ]]; then
        cat "$VERIFICATION_RESULTS_DIR/device_info.txt" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    # Test results summary
    echo "=== Test Results Summary ===" >> "$report_file"
    
    # Installation result
    if grep -q "INSTALLATION SUCCESS" "$VERIFICATION_RESULTS_DIR/installation.log" 2>/dev/null; then
        echo "✅ Installation: PASSED" >> "$report_file"
    else
        echo "❌ Installation: FAILED" >> "$report_file"
    fi
    
    # Launch result
    if grep -q "LAUNCH SUCCESS" "$VERIFICATION_RESULTS_DIR/launch_test.log" 2>/dev/null; then
        echo "✅ App Launch: PASSED" >> "$report_file"
    else
        echo "❌ App Launch: FAILED" >> "$report_file"
    fi
    
    # App running status
    if grep -q "App Status: RUNNING" "$VERIFICATION_RESULTS_DIR/launch_test.log" 2>/dev/null; then
        echo "✅ App Stability: PASSED" >> "$report_file"
    else
        echo "⚠️  App Stability: NEEDS REVIEW" >> "$report_file"
    fi
    
    echo "" >> "$report_file"
    echo "=== Detailed Logs ===" >> "$report_file"
    echo "All detailed logs are available in: $VERIFICATION_RESULTS_DIR" >> "$report_file"
    
    print_success "Final report generated: $report_file"
}

# Main execution flow
print_header "🚀 STARTING REAL DEVICE VERIFICATION"

# Step 1: Detect package name
detect_package_name

# Step 2: Check device connectivity
if ! check_device_connectivity; then
    exit 1
fi

# Step 3: Check device compatibility
if ! check_device_compatibility; then
    print_warning "Device may not be compatible, but continuing with installation test..."
fi

# Step 4: Install APK
if ! install_apk; then
    print_error "Installation failed. Stopping verification."
    generate_final_report
    exit 1
fi

# Step 5: Test app launch
test_app_launch

# Step 6: Collect performance information
collect_performance_info

# Step 7: Run comprehensive tests
run_comprehensive_tests

# Step 8: Generate final report
generate_final_report

# Display summary
print_header "📊 VERIFICATION SUMMARY"
cat "$VERIFICATION_RESULTS_DIR/verification_report.txt"

print_success "🎉 Real device verification completed!"
print_status "📁 All results saved in: $VERIFICATION_RESULTS_DIR"

# Final ADB command demonstration
print_header "🔧 MANUAL INSTALLATION COMMAND"
print_status "For manual installation, use this exact command:"
echo ""
echo -e "${GREEN}adb install -r -g -t $APK_FILE${NC}"
echo ""
print_status "Command explanation:"
print_status "  -r  Replace existing application"
print_status "  -g  Grant all runtime permissions automatically"
print_status "  -t  Allow installation of test packages"
