#!/bin/bash

# Your Exact Implementation
# if payment_success_rate < 99.9%; then
#   revert_to_commit PAYMULE_STABLE_v3
#   send_sms_alert "Deployment failed - Reverted"
# fi

# Source the rollback functions
source ./rollback_functions.sh

# Your exact implementation as a function
payment_monitoring_check() {
    local payment_success_rate
    payment_success_rate=$(get_payment_success_rate)
    
    echo "🔍 Payment Success Rate Check"
    echo "Current rate: ${payment_success_rate}%"
    echo "Threshold: 99.9%"
    echo
    
    # Convert to integer for comparison (multiply by 100)
    local current_rate_int=$(echo "$payment_success_rate" | awk '{printf "%.0f", $1 * 100}')
    local threshold_int=9990  # 99.9 * 100
    
    if [ "$current_rate_int" -lt "$threshold_int" ]; then
        echo "🚨 CONDITION: payment_success_rate < 99.9% (${payment_success_rate}% < 99.9%)"
        echo "Executing your exact implementation:"
        echo
        
        echo "→ revert_to_commit PAYMULE_STABLE_v3"
        revert_to_commit PAYMULE_STABLE_v3
        
        echo "→ send_sms_alert \"Deployment failed - Reverted\""
        send_sms_alert "Deployment failed - Reverted"
        
        echo
        echo "🔄 Rollback sequence completed"
        return 1
    else
        echo "✅ CONDITION: payment_success_rate >= 99.9% (${payment_success_rate}% >= 99.9%)"
        echo "🚀 No rollback needed - system healthy"
        return 0
    fi
}

# Run the check
echo "🎯 Your Exact Rollback Implementation"
echo "====================================="
echo
echo "Implementation:"
echo "if payment_success_rate < 99.9%; then"
echo "  revert_to_commit PAYMULE_STABLE_v3"
echo "  send_sms_alert \"Deployment failed - Reverted\""
echo "fi"
echo
echo "Executing check..."
echo

payment_monitoring_check

echo
echo "✅ Implementation test completed"
