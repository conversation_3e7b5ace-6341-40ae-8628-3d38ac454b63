#!/bin/bash

# zambia_compatibility_fix.sh - Master script for Zambian device compatibility
# Combines manifest updates, V1 signing, and APK rebuilding

set -e

# Default values
APK_FILE="paymule_mobile_money_v1.1.apk"
FINAL_APK="paymule_zambia_compatible_v1.1.apk"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "This script performs a complete compatibility fix for Zambian devices:"
    echo "1. Updates AndroidManifest.xml with proper package name and SDK versions"
    echo "2. Adds V1 signing for older device compatibility"
    echo "3. Rebuilds APK with Zambian device optimizations"
    echo ""
    echo "Options:"
    echo "  --apk=FILE              Input APK file (default: paymule_mobile_money_v1.1.apk)"
    echo "  --output=FILE           Final output APK (default: paymule_zambia_compatible_v1.1.apk)"
    echo "  --help                  Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --apk=paymule_mobile_money_v1.1.apk"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --apk=*)
            APK_FILE="${1#*=}"
            shift
            ;;
        --output=*)
            FINAL_APK="${1#*=}"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if APK file exists
if [[ ! -f "$APK_FILE" ]]; then
    print_error "APK file not found: $APK_FILE"
    print_status "Available APK files:"
    ls -la *.apk 2>/dev/null || print_status "No APK files found in current directory"
    exit 1
fi

print_header "🇿🇲 ZAMBIAN DEVICE COMPATIBILITY FIX"
print_status "Starting comprehensive compatibility fix for Zambian devices..."
print_status "Input APK: $APK_FILE"
print_status "Final APK: $FINAL_APK"
echo ""

# Step 1: Update AndroidManifest.xml
print_header "📝 STEP 1: UPDATE MANIFEST"
print_status "Updating AndroidManifest.xml for Zambian compatibility..."

if [[ -f "update_manifest.sh" ]]; then
    chmod +x update_manifest.sh
    ./update_manifest.sh \
        --package="com.zm.paymule" \
        --min-sdk=21 \
        --target-sdk=33
    print_success "AndroidManifest.xml updated successfully"
else
    print_warning "update_manifest.sh not found. Manifest may need manual update."
fi

echo ""

# Step 2: Add V1 signing
print_header "🔐 STEP 2: ADD V1 SIGNING"
print_status "Adding V1 signature scheme for older device compatibility..."

if [[ -f "fix_signing.sh" ]]; then
    chmod +x fix_signing.sh
    ./fix_signing.sh \
        --apk="$APK_FILE" \
        --v1-signing=enabled \
        --keystore=zm_prod_key.jks
    
    # Use the signed APK for next step
    SIGNED_APK="${APK_FILE%.apk}_v1_signed.apk"
    if [[ -f "$SIGNED_APK" ]]; then
        APK_FILE="$SIGNED_APK"
        print_success "V1 signing completed: $SIGNED_APK"
    else
        print_warning "V1 signed APK not found. Continuing with original APK."
    fi
else
    print_warning "fix_signing.sh not found. V1 signing skipped."
fi

echo ""

# Step 3: Rebuild with compatibility optimizations
print_header "🔧 STEP 3: REBUILD WITH OPTIMIZATIONS"
print_status "Rebuilding APK with Zambian device optimizations..."

if [[ -f "rebuild_compatible_apk.sh" ]]; then
    chmod +x rebuild_compatible_apk.sh
    ./rebuild_compatible_apk.sh \
        --input="$APK_FILE" \
        --output="$FINAL_APK" \
        --optimize-for="low_memory_devices" \
        --zambia-device-profiles
    print_success "APK rebuilt with optimizations: $FINAL_APK"
else
    print_warning "rebuild_compatible_apk.sh not found. Copying APK as final version."
    cp "$APK_FILE" "$FINAL_APK"
fi

echo ""

# Final verification
print_header "✅ FINAL VERIFICATION"
print_status "Verifying final APK compatibility..."

if [[ -f "$FINAL_APK" ]]; then
    # Check file size
    FINAL_SIZE=$(du -h "$FINAL_APK" | cut -f1)
    print_status "Final APK size: $FINAL_SIZE"
    
    # Check if we can analyze the APK
    if command -v aapt &> /dev/null; then
        print_status "Analyzing APK structure..."
        
        # Check minimum SDK
        MIN_SDK=$(aapt dump badging "$FINAL_APK" 2>/dev/null | grep "sdkVersion" | sed "s/.*sdkVersion:'\([0-9]*\)'.*/\1/" || echo "unknown")
        print_status "Minimum SDK: $MIN_SDK"
        
        # Check package name
        PACKAGE=$(aapt dump badging "$FINAL_APK" 2>/dev/null | grep "package:" | sed "s/.*name='\([^']*\)'.*/\1/" || echo "unknown")
        print_status "Package name: $PACKAGE"
        
        # Verify APK structure
        if aapt dump badging "$FINAL_APK" > /dev/null 2>&1; then
            print_success "APK structure is valid"
        else
            print_warning "APK structure verification failed"
        fi
    fi
    
    print_success "Final APK created: $FINAL_APK"
else
    print_error "Final APK not found: $FINAL_APK"
    exit 1
fi

echo ""
print_header "🎉 COMPATIBILITY FIX COMPLETE"
print_success "Zambian device compatibility fix completed successfully!"
echo ""
print_status "📱 Your APK is now optimized for:"
print_status "   ✅ Android 5.0+ devices (API 21+)"
print_status "   ✅ Entry-level smartphones common in Zambia"
print_status "   ✅ Devices with limited RAM (1-2GB)"
print_status "   ✅ Older devices requiring V1 signature scheme"
print_status "   ✅ Various screen sizes and densities"
print_status "   ✅ Zambian locale and device profiles"
echo ""
print_status "📁 Files created:"
print_status "   • Final APK: $FINAL_APK"
if [[ -f "${APK_FILE%.apk}_backup.apk" ]]; then
    print_status "   • Backup APK: ${APK_FILE%.apk}_backup.apk"
fi
if [[ -f "${APK_FILE%.apk}_v1_signed.apk" ]]; then
    print_status "   • V1 Signed APK: ${APK_FILE%.apk}_v1_signed.apk"
fi
echo ""
print_status "🚀 Ready for testing and deployment in Zambia!"
print_status "🇿🇲 Recommended testing on various Zambian device models"
