<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zambia Pay Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #2E7D32; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; }
        .healthy { color: #4CAF50; }
        .warning { color: #FF9800; }
        .critical { color: #F44336; }
        .status { text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; }
    </style>
    <script>
        function refreshDashboard() {
            location.reload();
        }
        setInterval(refreshDashboard, 5000);
    </script>
</head>
<body>
    <div class="header">
        <h1>🇿🇲 Zambia Pay Live Dashboard</h1>
        <p>Last Updated: Fri, Aug  1, 2025  7:31:26 PM</p>
    </div>

    <div class="metrics">
        <div class="metric-card">
            <h3>tx_success</h3>
            <div class="metric-value critical">99.5</div>
            <p>Threshold: 99.9%</p>
        </div>
        <div class="metric-card">
            <h3>notification_delay</h3>
            <div class="metric-value healthy">2.5</div>
            <p>Threshold: <5s</p>
        </div>
        <div class="metric-card">
            <h3>agent_accuracy</h3>
            <div class="metric-value critical">96.5</div>
            <p>Threshold: >95%</p>
        </div>
    </div>

    <div class="footer">
        <p>Zambia Pay Dashboard - Auto-refresh every 5 seconds</p>
    </div>
</body>
</html>
