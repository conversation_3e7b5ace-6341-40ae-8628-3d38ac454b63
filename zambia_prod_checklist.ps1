#!/usr/bin/env pwsh

# Zambia Production Deployment Checklist
# Validates production readiness with comprehensive test cases
# Usage: .\zambia_prod_checklist.ps1 --test-cases="no_dummy_data,real_momo_endpoints,ussd_functional" --payment-test-amount=0.10 --require-biometric

param(
    [string]$TestCases = "no_dummy_data,real_momo_endpoints,ussd_functional",
    [decimal]$PaymentTestAmount = 0.10,
    [switch]$RequireBiometric = $false
)

$ErrorActionPreference = "Stop"

# Parse test cases
$testCaseList = $TestCases -split ","

Write-Host "PAY MULE ZAMBIA - PRODUCTION DEPLOYMENT CHECKLIST" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Cyan
Write-Host "Test Cases: $TestCases" -ForegroundColor White
Write-Host "Payment Test Amount: K$PaymentTestAmount" -ForegroundColor White
Write-Host "Require Biometric: $RequireBiometric" -ForegroundColor White
Write-Host ""

$allTestsPassed = $true
$testResults = @{}

function Test-NoDummyData {
    Write-Host "🧹 TEST CASE: NO_DUMMY_DATA" -ForegroundColor Yellow
    Write-Host "Validating removal of all dummy/test data..." -ForegroundColor Gray
    
    $passed = $true
    
    # Check production lock implementation
    if (Test-Path "lib/core/production_lock.dart") {
        $content = Get-Content "lib/core/production_lock.dart" -Raw
        
        if ($content -match "removeDummyElements") {
            Write-Host "✅ Dummy element removal implemented" -ForegroundColor Green
        } else {
            Write-Host "❌ Dummy element removal missing" -ForegroundColor Red
            $passed = $false
        }
        
        if ($content -match "purgeMockUsers") {
            Write-Host "✅ Mock user purging implemented" -ForegroundColor Green
        } else {
            Write-Host "❌ Mock user purging missing" -ForegroundColor Red
            $passed = $false
        }
        
        if ($content -match "removeTestUIElements") {
            Write-Host "✅ Test UI element removal implemented" -ForegroundColor Green
        } else {
            Write-Host "❌ Test UI element removal missing" -ForegroundColor Red
            $passed = $false
        }
    } else {
        Write-Host "❌ Production lock system missing" -ForegroundColor Red
        $passed = $false
    }
    
    # Check for dummy data in configuration files
    $configFiles = @(
        "lib/core/config/app_config.dart",
        "lib/core/config/production_config.dart"
    )
    
    foreach ($file in $configFiles) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw
            if ($content -match "dummy|test|mock|demo" -and $content -notmatch "remove.*dummy|purge.*mock") {
                Write-Host "⚠️ Potential dummy data found in $file" -ForegroundColor Yellow
            }
        }
    }
    
    Write-Host "📊 NO_DUMMY_DATA Test Result: $(if ($passed) { 'PASSED' } else { 'FAILED' })" -ForegroundColor $(if ($passed) { 'Green' } else { 'Red' })
    return $passed
}

function Test-RealMomoEndpoints {
    Write-Host "`n💳 TEST CASE: REAL_MOMO_ENDPOINTS" -ForegroundColor Yellow
    Write-Host "Validating real mobile money endpoint configuration..." -ForegroundColor Gray
    
    $passed = $true
    
    # Check production config
    if (Test-Path "lib/core/config/production_config.dart") {
        $content = Get-Content "lib/core/config/production_config.dart" -Raw
        
        # MTN Production Endpoints
        if ($content -match "momodeveloper\.mtn\.com") {
            Write-Host "✅ MTN production endpoint configured" -ForegroundColor Green
        } else {
            Write-Host "❌ MTN production endpoint missing" -ForegroundColor Red
            $passed = $false
        }
        
        # Airtel Production Endpoints
        if ($content -match "openapi\.airtel\.africa") {
            Write-Host "✅ Airtel production endpoint configured" -ForegroundColor Green
        } else {
            Write-Host "❌ Airtel production endpoint missing" -ForegroundColor Red
            $passed = $false
        }
        
        # Zamtel/Lupiya Endpoints
        if ($content -match "api\.lupiya\.com") {
            Write-Host "✅ Lupiya production endpoint configured" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Lupiya production endpoint not found" -ForegroundColor Yellow
        }
        
        # Check for sandbox/test endpoints
        if ($content -match "sandbox|test|demo" -and $content -notmatch "//.*sandbox|#.*test") {
            Write-Host "⚠️ Test/sandbox endpoints detected in production config" -ForegroundColor Yellow
        }
        
        # Validate API key placeholders
        if ($content -match "REPLACE_WITH_ACTUAL|YOUR_API_KEY|PLACEHOLDER") {
            Write-Host "❌ API key placeholders found - update with real credentials" -ForegroundColor Red
            $passed = $false
        } else {
            Write-Host "✅ No API key placeholders detected" -ForegroundColor Green
        }
        
    } else {
        Write-Host "❌ Production configuration missing" -ForegroundColor Red
        $passed = $false
    }
    
    # Check mobile money service integration
    if (Test-Path "lib/features/mobile_money/data/services/mobile_money_service.dart") {
        $content = Get-Content "lib/features/mobile_money/data/services/mobile_money_service.dart" -Raw
        
        if ($content -match "ProductionConfig") {
            Write-Host "✅ Mobile money service uses production config" -ForegroundColor Green
        } else {
            Write-Host "❌ Mobile money service not using production config" -ForegroundColor Red
            $passed = $false
        }
    }
    
    Write-Host "📊 REAL_MOMO_ENDPOINTS Test Result: $(if ($passed) { 'PASSED' } else { 'FAILED' })" -ForegroundColor $(if ($passed) { 'Green' } else { 'Red' })
    return $passed
}

function Test-UssdFunctional {
    Write-Host "`n📱 TEST CASE: USSD_FUNCTIONAL" -ForegroundColor Yellow
    Write-Host "Validating USSD functionality for feature phones..." -ForegroundColor Gray
    
    $passed = $true
    
    # Check USSD service implementation
    if (Test-Path "lib/services/ussd_service.dart") {
        $content = Get-Content "lib/services/ussd_service.dart" -Raw
        
        if ($content -match "handleFeaturePhones") {
            Write-Host "✅ Feature phone support implemented" -ForegroundColor Green
        } else {
            Write-Host "❌ Feature phone support missing" -ForegroundColor Red
            $passed = $false
        }
        
        # Check Zambian USSD codes
        if ($content -match "\*303#.*MTN") {
            Write-Host "✅ MTN USSD code (*303#) configured" -ForegroundColor Green
        } else {
            Write-Host "❌ MTN USSD code missing" -ForegroundColor Red
            $passed = $false
        }
        
        if ($content -match "\*778#.*AIRTEL") {
            Write-Host "✅ Airtel USSD code (*778#) configured" -ForegroundColor Green
        } else {
            Write-Host "❌ Airtel USSD code missing" -ForegroundColor Red
            $passed = $false
        }
        
        if ($content -match "\*456#.*ZAMTEL") {
            Write-Host "✅ Zamtel USSD code (*456#) configured" -ForegroundColor Green
        } else {
            Write-Host "❌ Zamtel USSD code missing" -ForegroundColor Red
            $passed = $false
        }
        
        # Check essential USSD menu items
        $menuItems = @("Check Balance", "Send Money", "Agent Locator", "Buy Airtime", "Pay Bills")
        foreach ($item in $menuItems) {
            if ($content -match $item) {
                Write-Host "✅ USSD menu item: $item" -ForegroundColor Green
            } else {
                Write-Host "❌ USSD menu item missing: $item" -ForegroundColor Red
                $passed = $false
            }
        }
        
        # Check USSD gateway integration
        if ($content -match "USSDGateway") {
            Write-Host "✅ USSD gateway integration implemented" -ForegroundColor Green
        } else {
            Write-Host "❌ USSD gateway integration missing" -ForegroundColor Red
            $passed = $false
        }
        
    } else {
        Write-Host "❌ USSD service implementation missing" -ForegroundColor Red
        $passed = $false
    }
    
    # Check production integration
    if (Test-Path "lib/core/production_lock.dart") {
        $content = Get-Content "lib/core/production_lock.dart" -Raw
        if ($content -match "ussd_service_enabled") {
            Write-Host "✅ USSD service integrated with production lock" -ForegroundColor Green
        } else {
            Write-Host "❌ USSD production integration missing" -ForegroundColor Red
            $passed = $false
        }
    }
    
    Write-Host "📊 USSD_FUNCTIONAL Test Result: $(if ($passed) { 'PASSED' } else { 'FAILED' })" -ForegroundColor $(if ($passed) { 'Green' } else { 'Red' })
    return $passed
}

function Test-PaymentAmount {
    Write-Host "`n💰 PAYMENT TEST AMOUNT VALIDATION" -ForegroundColor Yellow
    Write-Host "Validating payment test amount: K$PaymentTestAmount..." -ForegroundColor Gray
    
    $passed = $true
    
    # Validate amount is within acceptable range for testing
    if ($PaymentTestAmount -lt 0.01) {
        Write-Host "❌ Payment test amount too small (minimum K0.01)" -ForegroundColor Red
        $passed = $false
    } elseif ($PaymentTestAmount -gt 1.00) {
        Write-Host "⚠️ Payment test amount is high for testing (K$PaymentTestAmount)" -ForegroundColor Yellow
    } else {
        Write-Host "✅ Payment test amount is appropriate: K$PaymentTestAmount" -ForegroundColor Green
    }
    
    # Check transaction limits configuration
    if (Test-Path "lib/core/config/production_config.dart") {
        $content = Get-Content "lib/core/config/production_config.dart" -Raw
        
        if ($content -match "transactionLimits") {
            Write-Host "✅ Transaction limits configured" -ForegroundColor Green
            
            # Check if test amount is within limits
            if ($content -match "minimum_amount.*1\.0") {
                if ($PaymentTestAmount -ge 1.0) {
                    Write-Host "✅ Test amount meets minimum transaction limit" -ForegroundColor Green
                } else {
                    Write-Host "⚠️ Test amount below minimum transaction limit (K1.00)" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "❌ Transaction limits not configured" -ForegroundColor Red
            $passed = $false
        }
    }
    
    Write-Host "📊 PAYMENT_AMOUNT Test Result: $(if ($passed) { 'PASSED' } else { 'FAILED' })" -ForegroundColor $(if ($passed) { 'Green' } else { 'Red' })
    return $passed
}

function Test-BiometricRequirement {
    Write-Host "`n🔐 BIOMETRIC REQUIREMENT VALIDATION" -ForegroundColor Yellow
    Write-Host "Validating biometric authentication requirement..." -ForegroundColor Gray
    
    $passed = $true
    
    if ($RequireBiometric) {
        # Check biometric service implementation
        if (Test-Path "lib/core/security/biometric_service.dart") {
            Write-Host "✅ Biometric service implemented" -ForegroundColor Green
        } else {
            Write-Host "❌ Biometric service missing" -ForegroundColor Red
            $passed = $false
        }
        
        # Check Zambia auth flow biometric integration
        if (Test-Path "lib/auth/zambia_flow.dart") {
            $content = Get-Content "lib/auth/zambia_flow.dart" -Raw
            
            if ($content -match "biometricBackup.*true") {
                Write-Host "✅ Biometric backup enabled in auth flow" -ForegroundColor Green
            } else {
                Write-Host "❌ Biometric backup not enabled" -ForegroundColor Red
                $passed = $false
            }
            
            if ($content -match "setupSecurePIN.*biometric") {
                Write-Host "✅ Biometric PIN setup integration" -ForegroundColor Green
            } else {
                Write-Host "❌ Biometric PIN integration missing" -ForegroundColor Red
                $passed = $false
            }
        }
        
        # Check production lock biometric requirement
        if (Test-Path "lib/core/production_lock.dart") {
            $content = Get-Content "lib/core/production_lock.dart" -Raw
            
            if ($content -match "require_biometric_for_transactions") {
                Write-Host "✅ Biometric requirement in production lock" -ForegroundColor Green
            } else {
                Write-Host "❌ Biometric requirement not configured" -ForegroundColor Red
                $passed = $false
            }
        }
    } else {
        Write-Host "ℹ️ Biometric requirement not specified - optional validation" -ForegroundColor Blue
    }
    
    Write-Host "📊 BIOMETRIC_REQUIREMENT Test Result: $(if ($passed) { 'PASSED' } else { 'FAILED' })" -ForegroundColor $(if ($passed) { 'Green' } else { 'Red' })
    return $passed
}

# Execute test cases
Write-Host "EXECUTING PRODUCTION CHECKLIST TESTS" -ForegroundColor Cyan
Write-Host ""

# Run specified test cases
if ($testCaseList -contains "no_dummy_data") {
    $testResults["no_dummy_data"] = Test-NoDummyData
    $allTestsPassed = $allTestsPassed -and $testResults["no_dummy_data"]
}

if ($testCaseList -contains "real_momo_endpoints") {
    $testResults["real_momo_endpoints"] = Test-RealMomoEndpoints
    $allTestsPassed = $allTestsPassed -and $testResults["real_momo_endpoints"]
}

if ($testCaseList -contains "ussd_functional") {
    $testResults["ussd_functional"] = Test-UssdFunctional
    $allTestsPassed = $allTestsPassed -and $testResults["ussd_functional"]
}

# Always run payment amount and biometric tests
$testResults["payment_amount"] = Test-PaymentAmount
$allTestsPassed = $allTestsPassed -and $testResults["payment_amount"]

$testResults["biometric_requirement"] = Test-BiometricRequirement
$allTestsPassed = $allTestsPassed -and $testResults["biometric_requirement"]

# Generate final report
Write-Host "`nPRODUCTION CHECKLIST SUMMARY" -ForegroundColor Cyan
Write-Host ("=" * 50) -ForegroundColor Cyan

foreach ($test in $testResults.Keys) {
    $status = if ($testResults[$test]) { "✅ PASSED" } else { "❌ FAILED" }
    $color = if ($testResults[$test]) { "Green" } else { "Red" }
    Write-Host "$($test.ToUpper()): $status" -ForegroundColor $color
}

Write-Host ""
if ($allTestsPassed) {
    Write-Host "🎉 ALL PRODUCTION CHECKLIST TESTS PASSED" -ForegroundColor Green
    Write-Host "🇿🇲 Pay Mule Zambia is READY FOR PRODUCTION DEPLOYMENT" -ForegroundColor Green
    
    # Generate deployment report
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $reportContent = "PAY MULE ZAMBIA - PRODUCTION DEPLOYMENT CHECKLIST REPORT`n"
    $reportContent += "================================================================`n"
    $reportContent += "Execution Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')`n"
    $reportContent += "Test Cases: $TestCases`n"
    $reportContent += "Payment Test Amount: K$PaymentTestAmount`n"
    $reportContent += "Require Biometric: $RequireBiometric`n"
    $reportContent += "Overall Status: PASSED`n`n"
    
    $reportContent += "TEST RESULTS:`n"
    foreach ($test in $testResults.Keys) {
        $status = if ($testResults[$test]) { "PASSED" } else { "FAILED" }
        $reportContent += "* $($test.ToUpper()): $status`n"
    }
    
    $reportContent += "`nDEPLOYMENT READINESS: APPROVED`n"
    $reportContent += "================================`n"
    $reportContent += "Pay Mule Zambia has passed all production checklist tests`n"
    $reportContent += "and is approved for live deployment in Zambia.`n`n"
    $reportContent += "Report Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')`n"
    
    $reportPath = "zambia_production_checklist_report_$timestamp.txt"
    $reportContent | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "📄 Production checklist report generated: $reportPath" -ForegroundColor Green
    
    exit 0
} else {
    Write-Host "❌ PRODUCTION CHECKLIST TESTS FAILED" -ForegroundColor Red
    Write-Host "🚫 Pay Mule Zambia is NOT READY for production deployment" -ForegroundColor Red
    Write-Host "Please address the failed tests before proceeding" -ForegroundColor Yellow
    exit 1
}
