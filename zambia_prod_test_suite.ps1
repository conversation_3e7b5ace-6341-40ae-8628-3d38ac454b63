#!/usr/bin/env pwsh

# Pay Mule Zambia Production Test Suite
# Comprehensive pre-release testing including icon integrity, app name consistency,
# data purge verification, and real payment testing with 0.50 ZMW transactions

param(
    [string]$Tests = "icon_integrity,app_name_consistency,data_purge_verification",
    [string]$Network = "production",
    [decimal]$PaymentAmount = 0.50,
    [string]$DeviceProfiles = "TecnoSpark7,SamsungA20,iPhoneSE",
    [string]$ReportFormat = "html"
)

# Configuration
$TestResultsDir = "test_results\production_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
$LogFile = "$TestResultsDir\test_execution.log"

function Write-Log {
    param([string]$Level, [string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    Add-Content -Path $LogFile -Value $logEntry
}

function Write-Info {
    param([string]$Message)
    Write-Log "INFO" $Message
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
    Write-Log "SUCCESS" $Message
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
    Write-Log "WARNING" $Message
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
    Write-Log "ERROR" $Message
}

# Initialize test environment
function Initialize-TestEnvironment {
    Write-Info "Initializing Pay Mule Production Test Suite"
    Write-Info "============================================"
    
    # Create test results directory
    if (-not (Test-Path $TestResultsDir)) {
        New-Item -ItemType Directory -Path $TestResultsDir -Force | Out-Null
    }
    
    # Initialize log file
    "Pay Mule Production Test Suite - $(Get-Date)" | Out-File -FilePath $LogFile -Encoding UTF8
    
    Write-Info "Test Configuration:"
    Write-Info "  Tests: $Tests"
    Write-Info "  Network: $Network"
    Write-Info "  Payment Amount: K$PaymentAmount ZMW"
    Write-Info "  Device Profiles: $DeviceProfiles"
    Write-Info "  Report Format: $ReportFormat"
    Write-Info "  Results Directory: $TestResultsDir"
}

# Test 1: Icon Integrity Test
function Test-IconIntegrity {
    Write-Info "Running Icon Integrity Test..."
    
    $testResults = @{
        "test_name" = "Icon Integrity"
        "status" = "PASS"
        "details" = @()
        "errors" = @()
    }
    
    try {
        # Check Android icons
        $androidDensities = @("mdpi", "hdpi", "xhdpi", "xxhdpi", "xxxhdpi")
        foreach ($density in $androidDensities) {
            $iconPath = "android\app\src\main\res\mipmap-$density\ic_launcher.png"
            if (Test-Path $iconPath) {
                $size = (Get-Item $iconPath).Length
                if ($size -gt 1KB) {
                    $testResults.details += "✅ Android $density icon: $([math]::Round($size/1KB, 1)) KB"
                } else {
                    $testResults.errors += "❌ Android $density icon too small: $size bytes"
                    $testResults.status = "FAIL"
                }
            } else {
                $testResults.errors += "❌ Missing Android $density icon"
                $testResults.status = "FAIL"
            }
        }
        
        # Check iOS icons
        $iosIcons = @("<EMAIL>", "<EMAIL>")
        foreach ($icon in $iosIcons) {
            $iconPath = "ios\Runner\Assets.xcassets\AppIcon.appiconset\$icon"
            if (Test-Path $iconPath) {
                $size = (Get-Item $iconPath).Length
                if ($size -gt 5KB) {
                    $testResults.details += "✅ iOS $icon: $([math]::Round($size/1KB, 1)) KB"
                } else {
                    $testResults.errors += "❌ iOS $icon too small: $size bytes"
                    $testResults.status = "FAIL"
                }
            } else {
                $testResults.errors += "❌ Missing iOS icon: $icon"
                $testResults.status = "FAIL"
            }
        }
        
        # Run Flutter icon validation test
        Write-Info "Running Flutter icon validation test..."
        $flutterTestResult = flutter test integration_test\icon_validation_test.dart 2>&1
        if ($LASTEXITCODE -eq 0) {
            $testResults.details += "✅ Flutter icon validation test passed"
        } else {
            $testResults.errors += "❌ Flutter icon validation test failed"
            $testResults.status = "FAIL"
        }
        
    } catch {
        $testResults.errors += "❌ Icon integrity test exception: $($_.Exception.Message)"
        $testResults.status = "FAIL"
    }
    
    if ($testResults.status -eq "PASS") {
        Write-Success "Icon Integrity Test: PASSED"
    } else {
        Write-Error "Icon Integrity Test: FAILED"
        $testResults.errors | ForEach-Object { Write-Error "  $_" }
    }
    
    return $testResults
}

# Test 2: App Name Consistency Test
function Test-AppNameConsistency {
    Write-Info "Running App Name Consistency Test..."
    
    $testResults = @{
        "test_name" = "App Name Consistency"
        "status" = "PASS"
        "details" = @()
        "errors" = @()
    }
    
    try {
        # Check Android app name
        $androidManifest = Get-Content "android\app\src\main\AndroidManifest.xml" -Raw
        if ($androidManifest -match 'android:label="@string/app_name"') {
            $testResults.details += "✅ Android manifest uses string resource"
            
            $stringsXml = Get-Content "android\app\src\main\res\values\strings.xml" -Raw
            if ($stringsXml -match '<string name="app_name">Pay Mule</string>') {
                $testResults.details += "✅ Android strings.xml contains 'Pay Mule'"
            } else {
                $testResults.errors += "❌ Android strings.xml does not contain 'Pay Mule'"
                $testResults.status = "FAIL"
            }
        } else {
            $testResults.errors += "❌ Android manifest does not use string resource"
            $testResults.status = "FAIL"
        }
        
        # Check iOS app name
        $iosInfoPlist = Get-Content "ios\Runner\Info.plist" -Raw
        if ($iosInfoPlist -match '<string>Pay Mule</string>') {
            $testResults.details += "✅ iOS Info.plist contains 'Pay Mule'"
        } else {
            $testResults.errors += "❌ iOS Info.plist does not contain 'Pay Mule'"
            $testResults.status = "FAIL"
        }
        
        # Check app config
        $appConfig = Get-Content "lib\core\config\app_config.dart" -Raw
        if ($appConfig -match "appName = 'Pay Mule'") {
            $testResults.details += "✅ App config contains 'Pay Mule'"
        } else {
            $testResults.errors += "❌ App config does not contain 'Pay Mule'"
            $testResults.status = "FAIL"
        }
        
        # Check pubspec.yaml
        $pubspec = Get-Content "pubspec.yaml" -Raw
        if ($pubspec -match "name: pay_mule") {
            $testResults.details += "✅ pubspec.yaml package name is 'pay_mule'"
        } else {
            $testResults.errors += "❌ pubspec.yaml package name is not 'pay_mule'"
            $testResults.status = "FAIL"
        }
        
    } catch {
        $testResults.errors += "❌ App name consistency test exception: $($_.Exception.Message)"
        $testResults.status = "FAIL"
    }
    
    if ($testResults.status -eq "PASS") {
        Write-Success "App Name Consistency Test: PASSED"
    } else {
        Write-Error "App Name Consistency Test: FAILED"
        $testResults.errors | ForEach-Object { Write-Error "  $_" }
    }
    
    return $testResults
}

# Test 3: Data Purge Verification Test
function Test-DataPurgeVerification {
    Write-Info "Running Data Purge Verification Test..."
    
    $testResults = @{
        "test_name" = "Data Purge Verification"
        "status" = "PASS"
        "details" = @()
        "errors" = @()
    }
    
    try {
        # Check that test configuration files are removed
        $testFiles = @(
            "test\test_config.json",
            "lib\main_phase1.dart",
            "scripts\deploy_phase1.sh"
        )
        
        foreach ($file in $testFiles) {
            if (-not (Test-Path $file)) {
                $testResults.details += "✅ Test file removed: $file"
            } else {
                $testResults.errors += "❌ Test file still exists: $file"
                $testResults.status = "FAIL"
            }
        }
        
        # Check environment configuration
        $envConfig = Get-Content "lib\core\config\environment_config.dart" -Raw
        if ($envConfig -match "defaultValue: 'production'") {
            $testResults.details += "✅ Environment config defaults to production"
        } else {
            $testResults.errors += "❌ Environment config does not default to production"
            $testResults.status = "FAIL"
        }
        
        # Check app config production flag
        $appConfig = Get-Content "lib\core\config\app_config.dart" -Raw
        if ($appConfig -match "isProduction = true") {
            $testResults.details += "✅ App config isProduction set to true"
        } else {
            $testResults.errors += "❌ App config isProduction not set to true"
            $testResults.status = "FAIL"
        }
        
        # Check for sandbox/test references
        $suspiciousFiles = Get-ChildItem -Path "lib" -Recurse -Include "*.dart" | 
            Where-Object { (Get-Content $_.FullName -Raw) -match "(sandbox|test_account|dummy_merchant)" }
        
        if ($suspiciousFiles.Count -eq 0) {
            $testResults.details += "✅ No suspicious test/sandbox references found"
        } else {
            foreach ($file in $suspiciousFiles) {
                $testResults.errors += "❌ Suspicious test/sandbox reference in: $($file.Name)"
            }
            $testResults.status = "FAIL"
        }
        
        # Check production config exists
        if (Test-Path "lib\core\config\production_config.dart") {
            $testResults.details += "✅ Production config file exists"
        } else {
            $testResults.errors += "❌ Production config file missing"
            $testResults.status = "FAIL"
        }
        
    } catch {
        $testResults.errors += "❌ Data purge verification test exception: $($_.Exception.Message)"
        $testResults.status = "FAIL"
    }
    
    if ($testResults.status -eq "PASS") {
        Write-Success "Data Purge Verification Test: PASSED"
    } else {
        Write-Error "Data Purge Verification Test: FAILED"
        $testResults.errors | ForEach-Object { Write-Error "  $_" }
    }
    
    return $testResults
}

# Test 4: Real Payment Test (0.50 ZMW)
function Test-RealPayment {
    Write-Info "Running Real Payment Test (K$PaymentAmount ZMW)..."
    
    $testResults = @{
        "test_name" = "Real Payment Test"
        "status" = "PASS"
        "details" = @()
        "errors" = @()
        "warnings" = @()
    }
    
    try {
        # This is a placeholder for real payment testing
        # In a real scenario, this would integrate with actual payment APIs
        
        Write-Warning "Real payment testing requires actual API credentials and test accounts"
        $testResults.warnings += "⚠️ Real payment test requires manual execution with actual credentials"
        $testResults.warnings += "⚠️ Test amount: K$PaymentAmount ZMW"
        $testResults.warnings += "⚠️ Ensure test accounts are configured for MTN/Airtel"
        
        # Check that production config is properly set up
        $productionConfig = Get-Content "lib\core\config\production_config.dart" -Raw
        if ($productionConfig -match "REPLACE_WITH_ACTUAL") {
            $testResults.warnings += "⚠️ Production credentials still contain placeholder values"
        } else {
            $testResults.details += "✅ Production credentials appear to be configured"
        }
        
        # Simulate payment test structure
        $testResults.details += "📋 Payment test checklist:"
        $testResults.details += "  1. Configure MTN production credentials"
        $testResults.details += "  2. Configure Airtel production credentials"
        $testResults.details += "  3. Test K$PaymentAmount transaction with MTN"
        $testResults.details += "  4. Test K$PaymentAmount transaction with Airtel"
        $testResults.details += "  5. Verify transaction status callbacks"
        $testResults.details += "  6. Verify balance updates"
        $testResults.details += "  7. Test transaction history"
        
        $testResults.status = "MANUAL_REQUIRED"
        
    } catch {
        $testResults.errors += "❌ Real payment test exception: $($_.Exception.Message)"
        $testResults.status = "FAIL"
    }
    
    Write-Warning "Real Payment Test: MANUAL EXECUTION REQUIRED"
    $testResults.warnings | ForEach-Object { Write-Warning "  $_" }
    
    return $testResults
}

# Generate HTML report
function New-HtmlReport {
    param([array]$TestResults)
    
    $reportFile = "$TestResultsDir\test_report.html"
    
    $html = @"
<!DOCTYPE html>
<html>
<head>
    <title>Pay Mule Production Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #2E8B57; color: white; padding: 20px; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { background-color: #d4edda; border-color: #c3e6cb; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; }
        .manual { background-color: #fff3cd; border-color: #ffeaa7; }
        .details { margin: 10px 0; }
        .error { color: #721c24; }
        .warning { color: #856404; }
        .success { color: #155724; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Pay Mule Production Test Report</h1>
        <p>Generated: $(Get-Date)</p>
        <p>Network: $Network | Payment Amount: K$PaymentAmount ZMW</p>
    </div>
"@
    
    foreach ($result in $TestResults) {
        $cssClass = switch ($result.status) {
            "PASS" { "pass" }
            "FAIL" { "fail" }
            "MANUAL_REQUIRED" { "manual" }
            default { "manual" }
        }
        
        $html += @"
    <div class="test-section $cssClass">
        <h2>$($result.test_name) - $($result.status)</h2>
        <div class="details">
"@
        
        foreach ($detail in $result.details) {
            $html += "            <p class='success'>$detail</p>`n"
        }
        
        foreach ($error in $result.errors) {
            $html += "            <p class='error'>$error</p>`n"
        }
        
        foreach ($warning in $result.warnings) {
            $html += "            <p class='warning'>$warning</p>`n"
        }
        
        $html += @"
        </div>
    </div>
"@
    }
    
    $html += @"
</body>
</html>
"@
    
    $html | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Success "HTML report generated: $reportFile"
}

# Main execution
function Main {
    Initialize-TestEnvironment
    
    $allTestResults = @()
    $testList = $Tests -split ","
    
    foreach ($test in $testList) {
        switch ($test.Trim()) {
            "icon_integrity" {
                $allTestResults += Test-IconIntegrity
            }
            "app_name_consistency" {
                $allTestResults += Test-AppNameConsistency
            }
            "data_purge_verification" {
                $allTestResults += Test-DataPurgeVerification
            }
            "real_payment" {
                $allTestResults += Test-RealPayment
            }
            default {
                Write-Warning "Unknown test: $test"
            }
        }
    }
    
    # Generate report
    if ($ReportFormat -eq "html") {
        New-HtmlReport -TestResults $allTestResults
    }
    
    # Summary
    $passCount = ($allTestResults | Where-Object { $_.status -eq "PASS" }).Count
    $failCount = ($allTestResults | Where-Object { $_.status -eq "FAIL" }).Count
    $manualCount = ($allTestResults | Where-Object { $_.status -eq "MANUAL_REQUIRED" }).Count
    
    Write-Info ""
    Write-Info "Test Suite Summary:"
    Write-Info "=================="
    Write-Success "Passed: $passCount"
    if ($failCount -gt 0) {
        Write-Error "Failed: $failCount"
    } else {
        Write-Info "Failed: $failCount"
    }
    Write-Warning "Manual Required: $manualCount"
    
    if ($failCount -eq 0) {
        Write-Success "All automated tests passed! Ready for production deployment."
    } else {
        Write-Error "Some tests failed. Please fix issues before production deployment."
        exit 1
    }
}

# Run main function
Main
