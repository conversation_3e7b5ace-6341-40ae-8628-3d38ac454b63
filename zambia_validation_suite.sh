#!/bin/bash

# 🇿🇲 Zambia Pay - Comprehensive Validation Suite
# Critical validation protocol for mobile money, offline sync, and notifications
# Usage: ./zambia_validation_suite.sh --critical-modules="momo,offline,notifications" --coverage-threshold=90% --max-failures=0 --report-file=validation_report.html

set -e

# Default configuration
CRITICAL_MODULES="momo,offline,notifications"
COVERAGE_THRESHOLD=90
MAX_FAILURES=0
REPORT_FILE="validation_report.html"
VERBOSE=false
ENVIRONMENT="sandbox"
REGION="eastern_province"
TIMEOUT=300
PARALLEL_TESTS=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Validation metrics
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
COVERAGE_ACTUAL=0
START_TIME=$(date +%s)

# Test results storage
declare -A TEST_RESULTS
declare -A MODULE_RESULTS

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_debug() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${PURPLE}🔍 $1${NC}"
    fi
}

print_critical() {
    echo -e "${CYAN}🚨 CRITICAL: $1${NC}"
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --critical-modules=*)
                CRITICAL_MODULES="${1#*=}"
                shift
                ;;
            --coverage-threshold=*)
                COVERAGE_THRESHOLD="${1#*=}"
                COVERAGE_THRESHOLD="${COVERAGE_THRESHOLD%\%}"
                shift
                ;;
            --max-failures=*)
                MAX_FAILURES="${1#*=}"
                shift
                ;;
            --report-file=*)
                REPORT_FILE="${1#*=}"
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --environment=*)
                ENVIRONMENT="${1#*=}"
                shift
                ;;
            --region=*)
                REGION="${1#*=}"
                shift
                ;;
            --timeout=*)
                TIMEOUT="${1#*=}"
                shift
                ;;
            --no-parallel)
                PARALLEL_TESTS=false
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Show help information
show_help() {
    cat << EOF
🇿🇲 Zambia Pay Validation Suite

USAGE:
    ./zambia_validation_suite.sh [OPTIONS]

OPTIONS:
    --critical-modules=MODULES     Comma-separated list of critical modules to test
                                  (default: "momo,offline,notifications")
    --coverage-threshold=PERCENT   Minimum test coverage required (default: 90%)
    --max-failures=NUMBER         Maximum allowed test failures (default: 0)
    --report-file=FILE            Output report file (default: validation_report.html)
    --verbose                     Enable verbose output
    --environment=ENV             Test environment (default: sandbox)
    --region=REGION              Test region (default: eastern_province)
    --timeout=SECONDS            Test timeout in seconds (default: 300)
    --no-parallel                Disable parallel test execution
    --help                       Show this help message

CRITICAL MODULES:
    momo                         Mobile money API stability tests
    offline                      Offline sync integrity tests
    notifications               Notification delivery metric tests
    
EXAMPLES:
    # Basic validation
    ./zambia_validation_suite.sh
    
    # High-coverage validation with verbose output
    ./zambia_validation_suite.sh --coverage-threshold=95% --verbose
    
    # Test specific modules only
    ./zambia_validation_suite.sh --critical-modules="momo,offline"
    
    # Production-ready validation
    ./zambia_validation_suite.sh --max-failures=0 --coverage-threshold=95% --report-file=prod_validation.html

EOF
}

# Initialize validation environment
initialize_validation() {
    print_info "🇿🇲 Initializing Zambia Pay Validation Suite"
    print_info "Environment: $ENVIRONMENT | Region: $REGION"
    print_info "Critical Modules: $CRITICAL_MODULES"
    print_info "Coverage Threshold: $COVERAGE_THRESHOLD%"
    print_info "Max Failures: $MAX_FAILURES"
    echo ""
    
    # Create output directory
    mkdir -p "validation_results/$(date +%Y%m%d_%H%M%S)"
    
    # Check Flutter environment
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed"
        exit 1
    fi
    
    # Check dependencies
    print_debug "Checking Flutter dependencies..."
    flutter pub get > /dev/null 2>&1
    
    print_status "Validation environment initialized"
}

# Validate mobile money API stability
validate_momo_api() {
    print_info "🏦 Validating Mobile Money API Stability..."
    
    local module_passed=0
    local module_total=0
    
    # Test MTN Mobile Money API
    print_debug "Testing MTN Mobile Money API..."
    ((module_total++))
    if run_momo_test "MTN" "260963000001"; then
        ((module_passed++))
        TEST_RESULTS["momo_mtn"]="PASSED"
    else
        TEST_RESULTS["momo_mtn"]="FAILED"
    fi
    
    # Test Airtel Money API
    print_debug "Testing Airtel Money API..."
    ((module_total++))
    if run_momo_test "AIRTEL" "260973000001"; then
        ((module_passed++))
        TEST_RESULTS["momo_airtel"]="PASSED"
    else
        TEST_RESULTS["momo_airtel"]="FAILED"
    fi
    
    # Test Zamtel Kwacha API
    print_debug "Testing Zamtel Kwacha API..."
    ((module_total++))
    if run_momo_test "ZAMTEL" "260953000001"; then
        ((module_passed++))
        TEST_RESULTS["momo_zamtel"]="PASSED"
    else
        TEST_RESULTS["momo_zamtel"]="FAILED"
    fi
    
    # Test provider detection
    print_debug "Testing provider detection..."
    ((module_total++))
    if run_provider_detection_test; then
        ((module_passed++))
        TEST_RESULTS["momo_detection"]="PASSED"
    else
        TEST_RESULTS["momo_detection"]="FAILED"
    fi
    
    # Test transaction validation
    print_debug "Testing transaction validation..."
    ((module_total++))
    if run_transaction_validation_test; then
        ((module_passed++))
        TEST_RESULTS["momo_validation"]="PASSED"
    else
        TEST_RESULTS["momo_validation"]="FAILED"
    fi
    
    MODULE_RESULTS["momo"]="$module_passed/$module_total"
    TOTAL_TESTS=$((TOTAL_TESTS + module_total))
    PASSED_TESTS=$((PASSED_TESTS + module_passed))
    FAILED_TESTS=$((FAILED_TESTS + module_total - module_passed))
    
    if [ $module_passed -eq $module_total ]; then
        print_status "Mobile Money API validation completed successfully ($module_passed/$module_total)"
    else
        print_error "Mobile Money API validation failed ($module_passed/$module_total)"
    fi
}

# Run individual mobile money test
run_momo_test() {
    local provider=$1
    local phone_number=$2
    
    cat > "temp_momo_${provider,,}_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/mobile_money/mobile_money_service.dart';

void main() {
  group('$provider Mobile Money API Test', () {
    late MobileMoneyService service;
    
    setUp(() {
      service = MobileMoneyService();
    });
    
    test('should detect $provider provider correctly', () {
      final provider = service.detectProvider('$phone_number');
      expect(provider, equals('$provider'));
    });
    
    test('should validate $provider phone number format', () {
      final isValid = service.validatePhoneNumber('$phone_number');
      expect(isValid, isTrue);
    });
    
    test('should calculate correct transaction fees for $provider', () {
      final fee = service.calculateFee('$provider', 1000.0);
      expect(fee, greaterThan(0));
      expect(fee, lessThan(50.0)); // Reasonable fee range
    });
  });
}
EOF
    
    flutter test "temp_momo_${provider,,}_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_momo_${provider,,}_test.dart"
    return $result
}

# Run provider detection test
run_provider_detection_test() {
    cat > "temp_provider_detection_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/mobile_money/mobile_money_service.dart';

void main() {
  group('Provider Detection Tests', () {
    late MobileMoneyService service;

    setUp(() {
      service = MobileMoneyService();
    });

    test('should detect MTN for 096 numbers', () {
      expect(service.detectProvider('26**********'), equals('MTN'));
      expect(service.detectProvider('**********'), equals('MTN'));
    });

    test('should detect Airtel for 097 numbers', () {
      expect(service.detectProvider('26**********'), equals('AIRTEL'));
      expect(service.detectProvider('**********'), equals('AIRTEL'));
    });

    test('should detect Zamtel for 095 numbers', () {
      expect(service.detectProvider('26**********'), equals('ZAMTEL'));
      expect(service.detectProvider('**********'), equals('ZAMTEL'));
    });
  });
}
EOF

    flutter test "temp_provider_detection_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_provider_detection_test.dart"
    return $result
}

# Run transaction validation test
run_transaction_validation_test() {
    cat > "temp_transaction_validation_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/mobile_money/mobile_money_service.dart';

void main() {
  group('Transaction Validation Tests', () {
    late MobileMoneyService service;

    setUp(() {
      service = MobileMoneyService();
    });

    test('should validate amount ranges', () {
      expect(service.validateAmount('MTN', 10.0), isTrue);
      expect(service.validateAmount('MTN', 50000.0), isTrue);
      expect(service.validateAmount('MTN', 5.0), isFalse);
      expect(service.validateAmount('MTN', 60000.0), isFalse);
    });

    test('should calculate Zambian mobile money levy', () {
      final fee = service.calculateFee('MTN', 1000.0);
      expect(fee, equals(2.1)); // 0.21% levy
    });
  });
}
EOF

    flutter test "temp_transaction_validation_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_transaction_validation_test.dart"
    return $result
}

# Validate offline sync integrity
validate_offline_sync() {
    print_info "📱 Validating Offline Sync Integrity..."

    local module_passed=0
    local module_total=0

    # Test offline transaction queue
    print_debug "Testing offline transaction queue..."
    ((module_total++))
    if run_offline_queue_test; then
        ((module_passed++))
        TEST_RESULTS["offline_queue"]="PASSED"
    else
        TEST_RESULTS["offline_queue"]="FAILED"
    fi

    # Test data synchronization
    print_debug "Testing data synchronization..."
    ((module_total++))
    if run_sync_test; then
        ((module_passed++))
        TEST_RESULTS["offline_sync"]="PASSED"
    else
        TEST_RESULTS["offline_sync"]="FAILED"
    fi

    # Test conflict resolution
    print_debug "Testing conflict resolution..."
    ((module_total++))
    if run_conflict_resolution_test; then
        ((module_passed++))
        TEST_RESULTS["offline_conflicts"]="PASSED"
    else
        TEST_RESULTS["offline_conflicts"]="FAILED"
    fi

    # Test offline storage encryption
    print_debug "Testing offline storage encryption..."
    ((module_total++))
    if run_encryption_test; then
        ((module_passed++))
        TEST_RESULTS["offline_encryption"]="PASSED"
    else
        TEST_RESULTS["offline_encryption"]="FAILED"
    fi

    MODULE_RESULTS["offline"]="$module_passed/$module_total"
    TOTAL_TESTS=$((TOTAL_TESTS + module_total))
    PASSED_TESTS=$((PASSED_TESTS + module_passed))
    FAILED_TESTS=$((FAILED_TESTS + module_total - module_passed))

    if [ $module_passed -eq $module_total ]; then
        print_status "Offline sync validation completed successfully ($module_passed/$module_total)"
    else
        print_error "Offline sync validation failed ($module_passed/$module_total)"
    fi
}

# Run offline queue test
run_offline_queue_test() {
    cat > "temp_offline_queue_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/offline/offline_queue_service.dart';

void main() {
  group('Offline Queue Tests', () {
    late OfflineQueueService queueService;

    setUp(() {
      queueService = OfflineQueueService();
    });

    test('should queue transactions when offline', () async {
      final transaction = {
        'id': 'test_tx_001',
        'amount': 100.0,
        'recipient': '26**********',
        'timestamp': DateTime.now().toIso8601String(),
      };

      await queueService.addToQueue(transaction);
      final queueSize = await queueService.getQueueSize();
      expect(queueSize, greaterThan(0));
    });

    test('should process queue when online', () async {
      final initialSize = await queueService.getQueueSize();
      await queueService.processQueue();
      final finalSize = await queueService.getQueueSize();
      expect(finalSize, lessThanOrEqualTo(initialSize));
    });
  });
}
EOF

    flutter test "temp_offline_queue_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_offline_queue_test.dart"
    return $result
}

# Run sync test
run_sync_test() {
    cat > "temp_sync_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/sync/sync_service.dart';

void main() {
  group('Data Synchronization Tests', () {
    late SyncService syncService;

    setUp(() {
      syncService = SyncService();
    });

    test('should sync pending transactions', () async {
      final result = await syncService.syncPendingTransactions();
      expect(result.success, isTrue);
      expect(result.syncedCount, greaterThanOrEqualTo(0));
    });

    test('should handle sync conflicts', () async {
      final result = await syncService.resolveConflicts();
      expect(result.success, isTrue);
    });
  });
}
EOF

    flutter test "temp_sync_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_sync_test.dart"
    return $result
}

# Run conflict resolution test
run_conflict_resolution_test() {
    cat > "temp_conflict_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/sync/conflict_resolver.dart';

void main() {
  group('Conflict Resolution Tests', () {
    late ConflictResolver resolver;

    setUp(() {
      resolver = ConflictResolver();
    });

    test('should resolve timestamp conflicts', () {
      final localData = {'timestamp': '2025-01-01T10:00:00Z', 'amount': 100.0};
      final serverData = {'timestamp': '2025-01-01T10:01:00Z', 'amount': 150.0};

      final resolved = resolver.resolveConflict(localData, serverData);
      expect(resolved['timestamp'], equals('2025-01-01T10:01:00Z'));
    });
  });
}
EOF

    flutter test "temp_conflict_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_conflict_test.dart"
    return $result
}

# Run encryption test
run_encryption_test() {
    cat > "temp_encryption_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/security/encryption_service.dart';

void main() {
  group('Encryption Tests', () {
    late EncryptionService encryptionService;

    setUp(() {
      encryptionService = EncryptionService();
    });

    test('should encrypt sensitive data', () {
      const sensitiveData = 'user_pin_1234';
      final encrypted = encryptionService.encrypt(sensitiveData);
      expect(encrypted, isNot(equals(sensitiveData)));
      expect(encrypted.length, greaterThan(sensitiveData.length));
    });

    test('should decrypt encrypted data correctly', () {
      const originalData = 'transaction_data_test';
      final encrypted = encryptionService.encrypt(originalData);
      final decrypted = encryptionService.decrypt(encrypted);
      expect(decrypted, equals(originalData));
    });
  });
}
EOF

    flutter test "temp_encryption_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_encryption_test.dart"
    return $result
}

# Validate notification delivery metrics
validate_notifications() {
    print_info "🔔 Validating Notification Delivery Metrics..."

    local module_passed=0
    local module_total=0

    # Test SMS notifications
    print_debug "Testing SMS notifications..."
    ((module_total++))
    if run_sms_notification_test; then
        ((module_passed++))
        TEST_RESULTS["notifications_sms"]="PASSED"
    else
        TEST_RESULTS["notifications_sms"]="FAILED"
    fi

    # Test push notifications
    print_debug "Testing push notifications..."
    ((module_total++))
    if run_push_notification_test; then
        ((module_passed++))
        TEST_RESULTS["notifications_push"]="PASSED"
    else
        TEST_RESULTS["notifications_push"]="FAILED"
    fi

    # Test utility bill alerts
    print_debug "Testing utility bill alerts..."
    ((module_total++))
    if run_utility_alert_test; then
        ((module_passed++))
        TEST_RESULTS["notifications_utility"]="PASSED"
    else
        TEST_RESULTS["notifications_utility"]="FAILED"
    fi

    # Test language localization
    print_debug "Testing notification localization..."
    ((module_total++))
    if run_localization_test; then
        ((module_passed++))
        TEST_RESULTS["notifications_localization"]="PASSED"
    else
        TEST_RESULTS["notifications_localization"]="FAILED"
    fi

    MODULE_RESULTS["notifications"]="$module_passed/$module_total"
    TOTAL_TESTS=$((TOTAL_TESTS + module_total))
    PASSED_TESTS=$((PASSED_TESTS + module_passed))
    FAILED_TESTS=$((FAILED_TESTS + module_total - module_passed))

    if [ $module_passed -eq $module_total ]; then
        print_status "Notification validation completed successfully ($module_passed/$module_total)"
    else
        print_error "Notification validation failed ($module_passed/$module_total)"
    fi
}

# Run SMS notification test
run_sms_notification_test() {
    cat > "temp_sms_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/notifications/sms_service.dart';

void main() {
  group('SMS Notification Tests', () {
    late SMSService smsService;

    setUp(() {
      smsService = SMSService();
    });

    test('should format Zambian phone numbers correctly', () {
      expect(smsService.formatPhoneNumber('**********'), equals('26**********'));
      expect(smsService.formatPhoneNumber('+26**********'), equals('26**********'));
    });

    test('should generate SMS tokens for offline transactions', () {
      final token = smsService.generateSMSToken('tx_12345', 100.0);
      expect(token.length, equals(8));
      expect(token, matches(r'^[A-Z0-9]{8}$'));
    });
  });
}
EOF

    flutter test "temp_sms_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_sms_test.dart"
    return $result
}

# Run push notification test
run_push_notification_test() {
    cat > "temp_push_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/notifications/push_service.dart';

void main() {
  group('Push Notification Tests', () {
    late PushService pushService;

    setUp(() {
      pushService = PushService();
    });

    test('should create transaction notifications', () {
      final notification = pushService.createTransactionNotification(
        'tx_12345', 100.0, 'MTN', 'COMPLETED'
      );
      expect(notification.title, contains('Transaction'));
      expect(notification.body, contains('K100.00'));
    });

    test('should handle notification permissions', () async {
      final hasPermission = await pushService.checkPermissions();
      expect(hasPermission, isA<bool>());
    });
  });
}
EOF

    flutter test "temp_push_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_push_test.dart"
    return $result
}

# Run utility alert test
run_utility_alert_test() {
    cat > "temp_utility_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/features/utilities/utility_alert_service.dart';

void main() {
  group('Utility Alert Tests', () {
    late UtilityAlertService alertService;

    setUp(() {
      alertService = UtilityAlertService();
    });

    test('should create ZESCO bill alerts', () {
      final alert = alertService.createBillAlert(
        'ZESCO', '**********', 180.0, DateTime.now().add(Duration(days: 7))
      );
      expect(alert.provider, equals('ZESCO'));
      expect(alert.amount, equals(180.0));
    });

    test('should schedule alerts before due date', () {
      final dueDate = DateTime.now().add(Duration(days: 10));
      final alertDate = alertService.calculateAlertDate(dueDate, 7);
      expect(alertDate.isBefore(dueDate), isTrue);
    });
  });
}
EOF

    flutter test "temp_utility_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_utility_test.dart"
    return $result
}

# Run localization test
run_localization_test() {
    cat > "temp_localization_test.dart" << EOF
import 'package:flutter_test/flutter_test.dart';
import 'package:zambia_pay/core/localization/localization_service.dart';

void main() {
  group('Localization Tests', () {
    late LocalizationService localizationService;

    setUp(() {
      localizationService = LocalizationService();
    });

    test('should translate to Nyanja', () {
      final translated = localizationService.translate('transaction_successful', 'ny');
      expect(translated, isNotEmpty);
      expect(translated, isNot(equals('transaction_successful')));
    });

    test('should handle missing translations gracefully', () {
      final translated = localizationService.translate('non_existent_key', 'ny');
      expect(translated, equals('non_existent_key'));
    });
  });
}
EOF

    flutter test "temp_localization_test.dart" --reporter=compact > /dev/null 2>&1
    local result=$?
    rm -f "temp_localization_test.dart"
    return $result
}

# Calculate test coverage
calculate_coverage() {
    print_info "📊 Calculating test coverage..."

    # Run Flutter test with coverage
    flutter test --coverage > /dev/null 2>&1

    if [ -f "coverage/lcov.info" ]; then
        # Parse coverage from lcov.info
        local total_lines=$(grep -c "^LF:" coverage/lcov.info | head -1)
        local covered_lines=$(grep -c "^LH:" coverage/lcov.info | head -1)

        if [ "$total_lines" -gt 0 ]; then
            COVERAGE_ACTUAL=$((covered_lines * 100 / total_lines))
        else
            COVERAGE_ACTUAL=0
        fi
    else
        print_warning "Coverage file not found, using estimated coverage"
        COVERAGE_ACTUAL=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    fi

    print_debug "Actual coverage: $COVERAGE_ACTUAL%"
}

# Generate validation report
generate_report() {
    local end_time=$(date +%s)
    local duration=$((end_time - START_TIME))
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))

    print_info "📋 Generating validation report..."

    cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>🇿🇲 Zambia Pay Validation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2E8B57; color: white; padding: 20px; border-radius: 5px; }
        .summary { background: #f0f8ff; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .module { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .warning { color: #ffc107; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🇿🇲 Zambia Pay Validation Report</h1>
        <p>Generated on $(date)</p>
        <p>Environment: $ENVIRONMENT | Region: $REGION</p>
    </div>

    <div class="summary">
        <h2>📊 Validation Summary</h2>
        <div class="metric">
            <strong>Total Tests:</strong> $TOTAL_TESTS
        </div>
        <div class="metric">
            <strong>Passed:</strong> <span class="passed">$PASSED_TESTS</span>
        </div>
        <div class="metric">
            <strong>Failed:</strong> <span class="failed">$FAILED_TESTS</span>
        </div>
        <div class="metric">
            <strong>Success Rate:</strong> $success_rate%
        </div>
        <div class="metric">
            <strong>Coverage:</strong> $COVERAGE_ACTUAL%
        </div>
        <div class="metric">
            <strong>Duration:</strong> ${duration}s
        </div>
    </div>
EOF

    # Add module results
    echo "    <div class=\"module\">" >> "$REPORT_FILE"
    echo "        <h2>🏦 Module Results</h2>" >> "$REPORT_FILE"
    echo "        <table>" >> "$REPORT_FILE"
    echo "            <tr><th>Module</th><th>Results</th><th>Status</th></tr>" >> "$REPORT_FILE"

    for module in $(echo "$CRITICAL_MODULES" | tr ',' ' '); do
        local result="${MODULE_RESULTS[$module]:-0/0}"
        local status_class="failed"
        local status_text="❌ FAILED"

        if [[ "$result" =~ ^([0-9]+)/([0-9]+)$ ]]; then
            local passed="${BASH_REMATCH[1]}"
            local total="${BASH_REMATCH[2]}"
            if [ "$passed" -eq "$total" ] && [ "$total" -gt 0 ]; then
                status_class="passed"
                status_text="✅ PASSED"
            fi
        fi

        echo "            <tr>" >> "$REPORT_FILE"
        echo "                <td>$(echo $module | tr '[:lower:]' '[:upper:]')</td>" >> "$REPORT_FILE"
        echo "                <td>$result</td>" >> "$REPORT_FILE"
        echo "                <td class=\"$status_class\">$status_text</td>" >> "$REPORT_FILE"
        echo "            </tr>" >> "$REPORT_FILE"
    done

    echo "        </table>" >> "$REPORT_FILE"
    echo "    </div>" >> "$REPORT_FILE"

    # Add detailed test results
    echo "    <div class=\"module\">" >> "$REPORT_FILE"
    echo "        <h2>🔍 Detailed Test Results</h2>" >> "$REPORT_FILE"
    echo "        <table>" >> "$REPORT_FILE"
    echo "            <tr><th>Test</th><th>Status</th></tr>" >> "$REPORT_FILE"

    for test_name in "${!TEST_RESULTS[@]}"; do
        local status="${TEST_RESULTS[$test_name]}"
        local status_class="failed"
        local status_icon="❌"

        if [ "$status" = "PASSED" ]; then
            status_class="passed"
            status_icon="✅"
        fi

        echo "            <tr>" >> "$REPORT_FILE"
        echo "                <td>$(echo $test_name | tr '_' ' ' | tr '[:lower:]' '[:upper:]')</td>" >> "$REPORT_FILE"
        echo "                <td class=\"$status_class\">$status_icon $status</td>" >> "$REPORT_FILE"
        echo "            </tr>" >> "$REPORT_FILE"
    done

    echo "        </table>" >> "$REPORT_FILE"
    echo "    </div>" >> "$REPORT_FILE"

    # Close HTML
    echo "</body></html>" >> "$REPORT_FILE"

    print_status "Validation report generated: $REPORT_FILE"
}

# Validate results against thresholds
validate_results() {
    print_info "🎯 Validating results against thresholds..."

    local validation_passed=true

    # Check failure threshold
    if [ "$FAILED_TESTS" -gt "$MAX_FAILURES" ]; then
        print_error "Failed tests ($FAILED_TESTS) exceed maximum allowed ($MAX_FAILURES)"
        validation_passed=false
    else
        print_status "Failed tests ($FAILED_TESTS) within acceptable limit ($MAX_FAILURES)"
    fi

    # Check coverage threshold
    if [ "$COVERAGE_ACTUAL" -lt "$COVERAGE_THRESHOLD" ]; then
        print_error "Coverage ($COVERAGE_ACTUAL%) below required threshold ($COVERAGE_THRESHOLD%)"
        validation_passed=false
    else
        print_status "Coverage ($COVERAGE_ACTUAL%) meets required threshold ($COVERAGE_THRESHOLD%)"
    fi

    # Check critical module results
    for module in $(echo "$CRITICAL_MODULES" | tr ',' ' '); do
        local result="${MODULE_RESULTS[$module]:-0/0}"
        if [[ "$result" =~ ^([0-9]+)/([0-9]+)$ ]]; then
            local passed="${BASH_REMATCH[1]}"
            local total="${BASH_REMATCH[2]}"
            if [ "$passed" -ne "$total" ]; then
                print_error "Critical module '$module' has failures ($result)"
                validation_passed=false
            else
                print_status "Critical module '$module' passed all tests ($result)"
            fi
        fi
    done

    return $([ "$validation_passed" = true ] && echo 0 || echo 1)
}

# Main execution function
main() {
    # Parse command line arguments
    parse_arguments "$@"

    # Initialize validation environment
    initialize_validation

    # Run validation for each critical module
    IFS=',' read -ra MODULES <<< "$CRITICAL_MODULES"
    for module in "${MODULES[@]}"; do
        case "$module" in
            "momo")
                validate_momo_api
                ;;
            "offline")
                validate_offline_sync
                ;;
            "notifications")
                validate_notifications
                ;;
            *)
                print_warning "Unknown module: $module"
                ;;
        esac
    done

    # Calculate coverage
    calculate_coverage

    # Generate report
    generate_report

    # Validate results
    if validate_results; then
        print_critical "🇿🇲 VALIDATION SUCCESSFUL - All critical systems operational"
        echo ""
        print_status "Summary:"
        print_status "  ✅ Tests Passed: $PASSED_TESTS/$TOTAL_TESTS"
        print_status "  📊 Coverage: $COVERAGE_ACTUAL% (required: $COVERAGE_THRESHOLD%)"
        print_status "  ❌ Failures: $FAILED_TESTS (max allowed: $MAX_FAILURES)"
        print_status "  📋 Report: $REPORT_FILE"
        echo ""
        print_info "🚀 Ready for production deployment!"
        exit 0
    else
        print_critical "🇿🇲 VALIDATION FAILED - Critical issues detected"
        echo ""
        print_error "Summary:"
        print_error "  ❌ Tests Failed: $FAILED_TESTS/$TOTAL_TESTS"
        print_error "  📊 Coverage: $COVERAGE_ACTUAL% (required: $COVERAGE_THRESHOLD%)"
        print_error "  📋 Report: $REPORT_FILE"
        echo ""
        print_error "🛑 DO NOT DEPLOY - Fix critical issues first!"
        exit 1
    fi
}

# Execute main function with all arguments
main "$@"
